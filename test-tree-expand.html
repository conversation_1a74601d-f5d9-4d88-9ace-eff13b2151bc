<!DOCTYPE html>
<html>
<head>
    <title>Tree Expand Test</title>
</head>
<body>
    <h1>Tree 展开功能测试</h1>
    <p>这个文件用于测试 Tree 组件的展开功能是否正常工作。</p>
    
    <h2>问题描述</h2>
    <p>原问题：展开下一级后，点击一次就点不了了，需要重新弹起窗口才能再次点击。</p>
    
    <h2>修复内容</h2>
    <ul>
        <li>添加了 expandedKeys 状态管理</li>
        <li>添加了 onExpand 事件处理器</li>
        <li>移除了固定的 defaultExpandedKeys={[]}</li>
        <li>使用 expandedKeys 和 onExpand 来正确管理展开状态</li>
    </ul>
    
    <h2>修复后的行为</h2>
    <p>现在树组件应该能够：</p>
    <ul>
        <li>正确记住哪些节点是展开的</li>
        <li>允许用户反复点击展开/收起按钮</li>
        <li>在组件重新渲染时保持展开状态</li>
    </ul>
</body>
</html>
