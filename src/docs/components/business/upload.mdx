# Upload 组件

`Upload` 组件的文档

## 基础用法

```js type=component file=@/stories/business/Upload/index.tsx

```

## 基础案例

```js type=case file=@/stories/business/Upload/examples/base.tsx

```

## 不同主题

```js type=case file=@/stories/business/Upload/examples/theme.tsx

```

## 上传数量限制

```js type=case file=@/stories/business/Upload/examples/maxCount.tsx

```

## 自定义附件列表容器

```js type=case file=@/stories/business/Upload/examples/attachmentContainerSelector.tsx

```