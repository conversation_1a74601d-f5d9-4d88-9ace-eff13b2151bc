# FormulaEditor 公式编辑器 组件

`FormulaEditor` 组件的文档

## 常规编辑器

```js type=case file=@/stories/business/FormulaEditor/examples/base.tsx

```

## 常规编辑器-带变量

```js type=case file=@/stories/business/FormulaEditor/examples/baseVariables.tsx

```

## 开发者模式

```js type=case file=@/stories/business/FormulaEditor/examples/develpoerModel.tsx

```

## 开发者模式Pro

```js type=case file=@/stories/business/FormulaEditor/examples/develpoerModelPro.tsx

```

## 无条件公式

```js type=case file=@/stories/business/FormulaEditor/examples/unconditionalFormulaEditor.tsx

```

## 公式变量面板

```js type=case file=@/stories/business/FormulaEditor/examples/formulaVariablesPanel.tsx

```

## 公式校验器

```js type=case file=@/stories/business/FormulaEditor/examples/formulaValidator.tsx

```

## API

```js type=component file=@/stories/business/FormulaEditor/formulaEditor.tsx

```
