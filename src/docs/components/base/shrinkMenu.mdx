# ShrinkMenu 收缩菜单 组件

`ShrinkMenu` 组件的文档

## 基础用法

```js type=component file=@/stories/base/ShrinkMenu/index.tsx

```

## 基础案例

```js type=case file=@/stories/base/ShrinkMenu/examples/base.tsx

```

## 自动收缩

通过 `autoShrinkWidth` 属性可自动实现, 当浏览器宽度小于或大于指定值时, 自动收缩和展开

```js type=case file=@/stories/base/ShrinkMenu/examples/autoShrinkWidth.tsx

```

## 右侧收缩

通过 `btnPosition` 设置left right

```js type=case file=@/stories/base/ShrinkMenu/examples/autoShrinkWidthRight.tsx

```