# Table 组件

这是 `Table` 组件的文档

## 基本用法

```js type=component file=@/stories/base/Table/index.tsx

```

## 基础案例

```js type=case file=@/stories/base/Table/examples/base.tsx

```

## 多层级

```js type=case file=@/stories/base/Table/examples/level.tsx

```

## 多层级占位演示

```js type=case file=@/stories/base/Table/examples/placeholder-demo.tsx

```

## 主题色

```js type=case file=@/stories/base/Table/examples/theme.tsx

```

## 筛选

```js type=case file=@/stories/base/Table/examples/filter.tsx

```

## 滚动条自动定位

```js type=case file=@/stories/base/Table/examples/autoScrollBar.tsx

```

## 排序

```js type=case file=@/stories/base/Table/examples/sort.tsx

```

## 选择

```js type=case file=@/stories/base/Table/examples/select.tsx

```

## 聚和

```js type=case file=@/stories/base/Table/examples/group.tsx

```

## 拖拽排序

```js type=case file=@/stories/base/Table/examples/move.tsx

```

## 虚拟列表

```js type=case file=@/stories/base/Table/examples/virtual.tsx

```