# TitaForm 表单 组件

`TitaForm` 组件的文档

## 基础用法

```js type=component file=@/stories/base/TitaForm/index.tsx

```

## 基础案例

```js type=case file=@/stories/base/TitaForm/examples/base.tsx

```

## 表单列表

```js type=case file=@/stories/base/TitaForm/examples/list.tsx

```

## 关联更新 props

```js type=case file=@/stories/base/TitaForm/examples/shouldUpdate.tsx

```

## 关联隐藏

```js type=case file=@/stories/base/TitaForm/examples/hidden.tsx

```

## 表单页签

```js type=case file=@/stories/base/TitaForm/examples/tabs.tsx

```