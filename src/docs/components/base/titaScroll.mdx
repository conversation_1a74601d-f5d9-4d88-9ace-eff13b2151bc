# TitaScroll 组件

`TitaScroll` 自己实现的滚动条，用于满足以下场景

- 滚动条样式自定义
- 拖动条展示在容器外围
- 自动显示阴影
- 支持滚动自动隐藏 & 常显
- 封装了一些常用事件
  - onScrollBottom
  - onScrollEnd

## 基础用法

```js type=component file=@/stories/base/TitaScroll/index.tsx

```

## 基础案例

```js type=case file=@/stories/base/TitaScroll/examples/base.tsx

```

## 实现 Popup 自动获取父容器

```js type=case file=@/stories/base/TitaScroll/examples/getPopupContainer.tsx

```

## 滚动到边界的事件

```js type=case file=@/stories/base/TitaScroll/examples/scrollBoundary.tsx focus=11:15

```

## 滚动条常显

```js type=case file=@/stories/base/TitaScroll/examples/disableAutoHide.tsx

```

## 白色阴影

```js type=case file=@/stories/base/TitaScroll/examples/shadowTheme.tsx

```

## 内置方法

```js type=case file=@/stories/base/TitaScroll/examples/ref.tsx

```

### 滚动到某个子元素的位置

```js type=case file=@/stories/base/TitaScroll/examples/scrollToChild.tsx

```

## 自定义滚动条样式

```js type=case file=@/stories/base/TitaScroll/examples/barStyle.tsx

```