import Empty from '@/stories/base/Empty'
import Tree from '@/stories/base/Tree'
import { getLocale } from '@tita/utils'
import React, { useEffect, useRef, useState, useCallback } from 'react'
import TreeNode from '../TreeNode'
import './index.scss'

// TODO: 需要完善类型
interface IProps {
  onSelect?: (node: unknown) => void
  selected?: unknown[]
  multiple?: boolean | unknown
  getTreeData?: () => {}
  loadNodes?: unknown
  expandedKeys?: (string | number)[]
  onExpand?: (keys: (string | number)[]) => void
  renderEmpty?: () => React.ReactNode
  [key: string]: unknown
}

export default function (props: IProps) {
  const {
    onSelect,
    selected = [],
    multiple,
    getTreeData,
    loadNodes,
    expandedKeys: propsExpandedKeys,
    onExpand: propsOnExpand,
    renderEmpty = () => <Empty>{getLocale('OKR_MyO_Text_Noclassifica')}</Empty>,
    ...restProps
  } = props

  const loadedNode = useRef([])
  const [loading, setLoading] = useState<boolean>(true)
  const [treeData, setTreeData] = useState<any>(restProps?.treeData)
  const [rawTreeData, setRawTreeData] = useState<any>()

  useEffect(() => {
    setTreeData(restProps?.treeData)
  }, [restProps?.treeData])

  // 使用传入的展开状态，如果没有传入则使用内部状态
  const [internalExpandedKeys, setInternalExpandedKeys] = useState<(string | number)[]>([])
  const expandedKeys = propsExpandedKeys || internalExpandedKeys

  // 处理展开状态变化
  const onExpandHandler = useCallback((keys: (string | number)[]) => {
    if (propsOnExpand) {
      // 如果有外部的 onExpand，调用它
      propsOnExpand(keys)
    } else {
      // 否则更新内部状态
      setInternalExpandedKeys(keys)
    }
  }, [propsOnExpand])

  const titleRenderHandler = (node: any) => {
    return <TreeNode {...node} isChecked={selected.includes(node.key)} />
  }

  const onSelectHandler = (
    selectedKeys: (string | number)[],
    info: { node: any; selected: boolean }
  ) => {
    onSelect && onSelect(info?.node)
  }

  useEffect(() => {
    // restProps?.treeData.
  }, [selected])
  const selectedKeys = selected.map((it: any) => it.key)

  // if (loading) return <Loading />;

  if (!treeData || !treeData.length) {
    return renderEmpty()
  }

  return (
    <div className='perspm-tree'>
      <Tree
        // {...restProps}
        key={'key'}
        // multiple={true}
        // @ts-ignore
        selectNodeHighlight={false}
        expandedKeys={expandedKeys}
        onExpand={onExpandHandler}
        selectedKeys={selectedKeys}
        treeData={treeData}
        titleRender={titleRenderHandler}
        onSelect={onSelectHandler}
        // beforeSelect={true}
      />
    </div>
  )
}
