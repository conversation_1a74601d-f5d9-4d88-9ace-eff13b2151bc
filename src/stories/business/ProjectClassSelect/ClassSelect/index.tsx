// @ts-nocheck
import React, {
  FC,
  ReactElement,
  forwardRef,
  useContext,
  useEffect,
  useState,
  useImperativeHandle,
  useCallback,
} from 'react'

import Button from '@/stories/base/Button'
import Popup from '@/stories/base/Popup'
import Search from '@/stories/base/Search'
import SelectTree from '../SelectTree'
import TreeNode from '../TreeNode'
import { CommonContext } from '../context'

import { getBSGlobal, getLocale, getUserInfo } from '@tita/utils'
import { getLabels, labelSearch } from '../apis/label'
import { labelToTree } from '../utils'
import './index.scss'

const preCls = 'tita-ui--class-select'

interface ClassSelectorProps {
  multiple?: boolean
  trigger?: any
  children?: ReactElement
  selectNodes?: any[]
  // onClose?: (node: any) => void
  onSelect?: Function
  onOk?: Function
  onCancel?: Function
  popupAlign?: {
    offset: [number, number]
    overflow?: { adjustX: boolean; adjustY: boolean }
    targetOffset?: [number, number]
  }
  popClass?: any
  popupPlacement?: string
  onPopupVisibleChange?: Function
  searchWords?: string
  getPopupContainer?: () => HTMLElement
  autoClear?: boolean
  // onSearch?: () => void
  disabled?:boolean
}

const apiServer = getBSGlobal('apiPath')
const userId = getUserInfo()?.Id

const ClassSelect: FC<ClassSelectorProps> = forwardRef((props, ref) => {
  const {
    multiple,
    children,
    // onClose,
    trigger,
    onOk,
    onCancel,
    selectNodes = [],
    popClass,
    popupPlacement = 'bottomLeft',
    getPopupContainer,
    onPopupVisibleChange,
    disabled = false,
  } = props
  const [visible, setVisible] = useState<boolean>(false)
  // @ts-ignore
  const {
    classification,
    addClassification,
    onChange,
    setClassification,
    setMemoClassifycation,
    allData,
    setAllData,
  } = useContext(CommonContext)
  const [treeData, setTreeData] = useState()
  // 添加展开状态管理
  const [expandedKeys, setExpandedKeys] = useState<(string | number)[]>([])

  const init = () => {
    getLabels().then((res) => {
      console.log('==============================')
      const labels = res.Data.lables.filter(({ IsTenant }) => IsTenant)
      setAllData(labels)
      console.log('=======', labelToTree(labels))
      setTreeData(labelToTree(labels))
    })
  }

  useEffect(() => {
    if (visible || allData.length === 0) init()
  }, [visible])

  const submit = () => {
    setVisible(false)
    onChange(classification)
    setMemoClassifycation(classification)
  }

  const onSearch = (value) => {
    if (value !== '') {
      labelSearch(value).then((res) => {
        const labels = res.Data.lables.filter(({ IsTenant }) => IsTenant)
        setAllData(labels)
        setTreeData(labelToTree(labels))
        // 搜索时重置展开状态
        setExpandedKeys([])
      })
    } else {
      init()
      // 重置搜索时也重置展开状态
      setExpandedKeys([])
    }
  }

  // 处理展开状态变化
  const onExpandHandler = useCallback((keys: (string | number)[]) => {
    setExpandedKeys(keys)
  }, [])

  const onClose = (value: string) => {
    const _classification = [...classification]

    const index = _classification.findIndex((item) => item === value)
    if (index !== -1) {
      _classification.splice(index, 1)
    }
    setClassification(_classification)
  }

  const popup = () => {
    return (
      <div className={`${preCls}_box`}>
        <div className={`${preCls}_box-main`}>
          <div className={`${preCls}_box-main-left`}>
            <div className={`${preCls}_box-main-left-head`}>
              <Search style={{ width: '100%' }} disableSearchResult onSearch={onSearch} />
            </div>
            <div className={`${preCls}_box-main-left-tree`}>
              <SelectTree
                multiple={multiple}
                treeData={treeData}
                selected={classification}
                onSelect={addClassification}
                expandedKeys={expandedKeys}
                onExpand={onExpandHandler}
              />
            </div>
          </div>
          {multiple && (
            <div className={`${preCls}_box-main-right`}>
              <div className={`${preCls}_box-main-right-head`}>
                <div className={`${preCls}_box-main-right-head-total`}>
                  已选：分类{(classification as unknown[])?.length}
                </div>
                <span
                  className={`${preCls}_box-main-right-head-clear cursor-pointer`}
                  onClick={() => setClassification([])}
                >
                  {getLocale('Mod_Empty')}
                </span>
              </div>
              <div className={`${preCls}_box-main-right-list`}>
                {(classification as unknown[])?.map((item: any) => {
                  const label = allData.find(({ id }) => id === item)
                  return (
                    <TreeNode
                      key={item}
                      onClose={() => onClose(item)}
                      inTree={false}
                      title={label?.name}
                    />
                  )
                })}
              </div>
            </div>
          )}
        </div>
        <div className={`${preCls}_box-foot`}>
          <div>
            <Button
              type='link'
              href={`${apiServer}/u/${userId}/Home#/set?page_id=class`}
            >
              {getLocale('Tasks_Menu_Category')}
            </Button>
          </div>
          <div>
            <Button
              onClick={() => setVisible(false)}
              size='small'
              type='border'
            >
              {getLocale('Mod_Cancel')}
            </Button>
            <Button onClick={submit} className='ml-3' size='small' primary>
              {getLocale('OKR_MyO_Butt_Determine')}
            </Button>
          </div>
        </div>
      </div>
    )
  }

  useImperativeHandle(ref, () => ({
    onclose: () => setVisible(false),
    open: () => setVisible(true),
  }))

  return (
    <div className={preCls}>
      <Popup
        popup={popup}
        clearStyle
        popupVisible={visible}
        extraClass={popClass}
        getPopupContainer={getPopupContainer}
        onPopupVisibleChange={(visible: boolean) => {
          setVisible(visible && !disabled)
          onPopupVisibleChange?.(visible)
        }}
        popupPlacement={popupPlacement}
      >
        {children && (
          <span className='w-full' onClick={() => setVisible(!visible)}>
            {children && React.Children.map(children, child => {
              if (React.isValidElement(child)) {
                return React.cloneElement(child as React.ReactElement, { popupVisible: visible, ...child.props || {} });
              }
              return child;
            })}
          </span>
        )}
      </Popup>
    </div>
  )
})
export default React.memo(ClassSelect)
