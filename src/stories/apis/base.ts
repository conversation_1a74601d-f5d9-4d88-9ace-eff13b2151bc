import { Department } from "@tita/model";
import { isTestEnv } from "@tita/utils";
import { rget, rpost } from "@titaui/request";
import { IAxiosResponseType } from "@titaui/request/lib/axios";
import axios from "axios";

const instance = axios.create({
  baseURL: isTestEnv() ? "https://cloud.tita.work" : "https://cloud.tita.com",
  withCredentials: true,
});

/**
 * 查询量化单位
 */
export const getUnits = () => {
  return rget<{ id: number, name: string }[]>("v2")(`quantify/getByUser?objType=7&objId=&parentObjId=`);
};
// quantify/getByUser?objType=7&objId=&parentObjId=614169_24873705_7_20_638194335633915177

/**
 * AI点赞
 * @returns
 */
export const likeed = (recoredId: string, type: number) => {
  return instance
    .post(`/ai/gpt/likeOrDislike?recoredId=${recoredId}&type=${type}`)
    .then((res: any) => res?.data || []);
};

/**
 * AI踩
 * @param params
 * @returns
 */
export const feedback = (promptId: string, params) => {
  return instance
    .post(`/ai/gpt/feedback`, {
      ...params,
      promptId,
    })
    .then((res: any) => res?.data || []);
};