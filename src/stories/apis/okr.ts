import skynetFetch from "@titaui/axios-auth-request";
import { PushScreenType, TableData } from '../modals/modals/okr/type'

export const getData = (
  tenantId: number,
  departId: number,
  type: PushScreenType,
  currentPage: number,
  pageSize: number,
  cycle: string
) => {
  return skynetFetch().post(
    "/tita-api/v1/feed/okr_evolve_week",
    {
      tenantId,
      departId,
      type,
      currentPage,
      pageSize,
      cycle
    }
  );
}

export const okrPushScreenNotice = (
  departId: number,
  type: PushScreenType,
  cycle: string,
  isAll: boolean,
  selectIds: Array<number>,
  cancelIds: Array<number>,
  content: string
) => {
  return skynetFetch().post(
    "/tita-api/v1/feed/message_notice",
    {
      departId,
      type,
      cycle,
      isAll,
      selectIds,
      cancelIds,
      content
    }
  );
}