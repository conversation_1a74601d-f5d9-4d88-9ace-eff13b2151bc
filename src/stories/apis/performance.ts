import { rform, rget, rpost } from "@titaui/request";

export interface IndicatorLibraryClass {
  id:          number;
  name:        string;
}
/** 查询考核指标分类 */
export const getIndicatorLibraryClass = () => {
  return rget<{
    total: number
    data: IndicatorLibraryClass[]
  }>("v2")(`performance/normCategory/getlist`);
}
/** 考核指标 */
export interface PerformanceIndicator {
  id: string
  tenantID: number
  categoryID: number
  categoryName: string
  normType: number
  itemName: string
  itemDescription: string
  evaluateStandard: string
  scoreUpperLimit: null
  targetValue: string
  userID: number
  userName: string
  scoreLowerLimit: null
  scoreType: string
  formulaSetting: FormulaSetting
}

export interface FormulaSetting {
  scoreFormulaStyle: null
  scoreFormula: null
  scoreFormulaType: null
  mutilSettings: null
}
/** 查询考核指标 */
export const getIndicatorLibrary = (params: {
  pageIndex: number,
  pageSize: number,
  name: string,
  normTypeIDs: number[],
  normCategoryIDs: number[],
  userIds: number[],
}) => {
  return rpost<{
    total: number
    data: PerformanceIndicator[]
  }>("v2")(`performance/normLibrary/seach`, params);
}
/** 查询经营指标库 */
export const getTargetnormlibrary = (params: {
  pageIndex: number,
  pageSize: number,
  keyWord: string,
}) => {
  return rpost<{
    total: number
    data: PerformanceIndicator[]
  }>("v3")(`performance/targetnormlibrary/list`, params);
}

export const formulaSyntaxCheck = (params: {
  formula: string,
  variables: { [key: string]: number }
  operands: {
    type: string,
    value: string,
    tokenValue: string,
    start: number,
    end: number,
    key: string,
    name: string
  }[]
}) => {
  return rpost("v3")(`performance/ai/ali/formulaSyntaxCheck`, params);
}
