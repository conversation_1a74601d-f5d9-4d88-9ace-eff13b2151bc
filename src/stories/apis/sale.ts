import { rget, rpost } from '@titaui/request'
// @ts-ignore
import { TargetPlan, ISaleUserAuthority, QuantifyModel, TargetType, Target } from '@tita/model'
import { CreateTargetModal } from '../modals/modals/sale/targetExcel'
import { IAxiosResponseType } from '@titaui/request/lib/axios'

interface ITargetplanCreateParams {
  name?: string
  cycle?: number
  yearNum?: number
  yqmNum?: number
  startDate?: string
  endDate?: string
}
/**
 *
 * @param userId
 * @returns
 */
export const createSaleOPlain = (data: {
  name?: string
  cycle?: number
  yearNum?: number
  yqmNum?: number
  startDate?: string
  endDate?: string
}) => {
  return rpost<TargetPlan>('v1')(`mbo/targetplan/create`, data)
}

// 编辑计划
// /mbo/targetplan/updatename
export const editSaleOPlain = (params: {
  planId: string
  planName: string
}) => {
  return rpost<TargetPlan>(
    'v1'
  )(
    `mbo/targetplan/updatename?planId=${params.planId}&planname=${params.planName}`
  )
}

/**
 * 查询目标列表
 */
export const searchTarget = (params: {
  planId?: number
  targetPlanParticipantId?: number
  pageNum?: number
  pageSize?: number
}) => {
  return rpost<TargetPlan>('v1')(
    `mbo/target/getByParticipantId?planId=${params.planId}&targetPlanParticipantId=${params.targetPlanParticipantId}`,
    {
      targetPlanId: params?.planId,
      targetPlanParticipantId: params?.targetPlanParticipantId,
      pageNum: params?.pageNum || 1,
      pageSize: params?.pageSize || 50
    }
  )
}

/**
 * 查询子目标列表
 */
export const searchChildTarget = (params: { targetId?: number }) => {
  return rget<TargetPlan>(
    'v1',
  )(`mbo/target/getByParenId?targetId=${params.targetId}`)
}

/**
 * 批量创建目标
 */
export interface ICreateTargetParams {
  planId: number
  /** 0 保存 1 保存并提交审批 3 驳回 4 通过 */
  status?: 0 | 1 | 3 | 4
  planParticipantId: number
  deleteTargetIds?: number[]
  targetModels?: TargetPlan[]
}
export const createTargets = (params: ICreateTargetParams) => {
  return rpost<TargetPlan>('v1')(`mbo/target/create`, params)
}
export const createSplitTargets = ({
  planId,
  parentTargetId,
  targets,
}: {
  planId: number
  parentTargetId: number
  targets: TargetPlan[]
}) => {
  return rpost<TargetPlan>('v1')(
    `mbo/target/split?planId=${planId}&parentTargetId=${parentTargetId}`,
    targets
  )
}
/**
 * 提交审批
 */
export const submitApproval = (params: ICreateTargetParams) => {
  return rpost<TargetPlan>('v1')(
    `mbo/target/create?status=1`,
    params
  )
}

/**
 * 删除目标
 */
export const deleteTarget = (targetId: number) => {
  return rpost<TargetPlan>('v1')(
    `mbo/target/delete?targetId=${targetId}`,
    {
      targetId,
    }
  )
}

/**
 * 审批
 */
export const approval = (params: ICreateTargetParams) => {
  return rpost<TargetPlan[]>('v1')(`mbo/target/approval`, params)
}

export interface Quantify {
  id: number
  tenantId: number
  name: string
  code: string
  sort: number
  isShow: number
  createTime: string
  modifyTime: string
  isDelete: boolean
}

/**
 * 获取初始值、目标值等字段信息
 */
export const getUnitColumns = () => {
  return rget<Quantify[]>('v1')(`mbo/target/quantifyField`)
}

// 判断是否可以创建 列表顶部黄条数据
export interface IGetAuthorityParams {
  planId?: number | string
  todepId?: string | number
  toUserId?: string | number
  relationId?: string
}
export const getAuthority = (params: IGetAuthorityParams) => {
  let url = `mbo/targetplan/userauthority?planId=${params.planId}`
  params?.todepId && (url = url + `&todepId=${params.todepId}`)
  params?.toUserId && (url = url + `&toUserId=${params.toUserId}`)
  params?.relationId && (url = url + `&relationId=${params.relationId}`)

  return rget<ISaleUserAuthority>('v1')(url)
}

// 更新目标量化值
export interface ITargetUpdateParams {
  targetId?: number
  progressDescription?: string
  fileUrls?: string[]
  quantifyModels?: Array<QuantifyModel | undefined>
}
export const updateTargetProgressApi = (params: ITargetUpdateParams) => {
  return rpost<TargetPlan[]>('v1')(
    `mbo/target/progress/update`,
    params
  )
}

// 获取目标计划详情
export const getPlanDetail = (
  planId: string
): Promise<IAxiosResponseType<TargetPlan>> => {
  return rpost<TargetPlan>(
    'v1'
  )(`mbo/targetplan/detail?planId=${planId}`)
}

// 获取对齐目标列表
export interface ITargetListParams {
  targetPlanId?: number
  relation?: string
  pageNum?: number
  pageSize?: number
  targetTypes?: string
  status?: number
  keyWords?: string
  isGetDocument?: boolean
  userIds?: string
  departmentIds?: string
  toUserId?: number
  isGetVisibilityScope?: boolean // 是否获取可见范围
  isGetSubQuantifyValue?: boolean // 是否获取子目标量化值
}
export const getAlignTargetsApi = (
  params: ITargetListParams
): Promise<
  IAxiosResponseType<{
    total: number
    targets: Target[]
  }>
> => {
  return rpost<{
    total: number
    targets: Target[]
  }>('v1')(`mbo/target/search`, params)
}

// 获取目标列表进度信息
export const getTargetProgressRecordApi = (params: {
  targetId: number
  pageNum?: number
  pageSize?: number
}) => {
  return rget(
    'v1'
  )(
    `mbo/target/progress/list?targetId=${params?.targetId}&pageNum=${params.pageNum}&pageSize=${params.pageSize}`
  )
}

// 获取目标动态
export const getTargetFeeds = ({
  targetId,
  pageSize,
  pageNum,
  dataType = 0,
  feedType = 21
}: {
  targetId: number | string
  pageSize: number
  pageNum: number
  /** -1 -  全部动态 ｜ 0 - 本目标动态 | 1 - 一级子目标动态 */
  dataType: -1 | 0 | 1
  /** 21 - 进展 */
  feedType?: number
}) => {
  return rget('v2')(
    `targetfeed/Get?targetId=${targetId}&pageSize=${pageSize}&commentCount=4&pageNum=${pageNum}&dataType=${dataType}&feedType=${feedType}`
  )
}

// 查询目标地图数据
export const getTargetMap = (
  data: {
    targetPlanId: number;
    targetTypes: string;
    childRoot: boolean;
    level: number;
    objectId?: number;
    isManager?: boolean;
    userIds?: string;
    departmentIds?: string;
    isGetVisibilityScope?: boolean;
    isGetDocument?: boolean;
    keyWords?: string;
    targetPlanParticipantId?: number;
    relation?: string;
    pageNum?: number;
    pageSize?: number;
  }
): Promise<IAxiosResponseType<TargetPlan>> => {
  return rpost<TargetPlan>(
    'v1'
  )(`mbo/target/map/generalview`, data);
};

// 查询目标地图数据
export const getChildTargetMap = (
  data: {
    targetPlanId: number;
    targetTypes: string;
    childRoot: boolean;
    level: number;
    objectId?: number;
    isManager?: boolean;
    userIds?: string;
    departmentIds?: string;
    isGetVisibilityScope?: boolean;
    isGetDocument?: boolean;
    keyWords?: string;
    targetPlanParticipantId?: number;
    relation?: string;
    pageNum?: number;
    pageSize?: number;
  }
): Promise<IAxiosResponseType<TargetPlan>> => {
  return rpost<TargetPlan>(
    'v1'
  )(`mbo/target/map/expand`, data);
};

// 查询目标时间计划
export const searchTimePlanning = (targetId: number) => {
  return rget<TargetPlan>(
    'v1'
  )(`mbo/target/quantifyplan/get?targetId=${targetId}`);
};


export enum CycleType {
  Quarter = 2,
  Month = 3
}

export type QuantifyPlanModel = QuantifyModel & {
  quantifyFieldName: string
  subPlanQuantifyValues: (QuantifyModel & { yqmNum: number, quantifyFieldName: string })[]
}
/**
 * 保存目标时间计划
 */
export const saveTimePlanning = (data: {
  targetId: number
  cycleType: CycleType
  quantifyModels: QuantifyPlanModel[]
}) => {
  return rpost<TargetPlan>(
    'v1'
  )(`mbo/target/quantifyplan/save`, data);
};
/**
 * 获取指标详情
 */
export const getNormApp = (normId: string) => {
  return rget(
    'v1'
  )(`mbo/target/norm?normId=${normId}`);
};