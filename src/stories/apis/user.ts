import { Department, EmployeeMember, User } from '@tita/model'
import { getUserInfo } from '@tita/utils'
import { rget, rpost } from '@titaui/request'
import { IAxiosResponseType } from '@titaui/request/lib/axios'

export const getSubordinate = (pid?: number) => {
  if (!pid) pid = getUserInfo()?.Id

  return rget<{
    relationUsers: {
      subordinate: User[]
      dottedSubordinate: User[]
    }
  }>('v2')(`user/getRelationUsers?relationIds=1,2&toUserId=${pid}`)
}

// 获取企业下人员数据
export interface IStaffListParams {
  pageIndex: number
  pageSize: number
  /** 在职 - 1 */
  staffStatus?: number
}
export const getStaff = (params: IStaffListParams) => {
  return rpost<{
    list: User[]
    total: number
  }>('v5')(`staff/search`, {
    staffStatus: 1,
    ...params,
  })
}

// 获取目标计划下人员
export interface IStaffListParams {
  planId: number
  depId: string | number
  userIds: number[]
  parentId: number
  status: number | string
  pageNum: number
  pageSize: number
  /** 是否为拆解调用 */
  isDisassembly?: boolean
  depIds?: string[]
}
export const getPlanUserList = (
  params: Partial<IStaffListParams>
): Promise<IAxiosResponseType<EmployeeMember[]>> => {
  return rpost<EmployeeMember[]>('v1')(
    `mbo/targetplan/user/list`,
    {
      ...params,
    }
  )
}
