import { DepMember, Department, ObjType } from "@tita/model";
import { rget, rpost } from "@titaui/request";
import { IAxiosResponseType } from "@titaui/request/lib/axios";

/**
 * 
 * @param userId 
 * @returns 
 */
export const getDepartments = (data: { pageNum?: number, pageSize?: number, depIds: number[], levels: number[], topDepIds: number[], objType:ObjType, IsGetOrgLeader?: boolean }) => {
  return rpost<{
    list: Department[]
    total: number
  }>("v2")(`department/sub/departmentTile`, {
    ...data,
  });
};

export const getMyDepartment = (parentId?: number) => {
  if (parentId) {
    return rget<{
      departments: Department[]
    }>("v2")(`department/sub/depsAndUsers?departmentId=${parentId}&isGetUser=false&objType=93`);
  }
  return rget<{
    departments: Department[]
  }>("v2")(`department/sub/depsAndUsersByPrivilege?objType=93&isGetUser=false`);
}

// 获取计划下部门数据
export interface IDeptListParams {
  planId: number
  depId: number
  levels: number[]
  depIds: Array<string | number>
  topDepIds: Array<string | number>
  status: number
  pageNum: number
  pageSize: number
  /** 是否为拆解调用 */
  isDisassembly?: boolean
}
export const getPlanDeptList = (
  params: Partial<IDeptListParams>
): Promise<IAxiosResponseType<DepMember[]>> => {
  return rpost<DepMember[]>('v1')(`mbo/targetplan/dept/list`, {
    ...params,
  });
};