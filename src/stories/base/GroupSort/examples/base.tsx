import React, { FC, useState } from 'react'
import GroupSort, { GroupSortItem } from '@/stories/base/GroupSort'
import { getRandomStr } from '@tita/utils'
import cloneDeep from 'lodash/cloneDeep'
import { message } from 'antd'

export interface IBaseProps {}

const mockData: GroupSortItem[] = [
  {
    key: 'group1',
    title: '分组1',
    rows: [
      {
        key: '1',
        title: '分组1-1',
        data: {},
      },
      {
        key: '2',
        title: '分组1-2',
        data: {},
      },
      {
        key: '3',
        title: '分组1-3',
        data: {},
      },
    ],
  },
  {
    key: 'group2',
    title: '分组3',
    rows: [
      {
        key: '4',
        title: '分组2-1',
        data: {},
      },
      {
        key: '5',
        title: '分组2-2',
        data: {},
      },
      {
        key: '6',
        title: '分组2-3',
        data: {},
      },
    ],
  },
]

const Base: FC<IBaseProps> = ({}) => {
  const [items, setItems] = useState(mockData)
  return (
    <GroupSort
      items={items}
      onChange={setItems}
      onAdd={(group) => {
        const index = items.findIndex((item) => item.key === group.key)
        const key = getRandomStr()
        items[index].rows = [
          ...items[index].rows,
          {
            key,
            title: `新增 ${key}`,
          },
        ]
        setItems(cloneDeep(items))
      }}
      onClickItem={({ data }) => message.success(data.key)}
    />
  )
}

export default React.memo(Base)
