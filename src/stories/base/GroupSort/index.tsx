import React, { FC, useCallback, useMemo, useState } from 'react'
import classNames from 'classnames'
import './index.scss'
import Board, { BoardItem, OnDragEnd, RenderColumn, RenderRow, RenderRows } from '../Board'
import { useUpdateEffect } from 'ahooks'
import Button from '../Button'

export interface RowItem<Data = any> {
  key: string
  title: string
  data?: Data
}
export interface GroupSortItem {
  key: string
  title: string
  rows: RowItem[]
}

export interface IGroupSortProps<Data = any> {
  items: GroupSortItem[]
  renderRow?: (rowItem: RowItem, group: GroupSortItem) => React.ReactNode
  onAdd?: (group: GroupSortItem) => void
  onClickItem?: (event: {
    groupKey: string
    rowKey: string
    data: Data
  }) => void
  onChange?: (
    items: GroupSortItem[],
    event: {
      currentTarget: RowItem
      oldPosition: {
        groupKey: string
        rowIdx: number
      }
      newPosition: {
        groupKey: string
        rowIdx: number
      }
    }
  ) => void
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-group-sort'

export const GroupSort: FC<IGroupSortProps> = React.memo(
  ({ items, onAdd, onClickItem, onChange, className, style }) => {
    const [boardData, setBoardData] = useState(items)

    const onDragEndHandler: OnDragEnd = useCallback((newBoards, result) => {
      const { moveItem, sourceBoard, destinationBoard, source, destination } = result
      setBoardData(newBoards as GroupSortItem[])
      onChange?.(newBoards as GroupSortItem[], {
        currentTarget: moveItem as RowItem<any>,
        oldPosition: {
          groupKey: sourceBoard.key,
          rowIdx: source.index,
        },
        newPosition: {
          groupKey: destinationBoard.key,
          rowIdx: destination?.index || 0,
        }
      })
    }, [])

    useUpdateEffect(() => {
      setBoardData(items)
    }, [items])

    const renderColumn: RenderColumn<{
      key: string
      title: string
    }> = useCallback(({ data, renderRows }) => {
      return (
        <div className='p-10px shadow-lg rounded-md w-full'>
          <h3>{data.title}</h3>
          <div className='mt-10px'>{renderRows({ height: 400 })}</div>
          <Button
            onClick={() => onAdd?.(data as unknown as GroupSortItem)}
            primary
            className='w-full'
          >
            添加
          </Button>
        </div>
      )
    }, [])

    const renderRow: RenderRow<{
      key: string
      title: string
    }> = useCallback(({ data, board }) => {
      return (
        <div
          className='pb-10px'
          onClick={() =>
            onClickItem?.({
              groupKey: board.key,
              rowKey: data.key,
              data,
            })
          }
        >
          <div className='border rounded-lg p-10px bg-white'>
            {data.title}
          </div>
        </div>
      )
    }, [])

    if (!boardData) return <></>
    return (
      <Board
        className="space-x-12px"
        boards={boardData}
        onDragEnd={onDragEndHandler}
        columnKey='key'
        rowKey='key'
        renderColumn={renderColumn}
        renderRow={renderRow}
      />
    )
  }
)

export default GroupSort
