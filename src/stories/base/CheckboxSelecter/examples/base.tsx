import React, { FC, useState } from 'react'
import CheckboxSelecter from '@/stories/base/CheckboxSelecter'
import { CheckBoxItem } from '../../PopupCheckBox'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  const [menuData, setMenuData] = useState<CheckBoxItem[]>([
    { label: '🍎 苹果', value: 1 },
    { label: '🍌 香蕉', value: 2 },
    { label: '🍌 香蕉1', value: 3 },
    { label: '🍌 香蕉2', value: 4 },
    { label: '🍌 香蕉3', value: 5 },
    { label: '全选', value: 0, isAll: true },
  ])
  return <CheckboxSelecter menuData={menuData} />
}

export default React.memo(Base)
