import React, { FC, useCallback, useMemo, useState } from 'react'
import classNames from 'classnames'
import './index.scss'
import { deepMap } from '@tita/utils'
import PopupCheckBox, { IPopupCheckBoxProps } from '../PopupCheckBox'
import SelectView, { ISelectViewProps } from '../SelectView'
import TagsView, { TagItem } from '../TagsView'
import { useUpdateEffect } from 'ahooks'

type Key = string | number

// @ts-ignore
export interface ICheckboxSelecterProps<Multiple extends boolean = false | true> extends Omit<IPopupCheckBoxProps, 'mode'>, Omit<ISelectViewProps , 'onChange' | 'value'> {
  multiple?: Multiple
  value?: Multiple extends true ? Key[] : Key
  initialValue?: Multiple extends true ? Key[] : Key
  onChange?: (value: Multiple extends true ? Key[] : Key) => void
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-checkbox-selecter'

export const CheckboxSelecter: FC<ICheckboxSelecterProps> = React.memo(({ multiple = true, menuData, value: propsValue, placeholder, mode, type, onChange, className, style, ...other }) => {
  const [value, setValue] = useState(propsValue)
  useUpdateEffect(() => {
    setValue(propsValue)
  }, [propsValue])

  const _onChange = (value: Key | Key[]) => {
    setValue(value)
    onChange?.(value)
  }

  const viewTags = useMemo(() => {
    if (!menuData || !value) return []

    const selectItems: TagItem[] = []
    menuData.filter((item) => Array.isArray(value) ? value.includes(item.value) : item.value === value).forEach((item) => {
      selectItems.push({
        label: item.label,
        value: item.value
      })
    })
    return selectItems
  }, [menuData, value])

  const onDeleteUserHandler = useCallback((key: number) => {
    if (!value || !Array.isArray(value)) return

    const newValue = value.filter(valueItem => valueItem !== key)
    onChange?.(newValue)
    setValue(newValue)
  }, [value, onChange])

  return (
    <PopupCheckBox
      // @ts-ignore
      value={value}
      menuData={menuData}
      onChange={_onChange}
      {...other}
    >
      {multiple ? (
        <TagsView
          tags={viewTags}
          mode={mode}
          type={type}
          multiple
          className={className}
          style={style}
          placeholder={placeholder}
          // @ts-ignore
          onDelete={onDeleteUserHandler}
          closePlacement="back"
        />
      ) : (
        <SelectView
          placeholder={placeholder}
          mode={mode}
          type={type}
          style={style}
          className={className}>
          {viewTags[0]?.label}
        </SelectView>
      )}
    </PopupCheckBox>
  )
})

export default CheckboxSelecter
