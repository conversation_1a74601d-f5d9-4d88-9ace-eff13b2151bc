import React, { FC, MutableRefObject, forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react'
import classNames from 'classnames'
import { useSize, useUpdateEffect } from 'ahooks'
import { useHistory } from '@/hooks/useHistory'
import { useRefState } from '@tita/hooks'
import './index.scss'

export interface IFlippingProps {
  views: React.ReactNode[]
  current?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

export interface IFlippingRef {
  push: (view: React.ReactNode) => void
  pop: () => void
}

const preCls = 'tita-ui-flipping'

export const Flipping = React.memo(forwardRef<IFlippingRef, IFlippingProps>(({ views, current, className, style }, ref) => {
  
  const viewHistory = useHistory(views?.map((view, i) => ({ key: i, props: { view } })))
  const viewHistoryRef = useRef(viewHistory)
  viewHistoryRef.current = viewHistory

  useUpdateEffect(() => {
    viewHistoryRef.current.set(views?.map((view, i) => ({ key: i, props: { view } })))
    if (isFront) {
      setFace1(views[views.length - 1])
    } else {
      setFace2(views[views.length - 1])
    }
  }, [views])

  const [face1, setFace1] = useState<React.ReactNode>(viewHistory.current?.props?.view)
  const [face2, setFace2] = useState<React.ReactNode>()

  const face1Ref = useRef() as MutableRefObject<HTMLDivElement>
  const face2Ref = useRef() as MutableRefObject<HTMLDivElement>

  const face1Size = useSize(face1Ref) || { width: 0, height: 0}
  const face2Size = useSize(face2Ref) || { width: 0, height: 0}

  const [rotate, rotateRef, setRotate] = useRefState(0)
  const isFront = !(rotate % 2)
  const next = useCallback(() => {
    setRotate(rotateRef.current + 1)
  }, [])
  const prev = useCallback(() => {
    setRotate(rotateRef.current - 1)
  }, [])

  useImperativeHandle(ref, () => ({
    push: (view: React.ReactNode) => {
      if (isFront) setFace2(view)
      else setFace1(view)

      viewHistoryRef.current.push((viewHistoryRef.current.current?.key as number) + 1, { view })
      next()
    },
    pop: () => {
      if (isFront) setFace2(viewHistoryRef.current.back.props?.view)
      else setFace1(viewHistoryRef.current.back.props?.view)

      prev()
      viewHistoryRef.current.pop()
    },
  }))

  return (
    <div className={classNames(preCls, className)} style={style}>
      <div className={`${preCls}__container`} style={{
        width: isFront ? face1Size.width : face2Size.width,
        height: isFront ? face1Size.height : face2Size.height,
        transform: `rotateY(${rotate * 180}deg)`
      }}>
        <div className={classNames(`${preCls}__face ${preCls}__face1`, {
          [`${preCls}__face--hide`]: !isFront
        })} ref={face1Ref}>{face1}</div>
        <div className={classNames(`${preCls}__face ${preCls}__face2`, {
          [`${preCls}__face--hide`]: isFront
        })} ref={face2Ref}>{face2}</div>
      </div>
    </div>
  )
}))

export default Flipping
