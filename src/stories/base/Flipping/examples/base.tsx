import React, { FC, MutableRefObject, useRef } from 'react'
import Flipping, { IFlippingRef } from '@/stories/base/Flipping'
import Button from '@/stories/base/Button'

export interface IBaseProps { }

const views = [
  <div className="w-20 h-32 shadow-sm rounded-sm border bg-white">1</div>,
  <div className="w-20 h-32 shadow-sm rounded-sm border bg-white">2</div>,
]
let index = 3
const Base: FC<IBaseProps> = ({ }) => {
  const flippingRef = useRef() as MutableRefObject<IFlippingRef>
  return (
    <div>
      <Flipping ref={flippingRef} views={views} />
      <Button onClick={() => flippingRef.current.push(<div className="w-20 h-32 shadow-sm rounded-sm border">{++index}</div>)}>push</Button>
      <Button onClick={() => {
        flippingRef.current.pop()
        index--
      }}>pop</Button>
    </div>
  )
}

export default React.memo(Base)
