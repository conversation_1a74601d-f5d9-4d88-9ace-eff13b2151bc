import React, {
  Fragment,
  ReactElement,
  ReactNode,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'
import { CSSTransition } from 'react-transition-group'
import Button, { IButtonProps as Props } from '../Button'
import Portals from './portals'
import { getLocale } from '@tita/utils';

import {
  ButtonGroup,
  ModalBody,
  ModalContent,
  ModalContentWrapper,
  ModalFooter,
  ModalFooterLeft,
  ModalHeader,
  ModalMask,
  ModalWrapper,
} from './style'
import { useZindex } from '@/hooks/useZindex';
export const destroyFns: Array<() => void> = []
export interface ModalProps {
  id?: string
  afterClose?: () => void
  /** 取消按钮文字 */
  cancelText?: string
  /** 垂直居中展示 Modal */
  centered?: boolean
  children: any
  /** 自定义 class */
  className?: string
  /** 是否显示右上角的关闭按钮 */
  closable?: boolean
  /** 自定义关闭图标 */
  closeIcon?: any
  container?: string
  /** 开启时使用自定义内容 */
  customContent?: boolean
  /** 关闭时销毁 Modal 里的子元素 */
  destroyOnClose?: boolean
  /** 是否开启动画 */
  effect?: boolean
  /** 页脚内容 */
  footer?: string | any
  footerLeftContent?: string | any
  /** 设置高度 */
  height?: number | string
  maxHeight?: number | string
  /** 是否显示遮罩 */
  mask?: boolean
  /** 点击蒙层是否允许关闭 */
  maskClosable?: boolean
  /** 遮罩层样式 */
  maskStyle?: React.CSSProperties
  /** 确定按钮点击状态 */
  okDisabled?: boolean
  /** 确定按钮 loading */
  okLoading?: boolean
  /** 确定按钮文字 */
  okText?: string
  /** 确定按钮类型，默认 primary */
  okType?: 'link' | 'text' | 'default' | 'border' | undefined
  /** 取消回调 */
  onCancel?: () => void
  /** 确定回调 */
  onOk?: () => void
  /** 是否显示取消按钮 */
  showCancelButton?: boolean
  autoZindex?: boolean
  /** 浮层样式 */
  style?: React.CSSProperties
  /** 设置标题 */
  title?: string | any
  /** Modal是否可见 */
  visible: boolean | undefined
  /** 宽度 */
  width?: string | number
  /** 设置 Modal 的 z-index */
  zIndex?: number
  /** 浮层样式 */
  headStyle?: React.CSSProperties
  bodyStyle?: React.CSSProperties
  footerStyle?: React.CSSProperties
  customHeaderContent?: ReactNode | null
}

export interface ModalFuncProps {
  cancelButtonProps?: Props
  /** 取消按钮文字 */
  cancelText?: string
  /** 垂直居中展示 Modal */
  centered?: boolean
  /** 自定义 class */
  className?: string
  /** 是否显示右上角的关闭按钮 */
  closable?: boolean
  /** 自定义关闭图标 */
  closeIcon?: any
  content?: string | ReactNode
  container?: string
  /** 开启时使用自定义内容 */
  customContent?: boolean
  /** 关闭时销毁 Modal 里的子元素 */
  destroyOnClose?: boolean
  /** 是否开启动画 */
  effect?: boolean
  /** 页脚内容 */
  footer?: string | any
  footerLeftContent?: string | any
  /** 设置高度 */
  height?: number | string
  maxHeight?: number | string
  icon?: ReactNode
  /** 是否显示遮罩 */
  mask?: boolean
  /** 点击蒙层是否允许关闭 */
  maskClosable?: boolean
  /** 遮罩层样式 */
  maskStyle?: React.CSSProperties
  okButtonProps?: Props
  /** 确定按钮点击状态 */
  okDisabled?: boolean
  /** 确定按钮 loading */
  okLoading?: boolean
  /** 确定按钮文字 */
  okText?: string
  /** 确定按钮类型，默认 primary */
  okType?: 'link' | 'text' | 'default' | 'border' | undefined
  /** 取消回调 */
  onOk?: (...args: any[]) => any
  onCancel?: (...args: any[]) => any
  /** 是否显示取消按钮 */
  showCancelButton?: boolean
  /** 浮层样式 */
  style?: React.CSSProperties
  /** 设置标题 */
  title?: string | ReactElement
  type?: string
  /** Modal是否可见 */
  visible?: boolean
  autoZindex?: boolean
  /** 宽度 */
  width?: string | number
  /** 设置 Modal 的 z-index */
  zIndex?: number
  /** 是否是危险操作 */
  danger?: boolean
  headStyle?: React.CSSProperties
  titleStyle?: React.CSSProperties
  bodyStyle?: React.CSSProperties
  contentStyle?: React.CSSProperties
  footerStyle?: React.CSSProperties
}

const Modal = forwardRef(
  (
    {
      id,
      bodyStyle,
      cancelText,
      centered,
      children,
      className,
      closable,
      closeIcon,
      container,
      autoZindex,
      customContent,
      destroyOnClose,
      effect,
      footer,
      footerLeftContent,
      footerStyle,
      headStyle,
      height,
      maxHeight = '100vh',
      mask,
      maskClosable,
      maskStyle,
      okDisabled,
      okLoading,
      okText,
      okType,
      onCancel,
      onOk,
      showCancelButton = true,
      style,
      title,
      visible = false,
      zIndex,
      width,
      customHeaderContent = null
    }: ModalProps,
    ref
  ) => {
    // const [footerHeight, setFooterHeight] = useState(0);
    const [hasShadow, setHasShadow] = useState(false)
    const [showContent, setShowContent] = useState(false)
    const contentRef = useRef<any>()
    const contentBodyRef = useRef<any>()
    // const footerRef = useCallback(node => {
    //   setTimeout(() => {
    //     if (node !== null) {
    //       setFooterHeight(node.clientHeight);
    //     }
    //   }, 300)
    // }, []);

    const footerContent = (
      <ButtonGroup>
        {showCancelButton && (
          <Button type='border' size='large' minWidth={96} onClick={onCancel}>
            {cancelText || '取消'}
          </Button>
        )}
        <Button
          disabled={okDisabled}
          type={okType}
          primary
          loading={okLoading}
          size='large'
          minWidth={96}
          onClick={() => !okLoading && onOk?.()}
        >
          {okText || '确认'}
        </Button>
      </ButtonGroup>
    )

    useImperativeHandle(ref, () => ({
      getRef: () => contentRef.current,
    }))

    const hasScrollbar = () =>
      document.body.scrollHeight >
      (window.innerHeight || document.documentElement.clientHeight)

    useEffect(() => {
      if (visible) {
        if (hasScrollbar()) {
          document.body.style.overflowY = 'hidden'
          document.body.style.width = 'calc(100% - 15px)'
        }
        setShowContent(visible)
      } else {
        setTimeout(() => {
          document.body.removeAttribute('style')
        }, 200)

        setTimeout(() => {
          setShowContent(visible)
        }, 300)
      }
    }, [visible])
    const zindex = useZindex(autoZindex ? [visible] : [])

    return (
      <Portals domNode={container} id={id}>
        <ModalWrapper
          className={className}
          visible={showContent}
          zIndex={autoZindex ? zindex : zIndex}
        >
          <CSSTransition
            in={visible}
            timeout={500}
            classNames='fade'
            unmountOnExit
          >
            <ModalMask
              mask={mask ? 'true' : ''}
              style={maskStyle}
              zIndex={autoZindex ? zindex : zIndex}
            />
          </CSSTransition>
          <ModalContentWrapper
            className={centered ? 'modal-centered' : ''}
            cursor={mask && maskClosable ? 'pointer' : 'default'}
            ref={contentRef}
            onClick={(e) => {
              e.stopPropagation()
              e.nativeEvent.stopImmediatePropagation()
              if (mask && maskClosable) {
                // @ts-ignore
                onCancel && onCancel(e)
              }
            }}
            zIndex={autoZindex ? zindex : zIndex}
          >
            {customContent ? (
              <CSSTransition
                in={visible}
                timeout={500}
                classNames={effect ? 'fadeIn' : 'effect-non'}
              >
                <Fragment>{children}</Fragment>
              </CSSTransition>
            ) : (
              <CSSTransition in={visible} timeout={500} classNames='fadeIn'>
                <ModalContent
                  centered={centered}
                  className='modal'
                  onClick={(e: any) => {
                    e.stopPropagation()
                    e.nativeEvent.stopImmediatePropagation()
                  }}
                  style={{
                    ...{
                      width: Number(width),
                      height: Number(height),
                      maxHeight: maxHeight,
                    },
                    ...style,
                  }}
                >
                  {showContent && (
                    <Fragment>
                      {closable && (
                        <Button
                          className='modal-close-btn'
                          onClick={onCancel}
                          style={{
                            boxShadow: 'none',
                          }}
                        >
                          {closeIcon ? (
                            closeIcon
                          ) : (
                            <span className='tu-icon-close'></span>
                          )}
                        </Button>
                      )}
                      {title && (
                        <ModalHeader
                          className={`modal-header ${
                            hasShadow ? 'modal-header_shadow' : ''
                          }`}
                          style={headStyle}
                        >
                          <span>{title}</span>{customHeaderContent && customHeaderContent}
                        </ModalHeader>
                      )}
                      <ModalBody
                        className='modal-body scrollbar-size-sm'
                        style={{
                          ...{
                            // paddingBottom: footerHeight + 'px',
                          },
                          ...bodyStyle,
                        }}
                        ref={contentBodyRef}
                        onScroll={(e) => {
                          const target = e.currentTarget
                          const showShadow =
                            target.scrollTop > 0 &&
                            target.scrollTop + target.clientHeight <
                              target.scrollHeight - 1
                          if (showShadow !== hasShadow) {
                            setHasShadow(showShadow)
                          }
                        }}
                      >
                        {children}
                      </ModalBody>
                      {footer == null ? (
                        ''
                      ) : (
                        <ModalFooter
                          className={`modal-footer ${
                            hasShadow ? 'modal-footer_shadow' : ''
                          }`}
                          style={footerStyle}
                        >
                          <ModalFooterLeft>{footerLeftContent}</ModalFooterLeft>
                          {footer || footerContent}
                        </ModalFooter>
                      )}
                    </Fragment>
                  )}
                </ModalContent>
              </CSSTransition>
            )}
          </ModalContentWrapper>
        </ModalWrapper>
      </Portals>
    )
  }
)

Modal.defaultProps = {
  cancelText: getLocale('Mod_Cancel'),
  centered: true,
  closable: true,
  closeIcon: '',
  customContent: false,
  effect: true,
  footer: '',
  mask: true,
  maskClosable: false,
  okDisabled: false,
  okText: getLocale('OKR_MyO_Butt_Determine'),
  okType: undefined,
  showCancelButton: true,
  title: '',
  visible: false,
  zIndex: 1300,
  width: 520,
  maxHeight: '100vh',
}
export default Modal
