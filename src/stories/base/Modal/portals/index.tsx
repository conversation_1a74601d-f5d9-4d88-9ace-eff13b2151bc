import { useEffect, useRef } from "react";
import ReactDOM from "react-dom";

interface Props {
  id?: string;
  domNode?: string; // 挂载组件节点
  className?: string; // 挂载点class
  children: React.ReactNode;
}

const appRoot = document.body;

const Portals: React.FC<Props> = ({ domNode, className, children, id }) => {
  const elRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const el = document.createElement("div");
    el.setAttribute("class", `document-mouse-event-ignore ${className || ""}`);
    el.setAttribute("id", id || "");
    elRef.current = el;

    setTimeout(() => {
      if (domNode) {
        const targetNode = document.getElementById(domNode);
        if (targetNode) targetNode.appendChild(el);
      } else {
        appRoot.appendChild(el);
      }
    }, 5);

    return () => {
      setTimeout(() => {
        if (domNode) {
          const targetNode = document.getElementById(domNode);
          if (targetNode) targetNode.removeChild(el);
        } else {
          appRoot.removeChild(el);
        }
      }, 5);
    };
  }, [domNode, className, id]);
  if(!elRef.current) return <></>
  return ReactDOM.createPortal(children, elRef.current);
};

export default Portals;
