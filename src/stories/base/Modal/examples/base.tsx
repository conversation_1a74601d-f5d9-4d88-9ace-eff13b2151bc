import React, { FC, useState } from 'react'
import Modal from '@/stories/base/Modal'
import Button from '@/stories/base/Button'

const { confirm , success} = Modal

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const showModal = () => {
    setIsModalOpen(true)
  }

  const handleOk = () => {
    setIsModalOpen(false)
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  const onCLickConfirm = () => {
    success({
      title: 'Confirm',
      // icon: '',
      // content: 'Bla bla ...',
      onCancel(...args) {
        console.log(`点击取消${args}`)
      },
      onOk(...args) {
        console.log(`点击确定${args}`)
      },
      okText: '确认',
      cancelText: '取消',
    })
  }

  const onCLickConfirmDanger = () => {
    confirm({
      title: 'Confirm',
      // icon: '',
      content: 'Bla bla ...',
      danger: true,
      onCancel(...args) {
        console.log(`点击取消${args}`)
      },
      onOk(...args) {
        console.log(`点击确定${args}`)
      },
      okText: '确认',
      cancelText: '取消',
    })
  }

  return (
    <>
      <Button type='border' onClick={showModal}>
        Open Modal
      </Button>
      <Button type='border' onClick={onCLickConfirm}>
        Open confirm
      </Button>
      <Button type='border' danger onClick={onCLickConfirmDanger}>
        Open confirm danger
      </Button>
      <Modal onOk={handleOk} title="这是弹窗示例" onCancel={handleCancel} visible={isModalOpen}>
        <div className="px-32px">
          <p>Some contents...</p>
          <p>Some contents...</p>
          <p>Some contents...</p>
        </div>
      </Modal>
    </>
  )
}

export default React.memo(Base)
