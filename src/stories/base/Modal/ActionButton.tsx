import React, { useRef } from 'react'
import Button, { IButtonProps as Props } from '../Button'

export interface ActionButtonProps {
  actionFn?: (...args: any[]) => any | PromiseLike<any>
  closeModal: Function
  buttonProps?: Props
  children?: React.ReactNode
}

const ActionButton: React.FC<ActionButtonProps> = ({
  actionFn,
  buttonProps,
  closeModal,
  children,
}) => {
  const clickRef = useRef<boolean>(false)

  const handlePromiseOnOk = (returnValurofOnOk: PromiseLike<any>) => {
    if (!returnValurofOnOk || !returnValurofOnOk.then) return
    returnValurofOnOk.then(
      (...arg: any[]) => {
        closeModal(...arg)
      },
      (e: Error) => {
        console.error(e)
        clickRef.current = false
      }
    )
  }

  const onClick = () => {
    if (clickRef.current) return
    clickRef.current = true
    if (!actionFn) {
      closeModal()
      return
    }
    let returnValurofOnOk
    if (actionFn.length) {
      returnValurofOnOk = actionFn(closeModal)
      clickRef.current = false
    } else {
      returnValurofOnOk = actionFn()
      if (!returnValurofOnOk) {
        closeModal()
        return
      }
    }
    handlePromiseOnOk(returnValurofOnOk)
  }

  return (
    <Button
      type="border"
      minWidth={90}
      {...buttonProps}
      onClick={onClick}
    >
      {children}
    </Button>
  )
}

export default ActionButton
