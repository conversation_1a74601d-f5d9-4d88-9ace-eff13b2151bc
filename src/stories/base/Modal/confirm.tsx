import ReactDom from 'react-dom';
import ConfirmDialog, { ConfirmDialogProps } from './ConfirmDialog'
import { ModalFuncProps, destroyFns } from './Modal';
import { Icon, LargeIcon } from './style';
import { getLocale } from '@tita/utils';
import { ReactElement, ComponentType } from 'react'

export type ModalFunc = (
  props: ModalFuncProps
) => {
  destroy: () => void;
  update: (newConfig: ModalFuncProps) => void;
};

export interface ModalStaticFunctions {
  confirm: ModalFunc
  dialog: ModalFunc
  info: ModalFunc
  success: ModalFunc
  warn: ModalFunc
  error: ModalFunc
  ConfirmDialog: ComponentType<ConfirmDialogProps>
}

export default function confirm(config: ModalFuncProps) {
  const _container = config.container || 'tita-ui_modal-confirm-container';
  const div = document.createElement('div');
  div.id = _container;
  document.body.appendChild(div);
  let currentConfig = { ...config, close, visible: true } as any;

  function destroy(...args: any[]) {
    let unmountResult = ReactDom.unmountComponentAtNode(div);
    if (unmountResult && div.parentNode) {
      div.parentNode.removeChild(div);
    }

    // if (config.onCancel) {
    //     config.onCancel(...args);
    // }

    for (let i = 0; i < destroyFns.length; i++) {
      const fn = destroyFns[i];
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      if (fn === close) {
        destroyFns.splice(i, 1);
        break;
      }
    }
  }

  function render({ cancelText, container, okText, okType, ...props }: any) {
    ReactDom.render(
      <ConfirmDialog
        cancelText={cancelText || getLocale('Mod_Cancel') || '取消'}
        container={_container}
        okText={okText || getLocale('Mod_Determiness') || '确定'}
        okButtonProps={{
          type: okType || 'border',
        }}
        {...props}
      />,
      div
    );
  }

  function close(...args: any[]) {
    currentConfig = {
      ...currentConfig,
      visible: false,
      afterClose: destroy(...args),
    };
    render(currentConfig);
  }

  function update(newConfig: ModalFuncProps) {
    currentConfig = {
      ...currentConfig,
      ...newConfig,
    };
    render(currentConfig);
  }

  render(currentConfig);
  destroyFns.push(close);

  return {
    destroy: close,
    update,
  };
}

export function withWarn(props: ModalFuncProps): ModalFuncProps {
  return {
    type: 'warn',
    icon: <Icon className="tu-icon-tishi" />,
    ...props,
  };
}

export function withInfo(props: ModalFuncProps): ModalFuncProps {
  return {
    type: 'info',
    icon: <Icon className="tu-icon-warning" />,
    ...props,
  };
}

export function withSuccess(props: ModalFuncProps): ModalFuncProps {
  return {
    type: 'success',
    icon: <LargeIcon className="tu-icon-finish-m" color="#00D685" />,
    ...props,
    okButtonProps: {
      primary: true,
      type: 'default',
      ...props.okButtonProps
    },
  };
}

export function withError(props: ModalFuncProps): ModalFuncProps {
  return {
    type: 'error',
    icon: <Icon className="tu-icon-cross" color="#F05E5E" />,
    ...props,
  };
}

export function withConfirm(props: ModalFuncProps): ModalFuncProps {
  return {
    type: 'confirm',
    icon: <Icon className="tu-icon-tishi" />,
    ...props,
    okButtonProps: {
      primary: true,
      type: 'default',
      ...props.okButtonProps
    },
  };
}

export function withDialog(props: ModalFuncProps): ModalFuncProps {
  return {
    closable: true,
    ...props,
    style: {
      padding: '18px 32px',
      ...props.style,
    },
    okButtonProps: {
      primary: true,
      type: 'default',
      ...props.okButtonProps
    },
  };
}
