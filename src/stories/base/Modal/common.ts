import classnames from 'classnames'

export function getClasses(type: string, transitionName: string) {
    const initClasses = classnames({
        [`${transitionName}-appear`]: type === 'appear',
        [`${transitionName}-enter`]: type === 'enter',
        [`${transitionName}-leave`]: type === 'leave',
    });
    const activeClasses = classnames({
        [`${transitionName}-appear-active`]: type === 'appear',
        [`${transitionName}-enter-active`]: type === 'enter',
        [`${transitionName}-leave-active`]: type === 'leave',
    });
    const endClasses = classnames({
        [`${transitionName}-appear-end`]: type === 'appear',
        [`${transitionName}-enter-end`]: type === 'enter',
        [`${transitionName}-leave-end`]: type === 'leave',
    });
    return { initClasses, activeClasses, endClasses };
}