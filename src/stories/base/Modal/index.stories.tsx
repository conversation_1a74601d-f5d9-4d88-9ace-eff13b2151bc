import React, { useCallback, useRef, useState, useEffect } from 'react';
import Button from '../button';
import Modal, { ModalProps } from './index';
import mdx from './modal.mdx';
import { boolean, number, object, text } from '@storybook/addon-knobs';
import { ButtonGroup, Icon } from './style';
import { OpenUserName } from '../common/openData';
import Select from '../select';

export default {
  title: 'Modal',
};

// @ts-ignore
const { confirm, info, success, warn, error } = Modal;

export const ModalDemo = () => {
  const modalRef = useRef<any>();
  const cancelText = text('取消按钮文字', '取消');
  const centered = boolean('垂直居中展示 Modal', true);
  const closable = boolean('是否显示关闭按钮', true);
  const closeIcon = text('关闭按钮文字', '关闭');
  const customContent = boolean('是否自定义内容', false);
  const modalVisible = false;
  const mask = boolean('是否显示遮罩', true);
  const maskClosable = boolean('点击蒙层是否允许关闭', true);
  const okDisabled = boolean('是否禁用确定按钮', false);
  const okText = text('确定按钮文字', '确定');
  const okType = object('确定按钮类型', 'primary');
  const showCancelButton = boolean('是否显示取消按钮', true);
  const style = object('内容区样式', { top: '20px', borderRadius: 24 });
  // const title = text('标题', <span>{}</span>)
  const height = number('内容区的高度', 480);
  const width = number('内容区的宽度', 520);
  const zIndex = number('设置 Modal 的 z-index', 1300);
  const [visible, setVisible] = useState(modalVisible);

  const getModalRef = useCallback(
    () => console.log(modalRef.current.getRef()),
    [modalRef]
  );

  const renderUsersNameToolTip = (userInfo: any[]) => {
    let content: JSX.Element[] = [];
    userInfo.forEach((user, index) => {
      let symbol =
        userInfo.length > 1 && index != userInfo.length - 1 ? '、' : '';
      content.push(
        <>
          <OpenUserName id={user.userId} name={user.name} />
          {symbol}
        </>
      );
    });
    return content;
  };
  const userInfo = [
    {
      userId: 112,
      name: '112',
    },
    {
      userId: 221,
      name: '221',
    },
  ];
  const titleContent = () => (
    <span>
      是否将<span>{renderUsersNameToolTip(userInfo)}</span>移到新增的执行人中？
    </span>
  );
  const showConfirmModal = () => {
    confirm({
      title: '该指标将被删除不可恢复，请谨慎操作！',
      content: '该指标将被删除不可恢复，请谨慎操作！',
      maskClosable,
      onCancel() {
        console.log('cancel');
      },
      onOk() {
        console.log('ok');
      },
      style: {borderRadius: 24}
    });
  };
  const showInfoModal = () => {
    info({
      title: '确定删除该考核对象吗？',
      onOk() {
        console.log('ok');
        // @ts-ignore
        // Modal.destroyAll();
      },
    });
  };
  const showSuccessModal = () => {
    success({
      title: '操作成功',
      onOk() {
        console.log('ok');
      },
    });
  };
  const showWarnModal = () => {
    warn({
      title: '确定删除该考核对象？',
      onOk() {
        console.log('ok');
      },
    });
  };
  const showErrorModal = () => {
    error({
      title: '删除失败',
      onOk() {
        console.log('ok');
      },
    });
  };
  const title = () => (
    <span>
      <OpenUserName id={12} name="dff" />
    </span>
  );
  return (
    <div style={{ paddingTop: 100, textAlign: 'center', height: 300 }}>
      <ButtonGroup style={{ flexDirection: 'column' }}>
        <Button type="primary" onClick={() => setVisible(true)}>
          Open Modal
        </Button>
        <div style={{ height: 20 }} />
        <Button type="default" onClick={getModalRef}>
          Get ModalRef
        </Button>
        <div style={{ height: 20 }} />
        <Button type="default" onClick={showConfirmModal}>
          confirm
        </Button>
        <div style={{ height: 20 }} />
        <Button type="default" onClick={showInfoModal}>
          info
        </Button>
        <div style={{ height: 20 }} />
        <Button type="default" onClick={showSuccessModal}>
          success
        </Button>
        <div style={{ height: 20 }} />
        <Button type="default" onClick={showWarnModal}>
          warn
        </Button>
        <div style={{ height: 20 }} />
        <Button type="default" onClick={showErrorModal}>
          error
        </Button>
        <div style={{ height: 20 }} />
        <Button
          type="default"
          onClick={() => {
            // @ts-ignore
            Modal.destroyAll();
          }}
        >
          Modal DestroyAll
        </Button>
      </ButtonGroup>
      <Modal
        cancelText={cancelText}
        centered={centered}
        closable={closable}
        closeIcon={closeIcon}
        customContent={customContent}
        footerLeftContent="left content"
        mask={mask}
        maskClosable={maskClosable}
        okDisabled={okDisabled}
        okText={okText}
        okType={okType}
        onCancel={() => setVisible(false)}
        onOk={() => {
          setVisible(false);
        }}
        ref={modalRef}
        showCancelButton={showCancelButton}
        style={style}
        title={title()}
        visible={visible}
        width={width}
        zIndex={zIndex}
      >
        <div style={{ padding: '0 32px', overflow: 'auto' }}>
          <h1>Select 1</h1>
          <Select
            dropdownMaxHeight={400}
            onChange={(v) => {
              console.log(v);
            }}
            value={4}
            options={[
              { value: 0, lable: '张三' },
              { value: 1, lable: '李四' },
              { value: 2, lable: '王二麻子' },
              { value: 3, lable: '二狗子' },
            ]}
            searchable
            searchTrigger={['change', 'click', 'enter']}
            onSearch={(v) => console.log(v)}
          />
          <h1>Select 2</h1>
          <Select
            dropdownMaxHeight={400}
            onChange={(v) => {
              console.log(v);
            }}
            value={4}
            options={[
              { value: 0, lable: '张三' },
              { value: 1, lable: '李四' },
              { value: 2, lable: '王二麻子' },
              { value: 3, lable: '二狗子' },
            ]}
            searchable
            searchTrigger={['change', 'click', 'enter']}
            onSearch={(v) => console.log(v)}
          />
          <h1>Select 3</h1>
          <Select
            dropdownMaxHeight={400}
            onChange={(v) => {
              console.log(v);
            }}
            value={4}
            options={[
              { value: 0, lable: '张三' },
              { value: 1, lable: '李四' },
              { value: 2, lable: '王二麻子' },
              { value: 3, lable: '二狗子' },
            ]}
            searchable
            searchTrigger={['change', 'click', 'enter']}
            onSearch={(v) => console.log(v)}
          />
          <h1>Select 4</h1>
          <Select
            dropdownMaxHeight={400}
            onChange={(v) => {
              console.log(v);
            }}
            value={4}
            options={[
              { value: 0, lable: '张三' },
              { value: 1, lable: '李四' },
              { value: 2, lable: '王二麻子' },
              { value: 3, lable: '二狗子' },
            ]}
            searchable
            searchTrigger={['change', 'click', 'enter']}
            onSearch={(v) => console.log(v)}
          />
        </div>
      </Modal>
    </div>
  );
};

ModalDemo.story = {
  name: 'Modal',
  parameters: {
    docs: { page: mdx },
  },
};

export const MenuTable1: React.FC<ModalProps> = () => null;
