import React, { ReactNode, useEffect, useState } from 'react'
import { CSSTransition } from 'react-transition-group'
import ActionButton from '../ActionButton'
import Modal, { ModalFuncProps } from '../Modal'
import { ModalContentWrapper, ModalMask } from '../style'
import {
  ButtonGroup,
  Content,
  ContentMain,
  Description,
  Title,
  Wrapper,
} from './style'
import Button from '../../Button'
import { isMobile } from '@/utils/platform'
import classNames from 'classnames'

export interface ConfirmDialogProps extends ModalFuncProps {
  afterClose?: () => void
  close: (...args: any[]) => void
  icon: ReactNode
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = (props) => {
  const {
    afterClose,
    bodyStyle,
    contentStyle,
    cancelButtonProps,
    cancelText,
    centered,
    className,
    close,
    content,
    footerStyle,
    headStyle,
    height,
    icon,
    // mask,
    // maskClosable,
    closeIcon,
    maskStyle,
    okButtonProps,
    okDisabled,
    okText,
    // okType,
    onCancel,
    onOk,
    // showCancelButton,
    // style,
    title,
    visible,
    footer,
    danger = false,
    // width,
    zIndex,
    ...restProps
  } = props

  const [showContent, setShowContent] = useState(false)

  // const okType = props.okType || 'primary';
  // 默认为 true，保持向下兼容
  const showCancelButton =
    'showCancelButton' in props ? props.showCancelButton! : true
  const width = props.width || 360
  const style = props.style || {}
  const mask = props.mask == undefined ? true : props.mask
  // 默认为 false
  const maskClosable =
    props.maskClosable == undefined ? false : props.maskClosable
  const closable = props.closable == undefined ? false : props.closable

  const cancelButton = showCancelButton && (
    <ActionButton
      actionFn={onCancel}
      buttonProps={cancelButtonProps}
      closeModal={() => handleClose()}
    >
      {cancelText}
    </ActionButton>
  )
  // const a = new Promise<1 | null>(() => {});
  const handleClose = () => {
    setShowContent(false)
    setTimeout(() => {
      close()
    }, 500)
  }

  useEffect(() => {
    if (visible) {
      setTimeout(() => {
        setShowContent(true)
      }, 200)
    } else {
      setTimeout(() => {
        setShowContent(false)
      }, 500)
    }
  }, [visible])

  return (
    <Modal
      afterClose={afterClose}
      centered={centered}
      className={className}
      closable={closable}
      customContent={true}
      effect={false}
      footer={null}
      height={height}
      mask={false}
      maskClosable={maskClosable}
      maskStyle={maskStyle}
      onCancel={() => handleClose()}
      style={style}
      title=''
      visible={visible as boolean}
      width={width}
      zIndex={zIndex}
      {...restProps}
    >
      <div>
        <CSSTransition
          in={showContent}
          timeout={500}
          classNames='fade'
          unmountOnExit
        >
          <ModalMask
            mask={mask ? 'true' : ''}
            style={maskStyle}
            zIndex={zIndex}
          />
        </CSSTransition>
        <ModalContentWrapper
          className='modal-centered'
          cursor={maskClosable ? 'pointer' : 'default'}
          onClick={(e) => {
            e.stopPropagation()
            e.nativeEvent.stopImmediatePropagation()
            if (maskClosable) {
              handleClose()
            }
          }}
          zIndex={zIndex}
        >
          <CSSTransition
            in={showContent}
            timeout={500}
            classNames='fadeIn'
            unmountOnExit
          >
            <Wrapper
              className='dialog-wrapper'
              isMobile={isMobile()}
              onClick={(e) => {
                e.stopPropagation()
                e.nativeEvent.stopImmediatePropagation()
              }}
              zIndex={zIndex}
              style={{ ...props.style, width }}
            >
              {closable && (
                <Button
                  className='modal-close-btn'
                  onClick={handleClose}
                  style={{
                    boxShadow: 'none',
                    position: 'absolute',
                    right: 20,
                    top: 12,
                  }}
                >
                  {closeIcon ? (
                    closeIcon
                  ) : (
                    <span
                      className='tu-icon-canceled'
                      style={{ fontSize: '20px' }}
                    ></span>
                  )}
                </Button>
              )}
              <Content className={classNames('dialog-content', {
                'h-full flex-1 flex-col': isMobile(),
              })}>
                {!isMobile() && icon}
                <div
                  className={classNames('w-full', {
                    'h-full flex-1 flex flex-col justify-center': isMobile(),
                  })}
                >
                  {title && (
                    <Title
                      className='dialog-title'
                      isMobile={isMobile()}
                      style={headStyle}
                    >
                      {title}
                    </Title>
                  )}
                  {content && (
                    <Description isMobile={isMobile()} style={bodyStyle}>
                      {content}
                    </Description>
                  )}
                </div>
              </Content>
              {footer !== undefined ? (
                footer
              ) : (
                <ButtonGroup style={footerStyle} isMobile={isMobile()}>
                  {cancelButton}
                  <ActionButton
                    actionFn={onOk}
                    buttonProps={{
                      ...okButtonProps,
                      ...{ disabled: okDisabled },
                      danger,
                    }}
                    closeModal={() => handleClose()}
                  >
                    {okText}
                  </ActionButton>
                </ButtonGroup>
              )}
            </Wrapper>
          </CSSTransition>
        </ModalContentWrapper>
      </div>
    </Modal>
  )
}

export default ConfirmDialog
