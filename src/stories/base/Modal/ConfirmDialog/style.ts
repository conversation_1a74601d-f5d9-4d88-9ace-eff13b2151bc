import styled, { keyframes } from 'styled-components';
interface ModalContentWrapperInteface {
  // cursor: string;
  zIndex?: number;
}
export const Wrapper = styled.div<ModalContentWrapperInteface & { isMobile: boolean }>`
  position: relative;
  width: 360px;
  min-height: 156px;
  padding: 24px 32px 20px;
  border-radius: 24px;
  box-sizing: border-box;
  background: #ffffff;
  cursor: initial;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-shadow: 0 12px 24px #7f91b433;
  z-index: ${(p: any) => p.zIndex || 1000};

  ${({ isMobile }) => isMobile && `
    padding: 20px 25px;
    width: 295px;
    max-width: 295px;
    min-height: 164px;
  `}
`;
export const Content = styled.div`
  display: flex;
`;
export const ContentMain = styled.div`
  display: flex;
`;
export const ButtonGroup = styled.div<{ isMobile: boolean }>`
  margin-top: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  button {
    margin-left: 12px;
  }

  ${({ isMobile }) => isMobile && `
    justify-content: space-between;
    margin-top: 20px;

    button {
      height: 40px !important;
      border-radius: 24px !important;
      width: 100%;
      margin-left: 0;
      font-size: 15px !important;
    }
    button + button {
      margin-left: 12px;
    }
    .tita-ui-button--default--danger {
      background: #F03D26 !important;
    }
  `}
`;
export const Title = styled.h2<{ isMobile: boolean }>`
  margin: 0;
  font-size: 16px;
  line-height: 24px;
  font-weight: bold;
  color: #141c28;

  ${({ isMobile }) => isMobile && `
    text-align: center;
  `}
`;
export const Description = styled.div<{ isMobile: boolean }>`
  width: 100%;
  min-height: 34px;
  margin: 6px 0 0 0;
  font-size: 14px;
  line-height: 20px;
  color: #6f7886;

  ${({ isMobile }) => isMobile && `
    text-align: center;
    margin-top: 8px;
    min-height: 32px;
  `}
`;
export const Icon = styled.a`
  width: 16px;
  min-width: 16px;
  height: 16px;
  min-height: 16px;
  margin-right: 5px;
  font-size: 16px;
  position: relative;
  top: 4px;
`;
