import styled from 'styled-components'

interface ModalWrapperInterface {
  visible: boolean
  zIndex?: number
}
export const ModalWrapper = styled.div<ModalWrapperInterface>`
  visibility: ${(p: any) => (p.visible ? 'visible' : 'hidden')};
  transition: visibility 0.3s ease-in;
  position: relative;
  z-index: ${(p: any) => p.zIndex || 1000};

  .fade-enter {
    opacity: 0;
  }
  .fade-enter-active {
    animation-name: fadeIn;
    animation-duration: 0.5s;
    animation-fill-mode: both;
  }
  .fade-exit {
    animation-name: fadeOut;
    animation-duration: 0.6s;
    animation-fill-mode: both;
  }
  .fade-exit-active {
    opacity: 0;
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    0% {
      opacity: 1;
    }

    50% {
      opacity: 0.4;
    }

    100% {
      opacity: 0;
    }
  }
`
interface MaskInterface {
  mask: string
  zIndex?: number
}
export const ModalMask = styled.div<MaskInterface>`
  height: 100%;
  background-color: ${(p: any) =>
    p.mask ? 'rgba(20, 28, 40, 0.5)' : 'transparent'};
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: ${(p: any) => p.zIndex || 1000};
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
`
interface ModalContentWrapperInteface {
  cursor: string
  zIndex?: number
}
export const ModalContentWrapper = styled.div<ModalContentWrapperInteface>`
  background: transparent;
  cursor: ${(p: any) => p.cursor};
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  outline: 0;
  z-index: ${(p: any) => p.zIndex || 1000};

  &.modal-centered {
    /* text-align: center; */
    display: flex;
    align-items: center;
    justify-content: center;

    /* &::before {
      content: '';
      width: 0;
      height: 100%;
      display: inline-block;
      vertical-align: middle;
    } */

    /* .modal {
      top: 0;
      display: inline-block;
      vertical-align: middle;
    } */
  }

  .fadeIn-enter {
    opacity: 0;
  }
  .fadeIn-enter-active {
    animation-name: fadeInDown;
    animation-duration: 0.5s;
    animation-fill-mode: both;
  }
  .fadeIn-exit {
    animation-name: fadeInUp;
    animation-duration: 0.6s;
    animation-fill-mode: both;
  }
  .fadeIn-exit-active {
    opacity: 0;
  }

  .fadeIn-exit-done {
    opacity: 0;
  }

  @keyframes fadeInDown {
    0% {
      opacity: 0;
      transform: translate3d(0, -24%, 0);
    }

    100% {
      opacity: 1;
      transform: translateZ(0);
    }
  }

  @keyframes fadeInUp {
    0% {
      opacity: 1;
      transform: translateZ(0);
    }

    40% {
      opacity: 1;
    }

    100% {
      opacity: 0;
      transform: translate3d(0, -24%, 0);
    }
  }
`
interface ModalContentInterface {
  centered?: boolean
}
export const ModalContent = styled.div<ModalContentInterface>`
  max-width: calc(100vw - 32px);
  margin: ${(p) => (p.centered ? '' : '0 auto')};
  padding: 0;
  border-radius: 24px;
  background: #ffffff;
  cursor: default;
  display: flex;
  flex-direction: column;
  position: relative;
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  box-shadow: 0px 8px 24px 0px rgba(127, 145, 180, 0.2);

  .modal-close-btn {
    min-width: 32px;
    width: 32px;
    padding: 0;
    cursor: pointer;
    border: none;
    background: transparent !important;
    outline: none;
    position: absolute;
    top: 16px;
    right: 23px;
    z-index: 10;
    border-radius: 8px;
    &:hover {
      background: none !important;
    }
    span {
      font-size: 17px;
      font-weight: bold;
      transition: color 0.3s ease-in;
      color: #141c28;

      &:hover {
        color: #f76964;
      }
    }
  }
`
export const ModalHeader = styled.div`
  padding: 18px 32px 8px 32px;
  font-size: 18px;
  font-weight: bold;
  line-height: 26px;
  transition: all 0.3s ease;
  color: #141c28;
  text-align: left;

  &.modal-header_shadow {
    box-shadow: 0px 6px 10px -6px #e9eef3;
    border-color: transparent;
  }
`
export const ModalBody = styled.div`
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
`
export const ModalFooter = styled.div`
  padding: 18px 32px;
  border-radius: 0 0 24px 24px !important;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  /* position: absolute;
  right: 0;
  bottom: 0;
  left: 0; */

  &.modal-footer_shadow {
    box-shadow: 0px -6px 10px -6px #e9eef3;
  }
`
export const ModalFooterLeft = styled.div`
  flex: 1;
  display: flex;
`
export const ModalCloseBtn = styled.button`
  position: absolute;
  top: 0;
  right: 0;
`
export const ButtonGroup = styled.div`
  flex: 0;
  display: flex;
  justify-content: flex-end;

  button {
    margin-left: 12px;
  }
`
export const Icon = styled.a`
  width: 16px;
  min-width: 16px;
  height: 16px;
  min-height: 16px;
  margin-right: 5px;
  font-size: 16px;
  position: relative;
  top: 4px;
  color: ${(p) => p.color || '#FFBC00'};

  &:hover {
    color: ${(p) => p.color || '#FFBC00'};
  }
`

export const LargeIcon = styled.a`
  width: 18px;
  min-width: 18px;
  height: 18px;
  min-height: 18px;
  margin-right: 7px;
  font-size: 18px;
  position: relative;
  top: 2px;
  color: ${(p) => p.color || '#FFBC00'};

  &:hover {
    color: ${(p) => p.color || '#FFBC00'};
  }
`