import OriginModal, { ModalFuncProps, destroyFns } from './Modal'
import ConfirmDialog from './ConfirmDialog';
import confirm, {
  withConfirm,
  withError,
  withInfo,
  withSuccess,
  withWarn,
  ModalStaticFunctions,
  withDialog,
} from './confirm'
export type { ModalProps } from './Modal'

type ModalType = typeof OriginModal & ModalStaticFunctions

export const Modal = OriginModal as ModalType

// @ts-ignore
Modal.dialog = function confirmFn(props: ModalFuncProps) {
  return confirm(withDialog(props))
}

// @ts-ignore
Modal.confirm = function confirmFn(props: ModalFuncProps) {
  return confirm(withConfirm(props))
}

// @ts-ignore
Modal.info = function infoFn(props: ModalFuncProps) {
  return confirm(withInfo(props))
}

// @ts-ignore
Modal.success = function successFn(props: ModalFuncProps) {
  return confirm(withSuccess(props))
}

// @ts-ignore
Modal.warn = function warnFn(props: ModalFuncProps) {
  return confirm(withWarn(props))
}

// @ts-ignore
Modal.error = function errorFn(props: ModalFuncProps) {
  return confirm(withError(props))
}

// @ts-ignore
Modal.destroyAll = function destroyAllFn() {
  while (destroyFns.length) {
    const close = destroyFns.pop()
    if (close) {
      close()
    }
  }
}

Modal.ConfirmDialog = ConfirmDialog

export default Modal
