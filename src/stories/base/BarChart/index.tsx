import React, { FC, useRef, useEffect } from 'react'
import classNames from 'classnames'
import { useUpdateEffect } from 'ahooks'
import merge from 'lodash/merge'

import * as echarts from 'echarts/core'
import {
  TooltipComponent,
  TooltipComponentOption,
  LegendComponent,
  LegendComponentOption,
  TitleComponent,
  TitleComponentOption,
  GridComponent,
} from 'echarts/components'
import { BarChart, BarSeriesOption } from 'echarts/charts'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import type { EChartsType } from 'echarts/types/dist/shared'

import './index.scss'
import { getLocale } from '@tita/utils'

interface IData {
  name: string
  value: number
}

export interface IBarProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  width?: number
  height?: number
  option: ECOption
}

echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  Bar<PERSON><PERSON>,
  <PERSON>vas<PERSON><PERSON><PERSON>,
  LabelLayout,
  GridComponent,
])

const preCls = 'tita-ui--bar'

type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | LegendComponentOption
>

export const Bar: FC<IBarProps> = React.memo(
  ({ className, style, width = 800, height = 400, option = {} }) => {
    const ref = useRef<HTMLElement | null>(null)
    const chartRef = useRef<EChartsType | null>(null)

    useEffect(() => {
      // @ts-ignore
      if (window.WWOpenData?.initCanvas) {
        // @ts-ignore
        window.WWOpenData.initCanvas();
      }
    }, [])

    useEffect(() => {
      chartRef.current = echarts.init(ref.current as HTMLElement)
      let _option = {
        legend: {
          bottom: 10,
          left: 'center',
          itemWidth: 16,
          itemHeight: 8,
          borderRadius: 2,
          data: [
            {
              name: getLocale('OKR_MyO_E_From_Completed'),
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#92F0D3', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#5AD8A6', // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            {
              name: getLocale('Per_Ma_form_detail_Incomplete'),
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#CEDDFD', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#DFEDFE', // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
          ],
        },
        grid: {
          left: 22,
          right: 22,
          top: 0,
          bottom: 0,
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
          },
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
              },
            },
          },
        ],
        series: [
          {
            name: '已完成',
            type: 'bar',
            stack: 'Total',
            barWidth: 24,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#92F0D3', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#5AD8A6', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
              borderColor: '#fff',
              borderWidth: 1,
            },
          },
          {
            name: '未完成',
            type: 'bar',
            barWidth: 24,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#CEDDFD', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#DFEDFE', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
              borderColor: '#fff',
              borderWidth: 1,
              borderRadius: [4, 4, 0, 0],
            },
            stack: 'Total',
          },
        ],
      }
      let targetOption = merge(_option, option)
      chartRef.current.setOption(targetOption)
    }, [])

    useEffect(() => {
      const chartSize = () => chartRef.current?.resize()
      window.addEventListener('resize', chartSize)
      return () => {
        window.removeEventListener('resize', chartSize)
      }
    }, [])

    useUpdateEffect(() => {
      chartRef.current?.setOption(option)
    }, [option])

    return (
      <div
        // @ts-ignore
        ref={ref}
        style={{ ...style, width, height }}
        className={classNames(preCls, className)}
      ></div>
    )
  }
)

export default Bar
