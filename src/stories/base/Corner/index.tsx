import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'
import Tooltip from '../Tooltip'
import { ITooltipProps } from '../Tooltip/tooltip'

export interface OffsetType {
  y?: number | string
  x: number | string
}

type PositionType = 'topLeft' | 'bottomLeft' | 'topRight' | 'bottomRight'

export interface ICornerProps {
  corner: JSX.Element | string
  children: React.ReactNode
  visible?: boolean
  className?: string
  width?: number
  height?: number
  tooltip?: string
  tooltipPlacement?: ITooltipProps['placement']
  pointerNone?: boolean
  style?: React.CSSProperties
  containerStyle?: React.CSSProperties
  offset?: OffsetType
  position?: PositionType
  zIndex?: number
}

enum cornerPosition {
  topLeft = 'top-0 left-0',
  bottomLeft = 'bottom-0 left-0',
  topRight = 'top-0 right-0',
  bottomRight = 'bottom-0 right-0',
}

const preCls = 'titaui--corner'

export const Corner: FC<ICornerProps> = (props) => {
  const {
    className,
    children,
    style,
    containerStyle,
    tooltip,
    tooltipPlacement = 'top',
    corner: propsCorner,
    visible = true,
    width,
    height,
    zIndex = 10,
    pointerNone,
    position = 'topLeft',
    offset,
  } = props
  // 支持角标偏移
  const getCornerStyle = (): React.CSSProperties => {
    const { x = 0, y = 0 } = offset || ({} as OffsetType)
    return {
      ...style,
      zIndex,
      transform: `translate(${typeof x === 'number' ? `${x}px` : x},  ${
        typeof y === 'number' ? `${y}px` : y
      })`,
      pointerEvents: pointerNone ? 'none' : undefined,
    }
  }

  // 增加角标位置预设
  const getCornerPosition = (positionType: PositionType) =>
    cornerPosition[positionType]

  if (!visible) return <>{children}</>

  const getRenderCorner = (corner: JSX.Element | string) => {
    if (typeof corner === 'string')
      return <img style={{ width, height }} src={corner} alt='' />
    return corner
  }

  return (
    <div className={classNames(preCls, className, ' relative')} style={containerStyle}>
      {tooltip ? (
        <Tooltip overlay={tooltip} placement={tooltipPlacement}>
          <div
            className={classNames('absolute', getCornerPosition(position))}
            style={getCornerStyle()}
          >
            {getRenderCorner(propsCorner)}
          </div>
        </Tooltip>
      ) : (
        <div
          className={classNames('absolute', getCornerPosition(position))}
          style={getCornerStyle()}
        >
          {getRenderCorner(propsCorner)}
        </div>
      )}
      {children}
    </div>
  )
}

export default React.memo(Corner)
