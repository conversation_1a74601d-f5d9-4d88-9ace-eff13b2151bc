import React, { FC } from 'react'
import Corner from '@/stories/base/Corner'
import Approval from '../images/approvaling.svg'

export interface IBaseProps { }

const Base: FC<IBaseProps> = ({ }) => {

  const renderImg: JSX.Element = (<img src={Approval} />)
  const renderItem2: string = 'cross'
  const cornerStyle = {
    fontSize: 14,
    color: '#FFF'
  }
  return (
    <>
      <Corner corner={renderImg} style={cornerStyle} position="topLeft">
        <div className=' w-[212px] h-[40px] border rounded-[15px]'></div>
      </Corner>

      <p>带 tooltip</p>
      <Corner corner={renderImg} style={cornerStyle} position="topLeft" tooltip="哈哈">
        <div className=' w-[212px] h-[40px] border rounded-[15px]'></div>
      </Corner>
    </>
  )
}

export default React.memo(Base)
