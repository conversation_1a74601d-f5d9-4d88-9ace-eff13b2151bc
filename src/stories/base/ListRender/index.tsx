import React, { FC } from 'react'
import styled from 'styled-components'
import './index.scss'

export interface IListRenderProps<RecordType = any> {
  spaceY?: number
  spaceX?: number
  itemStyle?: React.CSSProperties
  children?: React.ReactNode
}
export const ListRender: FC<IListRenderProps> = ({
  spaceX = 10,
  spaceY = 10,
  itemStyle,
  children,
}) => {
  const _children = (Array.isArray(children) ? children : [children]).filter(
    Boolean
  )
  return (
    <ItemsContainerStyle className='tdm-list-container'>
      <ItemsContainerInnerStyle
        className='tdm-list-container-inner'
        spaceY={spaceY}
        spaceX={spaceX}
      >
        {React.Children.map(_children, (item, i) => (
          <ItemStyle className='tdm-list-item' key={i} style={itemStyle}>
            {item}
          </ItemStyle>
        ))}
      </ItemsContainerInnerStyle>
    </ItemsContainerStyle>
  )
}

const ItemsContainerStyle = styled.div`
  
`

const ItemsContainerInnerStyle = styled.div<{ spaceY: number; spaceX: number }>`
  display: flex;
  flex-wrap: wrap;

  gap: ${({ spaceY, spaceX }) => `${spaceY}px ${spaceX}px`};
`

const ItemStyle = styled.div`
  width: 100%;

  &:empty {
    padding: 0;
  }
`

export default ListRender
