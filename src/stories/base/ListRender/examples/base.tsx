import React, { FC } from 'react'
import ListRender from '@/stories/base/ListRender'
import FileCard from '../../FileCard'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  return (
    <ListRender spaceX={8} spaceY={8} itemStyle={{ width: '100%' }}>
      <FileCard
        name='超长的名字啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊.png'
        size='100.kb'
        cover='https://xfile6.tita.com/ux/tita-home-page/public/file-type-mind.svg'
        showDelete
        showDownload
        showPreview
      />
      <FileCard
        name='超长的名字啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊.png'
        size='100.kb'
        cover='https://xfile6.tita.com/ux/tita-home-page/public/file-type-mind.svg'
        showDelete
        showDownload
        showPreview
      />
      <FileCard
        name='超长的名字啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊.png'
        size='100.kb'
        cover='https://xfile6.tita.com/ux/tita-home-page/public/file-type-mind.svg'
        showDelete
        showDownload
        showPreview
      />
      <FileCard
        name='超长的名字啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊.png'
        size='100.kb'
        cover='https://xfile6.tita.com/ux/tita-home-page/public/file-type-mind.svg'
        showDelete
        showDownload
        showPreview
      />
    </ListRender>
  )
}

export default React.memo(Base)
