import React, { <PERSON> } from 'react'
import Nav from '@/stories/base/Nav'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  return (
    <Nav
      dataSource={[
        { key: 1, label: 'root' },
        { key: 2, label: '文件夹1' },
        { key: 3, label: '文件夹2' },
      ]}
      itemStyle={{
        color: '#2879ff'
      }}
      splitIcon={<i className='tu-icon-APP-xi' />}
      onSelect={(item) => alert(`您选中了 ${item.label}`)}
    />
  )
}

export default React.memo(Base)
