import React, { FC, useRef, useState, useEffect } from 'react'
import classNames from 'classnames'
import Ellipsis from '../Ellipsis'
import OverflowObserver from '../OverflowObserver'
import './index.scss'

export type NavItem = {
  key: number | string | null
  label: string
  [key: string]: any
}

export interface INavProps {
  /**
   * @editType json
   * @default [{"key": 1, "label": "root"}, {"key": 2, "label": "文件夹1"}, {"key": 3, "label": "文件夹2"}]
   */
  dataSource: NavItem[]
  onSelect?: (nav: NavItem, idx: number) => void
  disableOverflow?: boolean
  itemStyle?: React.CSSProperties
  lastItemStyle?: React.CSSProperties
  splitIcon?: React.ReactNode
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-nav'

const littelNavItem = {
  label: '...',
  key: '...',
}

export const Nav: FC<INavProps> = React.memo(
  ({
    dataSource,
    onSelect,
    itemStyle,
    lastItemStyle,
    splitIcon: propsSplitIcon,
    disableOverflow,
    className,
    style,
  }) => {
    const [isOverflow, setIsOverflow] = useState(false)

    const [innderDataSource, setInnderDataSource] = useState(dataSource)

    const changedOverflowFlag = useRef(false)

    const onOverflowChange = (overflow: boolean) => {
      if (disableOverflow) return;
      if (!changedOverflowFlag.current && overflow !== isOverflow) {
        setIsOverflow(overflow)
      }
    }

    useEffect(() => {
      if (isOverflow && dataSource.length >= 3) {
        setInnderDataSource([
          dataSource[0],
          littelNavItem,
          dataSource[dataSource.length - 1],
        ])
        changedOverflowFlag.current = true
      } else {
        setInnderDataSource(dataSource)
        changedOverflowFlag.current = false
      }
    }, [dataSource, isOverflow])

    const splitIcon = (
      <span
        className={`${preCls}__split`}
        style={{
          padding: '0 4px',
        }}
      >
        {propsSplitIcon || '/'}
      </span>
    )

    return (
      <OverflowObserver style={{ width: '100%' }} onChange={onOverflowChange}>
        <div className={classNames(preCls, className)} style={style}>
          {innderDataSource?.map((nav, i) => (
            <>
              {!!i && splitIcon}
              <p
                key={nav.key}
                className={`${preCls}_item`}
                style={i === dataSource.length - 1 ? lastItemStyle : itemStyle}
                onClick={() =>
                  i !== dataSource.length - 1 &&
                  nav.key !== '...' &&
                  onSelect?.(nav, i)
                }
              >
                <Ellipsis>{nav.label}</Ellipsis>
              </p>
            </>
          ))}
        </div>
      </OverflowObserver>
    )
  }
)

export default Nav
