import { getLocale } from '@tita/utils'
import classNames from 'classnames'
import dayjs from 'dayjs'
import React, { FC, useMemo } from 'react'
import Date from '../Date'
import SelectView, { ISelectViewProps, SelectViewMode } from '../SelectView'
import './index.scss'

export interface IDateRangeViewProps extends ISelectViewProps {
  mode?: SelectViewMode
  startDate?: dayjs.ConfigType
  startDatePlaceholder?: string
  endDate?: dayjs.ConfigType | Function
  endDatePlaceholder?: string
  onClear?: () => void
  placeholder?: React.ReactNode
  disabledDownIcon?: boolean
  showTime?: boolean
  disabled?: boolean
  className?: string
  format?: string
  contentClassName?: string
  style?: React.CSSProperties
  /**
   * 点击事件
   */
  onClick?: React.MouseEventHandler<HTMLButtonElement>
  isLongTerm?: boolean
  /** 内容展示形式 */
  contentMode?: 'text' | 'default'
  canClear?: boolean
  disableEndDate?: boolean
  icon?: string // 是否显示图标
  disabledSpace?: boolean
  between?: boolean
}

const preCls = 'tita-ui-date-range-view'

export const DateRangeView: FC<IDateRangeViewProps> = React.memo((props) => {
  const {
    startDate,
    endDate,
    startDatePlaceholder = getLocale('Mod_Startingtime') || '开始时间',
    isLongTerm,
    endDatePlaceholder = getLocale('Mod_EndTime') || '结束时间',
    disabled,
    mode = 'default',
    contentClassName,
    className,
    onClick,
    disabledDownIcon,
    onClear,
    showTime,
    placeholder = getLocale('Mod_Pleaseselect'),
    contentMode = 'default',
    canClear = true,
    disableEndDate,
    icon,
    disabledSpace = false,
    between,
    format,
    ...rest
  } = props

  const isStraddleYear =
    dayjs(startDate).year() !==
    dayjs(endDate instanceof Function ? endDate() : endDate).year()

  const renderContent = useMemo(() => {
    switch (contentMode) {
      case 'text':
        return (
          <>
            {disableEndDate ? (
              <>
                {icon && <i className='tu-icon-calendar'></i>}
                <span className='text-#3f4755'>
                  <Date
                    date={startDate}
                    placeholder={startDatePlaceholder}
                    showTime={showTime}
                    format={format}
                  />
                </span>
              </>
            ) : (
              <>
                {icon && <i className='tu-icon-calendar'></i>}
                <span className='text-#3f4755'>
                  <Date
                    date={startDate}
                    placeholder={startDatePlaceholder}
                    showTime={showTime}
                    format={format}
                  />
                </span>
                <span>~</span>
                <span className='text-#3f4755'>
                  {isLongTerm ? (
                    getLocale('Pro_page_Plan_LongTerm') || '长期'
                  ) : (
                    <Date
                      date={endDate}
                      placeholder={endDatePlaceholder}
                      showTime={showTime}
                      format={format}
                    />
                  )}
                </span>
              </>
            )}
          </>
        )
      case 'default':
        return (
          <>
            {disableEndDate ? (
              <>
                {icon && <i className='tu-icon-calendar'></i>}
                <span className='text-#3f4755'>
                  <Date
                    date={startDate}
                    showYear={isStraddleYear}
                    placeholder={startDatePlaceholder}
                    showTime={showTime}
                    format={format}
                  />
                </span>
              </>
            ) : (
              <>
                {icon && <i className='tu-icon-calendar'></i>}
                <span className='text-#3f4755'>
                  <Date
                    date={startDate}
                    showYear={isStraddleYear}
                    placeholder={startDatePlaceholder}
                    showTime={showTime}
                    format={format}
                  />
                </span>
                <i className='tu-icon-timeDy-icon' />
                <span className='text-#3f4755'>
                  {isLongTerm ? (
                    getLocale('Pro_page_Plan_LongTerm') || '长期'
                  ) : (
                    <Date
                      date={endDate}
                      showYear={isStraddleYear}
                      placeholder={endDatePlaceholder}
                      showTime={showTime}
                      format={format}
                    />
                  )}
                </span>
              </>
            )}
          </>
        )
    }
  }, [
    contentMode,
    startDate,
    endDate,
    endDatePlaceholder,
    startDatePlaceholder,
    isStraddleYear,
    isLongTerm,
    disableEndDate,
  ])

  /** 是否跨年 */
  return (
    <SelectView
      className={classNames(preCls, className)}
      disabled={disabled}
      mode={mode}
      disabledDownIcon={disabledDownIcon}
      contentClassName={classNames('justify-around', contentClassName)}
      onClick={onClick}
      onClear={onClear}
      canClear={canClear && (!!startDate || !!endDate)}
      {...rest}
    >
      <div
        className={classNames('flex items-center', {
          'space-x-12px':
            !disabledSpace &&
            (mode === 'text' || mode === 'button' || mode === 'inline'),
          'space-x-34px': !disabledSpace && mode === 'default',
          'w-full justify-between': between,
        })}
      >
        {renderContent}
        {/* <span><Date date={startDate} showYear={isStraddleYear} placeholder={startDatePlaceholder} /></span>
        <i className="tu-icon-timeDy-icon" />
        <span>{isLongTerm ? getLocale('Pro_page_Plan_LongTerm') : <Date date={endDate} showYear={isStraddleYear} placeholder={endDatePlaceholder} />}</span> */}
      </div>
    </SelectView>
  )
})

export default DateRangeView
