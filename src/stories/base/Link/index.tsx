import React, { FC } from 'react'
import classNames from 'classnames'
import { arrow } from './image'
import './index.scss'

export interface ILinkProps {
  linkItem?: React.ReactNode
  children?: React.ReactNode | React.ReactNode[]
  noWrap?: boolean
  margin?: number
  direction?: 'row' | 'column'
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-link'

export const Link: FC<ILinkProps> = React.memo(
  ({
    linkItem,
    direction = 'row',
    margin = 8,
    noWrap,
    children,
    className,
    style,
  }) => {
    const _children = (Array.isArray(children) ? children : [children]).filter(
      Boolean
    )
    const _linkItem = linkItem || <img width={14} src={arrow} alt='箭头' />
    const childLength = React.Children.count(_children)
    return (
      <div
        className={classNames(preCls, className, noWrap ? '' : 'flex-wrap', {
          [`${preCls}--column w-full`]: direction === 'column',
          'childs-mb-8px': direction === 'row' && !noWrap,
        })}
        style={style}
      >
        {React.Children.map(_children, (item, i) => (
          <>
            {item}
            {childLength > 1 && i < childLength - 1 && (
              <div
                style={
                  direction === 'row'
                    ? { marginLeft: margin, marginRight: margin }
                    : { marginTop: margin, marginBottom: margin, width: '100%' }
                }
              >
                {_linkItem}
              </div>
            )}
          </>
        ))}
      </div>
    )
  }
)
