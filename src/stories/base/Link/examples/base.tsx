import React, { <PERSON> } from 'react'
import { Link } from '@/stories/base/Link'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({ }) => {
  return (
    <>
      <p>横向</p>
      <Link>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>1</div>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>2</div>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>3</div>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>4</div>
      </Link>

      <p>纵向</p>
      <Link direction="column">
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>1</div>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>2</div>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>3</div>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>4</div>
      </Link>

      <p>自定义链接内容</p>
      <Link linkItem={'-'}>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>1</div>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>2</div>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>3</div>
        <div className=' border rounded-md flex justify-center items-center' style={{ width: 20, height: 20 }}>4</div>
      </Link>
    </>
  )
}

export default React.memo(Base)
