import { useLocalStorage } from '@/hooks/useLocalStorage'
import { arr2Dic } from '@/utils/array'
import chunk from 'lodash/chunk'
import { memo, RefObject, useRef } from 'react'
import styled from 'styled-components'
import { IColorSelectionPropTypes } from './type'
import Popup, { IPopupRef } from '../Popup'
import { ChromePicker } from 'react-color'
import { toChangeValue } from './utils'

const quickColors = [
  { color: '#FF6953' },
  { color: '#FF9844' },
  { color: '#FFCF00' },
  { color: '#CBE600' },
  { color: '#43E7B4' },
  { color: '#29D5FF' },
  { color: '#588DFF' },
  { color: '#8663FF' },
  { color: '#FF58B7' },
  {
    color: '#FFFFFF',
    style: {
      border: '1px solid #DFE3EA',
    },
  },
  { color: '#FFE1E1' },
  { color: '#FFE1D0' },
  { color: '#FFEDAA' },
  { color: '#EAF599' },
  { color: '#C6F7E8' },
  { color: '#BEF2FF' },
  { color: '#CCDCFF' },
  { color: '#DAD0FF' },
  { color: '#FFCCE9' },
  { color: '#E5EDF8' },
  { color: '#FF9686' },
  { color: '#FFB67C' },
  { color: '#FFDD4C' },
  { color: '#DAED4C' },
  { color: '#7BEECA' },
  { color: '#69E1FF' },
  { color: '#8AAFFF' },
  { color: '#AA91FF' },
  { color: '#FF8ACC' },
  { color: '#CCD3E1' },
  { color: '#EF1F1F' },
  { color: '#F3731B' },
  { color: '#F2B100' },
  { color: '#BBD200' },
  { color: '#2EC18D' },
  { color: '#14A3EC' },
  { color: '#546BFF' },
  { color: '#8627FF' },
  { color: '#F42493' },
  { color: '#89919F' },
  { color: '#C40404' },
  { color: '#B04700' },
  { color: '#A67A00' },
  { color: '#748300' },
  { color: '#008154' },
  { color: '#006DA4' },
  { color: '#182EB8' },
  { color: '#5202B9' },
  { color: '#A10056' },
  { color: '#3F4755' },
]
const quickColorsSplits = chunk(quickColors, 10)
const quickColorsDic = arr2Dic(quickColors, 'color')

const defaultRecentColors: string[] = []

export const QuickColorSelector = memo(
  ({ color, onChange }: IColorSelectionPropTypes) => {
    // 最近使用
    const [recentColors] = useLocalStorage<string[]>(
      'recentColors',
      defaultRecentColors
    )

    const popupRef = useRef<IPopupRef>() as RefObject<IPopupRef>

    return (
      <Container>
        {quickColorsSplits.map((colors, idx) => (
          <div
            className='flex space-x-4px'
            style={{
              marginTop: (idx > 0 && (idx === 1 ? 12 : 4)) || 0,
            }}
          >
            {colors.map((item) => (
              <QuickColorsItemStyle
                active={color === item.color}
                onClick={() => {
                  onChange(item.color)
                }}
                style={{
                  backgroundColor: item.color,
                  border: `1px solid ${item.color}`,
                  ...item.style,
                }}
              >
                <i className='tu-icon-finished' />
              </QuickColorsItemStyle>
            ))}
          </div>
        ))}
        <p
          className='text-#6f7886 text-xs mt-10px'
          style={{ lineHeight: '18px' }}
        >
          最近使用
        </p>

        <div className='flex space-x-4px mt-4px'>
          {recentColors.map((item) => (
            <QuickColorsItemStyle
              active={color === item}
              onClick={() => {
                onChange(item)
              }}
              style={{
                backgroundColor: item,
                border: `1px solid ${item}`,
                ...quickColorsDic[item]?.style,
              }}
            >
              <i className='tu-icon-finished' />
            </QuickColorsItemStyle>
          ))}
        </div>

        <Popup
          clearPadding
          popupPlacement="right"
          ref={popupRef}
          popup={
            <ChromePickerContainerStyle>
              <ChromePicker
                color={color}
                onChange={(c) => {
                  let colorRgb = toChangeValue(c)
                  onChange(colorRgb)
                }}
              />
            </ChromePickerContainerStyle>
          }
        >
          <MoreColorBtnStyle className='mt-10px'>
            <ColorPanel />
            <p
              className='text-#6f7886 text-xs ml-6px'
              style={{ lineHeight: '18px' }}
            >
              更多颜色
            </p>
          </MoreColorBtnStyle>
        </Popup>
      </Container>
    )
  }
)

const Container = styled.div`
  padding: 12px 16px;
`

const QuickColorsItemStyle = styled.div<{ active: boolean }>`
  position: relative;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  box-sizing: border-box;

  i {
    opacity: 0;
    color: #bec4cd;
    font-size: 20px;
    transform: scale(0.5);
  }

  &::before {
    content: '';
    position: absolute;
    left: -3px;
    top: -3px;
    z-index: 5;
    width: 20px;
    height: 20px;
    border-radius: 6px;
    border: 1px solid #dfe3ea;
    opacity: 0;
    box-sizing: border-box;
  }

  &:hover::before {
    opacity: 1;
  }

  ${(props) =>
    props.active &&
    `
    &::before {
      opacity: 1;
    }
    i {
      opacity: 1;
    }
  `}
`

const MoreColorBtnStyle = styled.div`
  width: 196px;
  height: 28px;
  background: #f0f4fa;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
`

const ColorPanel = styled.div`
  width: 16px;
  height: 16px;
  background: conic-gradient(
    from 90deg at 50% 50%,
    #e02020 0%,
    #fa6400 17%,
    #f7b500 33%,
    #6dd400 50%,
    #0091ff 67%,
    #6236ff 83%,
    #b620e0 100%
  );
  border-radius: 50%;
`

const ChromePickerContainerStyle = styled.div`
  padding: 12px 16px;
  .chrome-picker {
    box-shadow: none !important;
  }

  input {
    background-color: #fff;
  }
`

export default QuickColorSelector
