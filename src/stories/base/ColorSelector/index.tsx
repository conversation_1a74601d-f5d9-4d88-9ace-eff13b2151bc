import React, { FC, useCallback, useMemo, useRef, useState } from 'react'
import Popup from '../Popup'
import BasicColorSelector from './colorSelector'
import './index.scss'
import { IColorSelectionPropTypes } from './type'
import QuickColorSelector from './quickColorSelector'
import { useLocalStorage } from '@/hooks/useLocalStorage'
import { useUpdateEffect } from 'ahooks'

export interface IColorSelectorProps {
  mode?: 'default' | 'quick'
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-color-selector'

const defaultRecentColors: string[] = []

export const ColorSelector: FC<IColorSelectorProps & IColorSelectionPropTypes> =
  React.memo(({ mode, className, style, color: prosColor, value, onChange, ...rest }) => {
    const [color, setColor] = useState<string>(value || prosColor || '')
    const [visibility, setVisibility] = useState<boolean>(false)

    useUpdateEffect(() => {
      setColor(value || prosColor || '')
    }, [prosColor, value])

    const { children } = rest

    const [recentColors, setRecentColors] = useLocalStorage<string[]>(
      'recentColors',
      defaultRecentColors
    )

    const colorRef = useRef<string>(color)
    const handleOk = useCallback((color?: string) => {
      if (color) {
        setColor(color)
        colorRef.current = color
        onChange?.(color)
      }
      if (mode !== 'quick') setVisibility(false)
    }, [])

    const onPopupVisibleChangeHandler = (visibility: boolean) => {
      setVisibility(visibility)

      // 如果当前为快捷模式，当 popup 关闭时记录最近使用颜色
      if (
        !visibility &&
        mode === 'quick' &&
        !recentColors.includes(colorRef.current)
      ) {
        setRecentColors([...recentColors, colorRef.current].slice(-10))
      }
    }

    const popContent = useMemo(() => {
      if (mode === 'quick') {
        return (
          <QuickColorSelector
            color={color}
            onChange={handleOk}
            onCancel={() => {
              setVisibility(false)
            }}
          />
        )
      }
      return (
        <BasicColorSelector
          color={color}
          onChange={handleOk}
          onCancel={() => {
            setVisibility(false)
          }}
        />
      )
    }, [color, onChange])

    return (
      // @ts-ignore
      <Popup
        popup={popContent}
        popupVisible={visibility}
        popupPlacement='bottom'
        clearPadding={mode === 'quick'}
        onPopupVisibleChange={onPopupVisibleChangeHandler}
      >
        {children || (
          <div
            style={{
              width: 28,
              height: 28,
              backgroundColor: color,
              borderRadius: 4,
              cursor: 'pointer',
            }}
          ></div>
        )}
      </Popup>
    )
  })

export default ColorSelector
