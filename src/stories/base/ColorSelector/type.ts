import { VisibilityType } from "@/stories/business/Visibility"

export interface IColorSelectionPropTypes {
  color?: string
  value?: string
  onChange?: (color?: string) => void
  onCancel?: () => void
}

export interface HSLColor {
  a?: number | undefined
  h: number
  l: number
  s: number
}

export interface RGBColor {
  a?: number | undefined
  b: number
  g: number
  r: number
}

export type Color = string | HSLColor | RGBColor

export interface ColorResult {
  hex: string
  hsl: HSLColor
  rgb: RGBColor
}
