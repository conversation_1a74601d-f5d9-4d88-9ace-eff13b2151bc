import { memo, useState } from 'react'
import { ChromePicker } from 'react-color'
import styled from 'styled-components'
import Button from '../Button'
import { IColorSelectionPropTypes } from './type'
import { toChangeValue } from './utils'

const BasicColorSelector = memo(
  ({ color, onChange, onCancel }: IColorSelectionPropTypes) => {
    const [selectColor, setSelectColor] = useState<string>(color)

    return (
      <Container>
        <ChromePicker
          color={selectColor}
          onChange={(c) => {
            let colorRgb = toChangeValue(c)
            setSelectColor(colorRgb)
          }}
        />
        <FootContainer>
          <Button
            type='border'
            style={{ marginRight: 10, width: 60 }}
            size='small'
            onClick={() => {
              onCancel?.();
            }}
          >
            取消
          </Button>
          <Button
            style={{ width: 60 }}
            size='small'
            primary
            onClick={() => {
              onChange(selectColor)
            }}
          >
            确认
          </Button>
        </FootContainer>
      </Container>
    )
  }
)

const Container = styled.div`
  .chrome-picker {
    box-shadow: none !important;
  }

  input {
    background-color: #fff;
  }
`

const FootContainer = styled.div`
  display: flex;
  justify-content: right;
`

export default BasicColorSelector
