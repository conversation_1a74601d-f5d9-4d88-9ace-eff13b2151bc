import React, { FC } from 'react'
import CountNode from './count'
import './index.scss'

export interface IOffsetNumber {
  top?: number
  right?: number
}
export interface IBadgeProps {
  /**
   * 数字微标内容区
   */
  children: React.ReactNode
  className?: string
  /**
   * 数字微标样式
   */
  countStyle?: React.CSSProperties
  /**
   * 数字微标数值
   */
  count: number
  /**
   * 数字微标位置
   */
  offset?: IOffsetNumber
  /**
   * 数字微标数值边界
   * @default 999
   */
  maxCount?: number
  /**
   * 渲染类型，default｜round
   * @default default
   */
  type?: string
  style?: React.CSSProperties
  autoWidth?: boolean
}

const preCls = 'tita-ui--badge'

export const Badge: FC<IBadgeProps> = (props: IBadgeProps) => {
  const { children, style={} } = props

  return (
    <div className={preCls} style={{...style}}>
      <CountNode {...props} />
      {!!children && children}
    </div>
  )
}

export default React.memo(Badge)
