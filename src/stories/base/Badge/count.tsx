import React, { FC, useCallback, useState, useEffect } from 'react'
import classNames from 'classnames'
import './index.scss'
import { IBadgeProps, IOffsetNumber } from './index'

export interface ICountNodeProps extends IBadgeProps {
  autoWidth?: boolean
}

const preCls = 'tita-ui--countNode'
const countNode: FC<ICountNodeProps> = (props) => {
  const { className, count, type, autoWidth } = props

  const [renderCount, setRenderCount] = useState('0')
  useEffect(() => {
    const { count, maxCount = 99 } = props
    if (!count) return
    setRenderCount(count > maxCount ? `${maxCount}+` : `${count}`)
  }, [props])

  // count样式
  // TODO: 高度固定，宽度自适应
  const getCountStyle = (): React.CSSProperties => {
    const { countStyle = {}, offset = {} } = props
    const { top = 0, right = 0 } = offset
    const countSize = renderCount.length === 1 ? 16 : renderCount.length * 8 + 4
    return {
      ...countStyle,
      width: autoWidth ? 'max-content' : countSize,
      minWidth: countSize,
      height: countSize,
      transform: `translate(calc(50% + ${top}px), ${right}px)`,
      borderRadius: autoWidth ? '10px' : `${parseInt((countSize / 2).toString())}px`,
    }
  }

  if (!count) return <></>

  return (
    <sup
      className={classNames(preCls, className, {
        [`${preCls}--type-${type}`]: type,
      })}
      style={{ ...getCountStyle() }}
    >
      {renderCount}
    </sup>
  )
}
export default React.memo(countNode)
