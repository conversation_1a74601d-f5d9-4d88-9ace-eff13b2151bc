import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'

export interface IInsetShadowProps {
  top?: boolean
  right?: boolean
  bottom?: boolean
  left?: boolean
  bent?: boolean
  zIndex?: number
  className?: string
  style?: React.CSSProperties
  topStyle?: React.CSSProperties
  bottomStyle?: React.CSSProperties
  leftStyle?: React.CSSProperties
  rightStyle?: React.CSSProperties
  shadowType?: 'default' | 'elliptical'
}

const preCls = 'tita-ui-inset-shadow'

export const InsetShadow: FC<IInsetShadowProps> = ({ top, right, bottom, left, bent, className, zIndex, style, topStyle, bottomStyle, leftStyle, rightStyle, shadowType = 'default' }) => {
  const _style = { ...style, ...topStyle, ...bottomStyle, ...leftStyle, ...rightStyle, zIndex }
  const showElliptical: boolean = shadowType === 'elliptical'
  return (
    <>
      {top && <div style={_style} className={classNames(preCls, `${preCls}--top`, { [`${preCls}--top-ellipticalShadow`]: showElliptical, [`${preCls}--top--bent`]: bent }, className)} />}
      {right && <div style={_style} className={classNames(preCls, `${preCls}--right`, { [`${preCls}--right-ellipticalShadow`]: showElliptical, [`${preCls}--right--bent`]: bent }, className)} />}
      {bottom && <div style={_style} className={classNames(preCls, `${preCls}--bottom`, { [`${preCls}--bottom-ellipticalShadow`]: showElliptical, [`${preCls}--bottom--bent`]: bent }, className)} />}
      {left && <div style={_style} className={classNames(preCls, `${preCls}--left`, { [`${preCls}--left-ellipticalShadow`]: showElliptical, [`${preCls}--left--bent`]: bent }, className)} />}
    </>
  )
}

export default React.memo(InsetShadow)