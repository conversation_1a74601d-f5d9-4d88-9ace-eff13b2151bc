.tita-ui-inset-shadow {
  &--top {
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    width: 100%;
    height: 20px;
    background-image: linear-gradient(to bottom, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 10;

    &-ellipticalShadow {
      top: -20px;
      border-radius: 0 0 50% 50%;
      box-shadow: 0px 3px 16px 0 rgba(127, 145, 180, 0.12);
    }
  }

  &--bottom {
    position: absolute;
    pointer-events: none;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 20px;
    background-image: linear-gradient(to top, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 10;

    &-ellipticalShadow {
      bottom: -20px;
      border-radius: 50% 50% 0 0;
      box-shadow: 0px 3px 16px 0  rgba(127, 145, 180, 0.12);
    }
  }

  &--left {
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    width: 20px;
    height: 100%;
    background-image: linear-gradient(to right, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 10;

    &-ellipticalShadow {
      left: -20px;
      border-radius: 0 50% 50% 0;
      box-shadow: 0px 3px 16px 0  rgba(127, 145, 180, 0.12);
    }
  }

  &--right {
    position: absolute;
    pointer-events: none;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background-image: linear-gradient(to left, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 10;

    &-ellipticalShadow {
      right: -20px;
      border-radius: 50% 0 0 50%;
      box-shadow: 0px 3px 16px 0  rgba(127, 145, 180, 0.12);
    }
  }
}