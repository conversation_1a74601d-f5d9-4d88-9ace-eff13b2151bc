import React, { FC, useState } from 'react'
import classNames from 'classnames'
import './index.scss'
import SlideProgress, { ISlideProgressProps } from '../SlideProgress'
import { Input, handleNumberFormat } from '../Input'
import { useUpdateEffect } from 'ahooks'
import Ellipsis from '../Ellipsis'

export interface IMutualProgressProps extends Omit<ISlideProgressProps, 'onChange'> {
  className?: string
  style?: React.CSSProperties
  initialValue?: number
  targetValue?: number
  unit?: string
  disable?: boolean
  onChange: (progress: number, achievement: number | string) => void
  progress?: number
  achievement?: number
}

const preCls = 'tita-ui--mutual-progress'

// @ts-ignore
export const MutualProgress: FC<IMutualProgressProps> = React.memo((props) => {
  const { className, style, initialValue = 0, targetValue = 100, unit = '%', disable, onChange, progress: propsProgress, achievement: propsAchievement, ...rest } = props

  const [_progress, setProgress] = useState(propsProgress || 0)
  const [_achievement, setAchievement] = useState<any>(propsAchievement || 0)
  useUpdateEffect(() => {
    propsAchievement && setAchievement(propsAchievement)
    propsProgress !== undefined && setProgress(propsProgress)
  }, [propsAchievement, propsProgress])
  const differValue = targetValue - initialValue


  const onProgressChange = (value: number) => {
    let newAchievement: any = value === 0 ? initialValue : value === 100 ? targetValue : (value / 100) * differValue + initialValue
    const newAchievementStr = newAchievement.toString()
    const dotIndex = newAchievementStr.indexOf('.')

    if (dotIndex !== -1) {
      newAchievement = newAchievementStr?.length - dotIndex >= 2 ? newAchievementStr.slice(0, dotIndex + 3) : newAchievement
    }

    setProgress(parseInt(value + ''))
    setAchievement(newAchievement)
    onChange?.(value, newAchievement)
  }

  const onInputChange = (value: string | number) => {
    const formatValue = handleNumberFormat(value)
    const valueNum = Number(formatValue)
    const newProgress = ((valueNum - initialValue) / differValue) * 100
    let newAchievement = formatValue === '' ? _achievement : formatValue
    const newAchievementStr = newAchievement.toString()

    const dotIndex = newAchievementStr.indexOf('.')

    if (dotIndex !== -1) {
      newAchievement = newAchievementStr?.length - dotIndex >= 2 ? newAchievementStr.slice(0, dotIndex + 3) : newAchievement
    }
    setProgress(parseInt(newProgress + ''))
    setAchievement(newAchievement)
    onChange?.(parseInt(newProgress + ''), newAchievement)
  }
  return (
    // @ts-ignore
    <div className={classNames(preCls, className)} style={style}>
      <div className={`${preCls}__left`}>
        <SlideProgress className={`${preCls}__left__bar`} value={_progress} onChange={(value) => onProgressChange(value)} disabled={disable} {...rest} />
        <div className={`${preCls}__label`}>
          <Ellipsis style={{ maxWidth: '150px' }}>
            <span className={`${preCls}__label__content`} >初始值</span>
            {initialValue}{unit}
          </Ellipsis>
          <Ellipsis style={{ maxWidth: '150px' }}>
            <span className={`${preCls}__label__content`} >目标值</span>
            {targetValue}{unit}
          </Ellipsis>
        </div>
      </div>
      <div className={`${preCls}__right`}>
        <Input className={`${preCls}__right__input`} valueFormat='number' value={_achievement} initialValue={_achievement} onChange={(value) => onInputChange(value)} disabled={disable} suffixContent={unit} />
      </div>
    </div>
  )
})

export default MutualProgress
