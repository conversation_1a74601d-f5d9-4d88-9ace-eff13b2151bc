import React, { FC, MutableRefObject, useMemo, useRef, useState } from 'react'
import classNames from 'classnames'
import { useDebounceFn, useSize, useUpdateEffect } from 'ahooks'
import './index.scss'

export interface IObjectFitProps {
  width?: number | string
  height?: number | string
  mode?: 'scale-down' | 'fill' | 'contain' | 'cover' | 'none'
  /**
   * X轴定位
   * @default center
   */
  positionX?: 'left' | 'center' | 'right'
  /**
   * Y轴定位
   * @default center
   */
  positionY?: 'top' | 'center' | 'bottom'
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-object-fit'

export const ObjectFit: FC<IObjectFitProps> = React.memo(
  ({
    width,
    height,
    mode = 'contain',
    positionX = 'center',
    positionY = 'center',
    children,
    className,
    style,
  }) => {
    const containerRef = useRef() as MutableRefObject<HTMLDivElement>
    const contentRef = useRef() as MutableRefObject<HTMLDivElement>

    const containerSize = useSize(containerRef)
    const contentSize = useSize(contentRef)

    const [initialled, setInitialled] = useState(false)
    const [transition, setTransition] = useState(false)
    const [scale, setScale] = useState(1)

    const _setInitialled = useDebounceFn(setInitialled, { wait: 200 })
    const _setTransition = useDebounceFn(setTransition, { wait: 500 })

    useUpdateEffect(() => {
      if (!containerSize || !contentSize) return
      const containerWidth = containerSize.width
      const containerHeight = containerSize.height

      const contentWidth = contentSize.width
      const contentHeight = contentSize.height

      const widthOverflow = contentWidth - containerWidth
      const heightOverflow = contentHeight - containerHeight

      let widthScale = 1
      let heightScale = 1

      if (widthOverflow > 0) {
        widthScale = 1 - widthOverflow / contentWidth
      } else if (widthOverflow < 0) {
        widthScale = 1 + Math.abs(widthOverflow) / contentWidth
      }

      if (heightOverflow > 0) {
        heightScale = 1 - heightOverflow / contentHeight
      } else if (heightOverflow < 0) {
        heightScale = 1 + Math.abs(heightOverflow) / contentHeight
      }

      setScale(Math.min(widthScale, heightScale))
      _setInitialled.run(true)
      _setTransition.run(true)
    }, [containerSize, contentSize, mode])

    const contentStyle = useMemo(() => {
      let style: React.CSSProperties = {}
      style.left = {
        center: '50%',
        left: 0,
        right: 'unset',
      }[positionX]
      style.top = {
        center: '50%',
        top: 0,
        bottom: 'unset',
      }[positionY]
      style.right = {
        center: 'unset',
        left: 'unset',
        right: 0,
      }[positionX]
      style.bottom = {
        center: 'unset',
        top: 'unset',
        bottom: 0,
      }[positionY]

      style.transformOrigin = `${positionX} ${positionY}`
      style.transform = `translate(${
        {
          center: '-50%',
          left: '0',
          right: '0',
        }[positionX]
      }, ${
        {
          center: '-50%',
          top: '0',
          bottom: '0',
        }[positionY]
      }) scale(${scale})`

      return style
    }, [positionX, positionY, scale])

    return (
      <div
        ref={containerRef}
        className={classNames(preCls, className)}
        style={{ ...style, width, height }}
      >
        <div
          ref={contentRef}
          className={classNames(`${preCls}__content`, {
            [`${preCls}__content--initialled`]: initialled || scale !== 1,
            [`${preCls}__content--transition`]: transition,
          })}
          style={contentStyle}
        >
          {children}
        </div>
      </div>
    )
  }
)

export default ObjectFit
