import React, { FC } from 'react'
import ObjectFit from '@/stories/base/ObjectFit'

export interface IBaseProps {
  /**
   * 容器宽度
   * @default 300
   */
  containerWidth?: number
  /**
   * 容器高度
   * @default 200
   */
  containerHeight?: number
  /**
   * 内容宽度
   * @default 200
   */
  contentWidth?: number
  /**
   * 内容高度
   * @default 400
   */
  contentHeight?: number
  /**
   * X轴定位
   * @default center
   */
  positionX?: 'left' | 'center' | 'right'
  /**
   * Y轴定位
   * @default center
   */
  positionY?: 'top' | 'center' | 'bottom'
}

const Base: FC<IBaseProps> = ({ containerWidth = 300, containerHeight = 200, contentHeight = 400, contentWidth = 200, positionX, positionY }) => {
  return (
    <ObjectFit width={containerWidth} height={containerHeight} className="shadow-lg bg-black" positionX={positionX} positionY={positionY}>
      <div style={{ height: contentHeight, width: contentWidth }} className="bg-white"></div>
    </ObjectFit>
  )
}

export default React.memo(Base)
