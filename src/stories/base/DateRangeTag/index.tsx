import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'
import Tag, { ITagProps } from '../Tag'
import dayjs from 'dayjs'
import Date from '../Date'
import { getLocale } from '@tita/utils'

export interface IDateRangeTagProps extends Omit<ITagProps, 'children'> {
  startDate: string
  endDate: string
  startDatePlaceholder?: string
  endDatePlaceholder?: string
  showTime?: boolean
  disableEndDate?: boolean
  isLongTerm?: boolean
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui--date-range-tag'

export const DateRangeTag: FC<IDateRangeTagProps> = React.memo(
  ({
    startDate,
    endDate,
    startDatePlaceholder,
    endDatePlaceholder,
    disableEndDate,
    showTime,
    isLongTerm,
    className,
    style,
    ...other
  }) => {
    const isStraddleYear = dayjs(startDate).year() !== dayjs(endDate).year()
    return (
      <Tag className={classNames(preCls, className)} style={style} {...other}>
        <Date
          date={startDate}
          showYear={isStraddleYear}
          placeholder={startDatePlaceholder}
          showTime={showTime}
        />
        {!disableEndDate && '~'}
        {!disableEndDate &&
          (isLongTerm ? (
            getLocale('Pro_page_Plan_LongTerm') || '长期'
          ) : (
            <Date
              date={endDate}
              showYear={isStraddleYear}
              placeholder={endDatePlaceholder}
              showTime={showTime}
            />
          ))}
      </Tag>
    )
  }
)

export default DateRangeTag
