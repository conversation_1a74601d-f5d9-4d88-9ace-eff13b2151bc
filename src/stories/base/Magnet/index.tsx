import React, { FC, useRef, useState, useEffect, useCallback, MutableRefObject, RefObject } from 'react'
import { useSpring, animated } from '@react-spring/web';
import { useDrag } from '@use-gesture/react';
import { useSize } from 'ahooks';
import './index.scss'

export interface IMagnetProps extends React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  /**
   * 固定 X 轴位置
   */
  fastenX?: boolean
  /**
   * 固定 Y 轴位置
   */
  fastenY?: boolean
  data?: any
  onDragStart?: (data: any) => void
  onDragEnd?: (data: any) => void
  containerRef?: React.MutableRefObject<HTMLDivElement> | 'parent'
  /**
   * @default Drag ME!
   */
  children?: React.ReactNode
  className?: string
  cacheKey?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-magnet'

const getCacheYKey = (key: string) => `${preCls}_cache_y_${key}`
const getCacheXKey = (key: string) => `${preCls}_cache_x_${key}`

export const Magnet: FC<IMagnetProps> = React.memo(({ fastenX, fastenY, data, onDragStart, onDragEnd, className, style, containerRef, children, cacheKey, ...otherPorps }) => {
  const cacheY = parseInt(cacheKey && localStorage.getItem(getCacheYKey(cacheKey)) || '0')
  const cacheX = parseInt(cacheKey && localStorage.getItem(getCacheXKey(cacheKey)) || '0')
  
  const [boundary, setBoundary] = useState({ left: 0, right: 0, top: 0, bottom: 0 })
  const [{ x, y }, api] = useSpring(() => ({ x: cacheX, y: cacheY }))
  
  const dragItemRef = useRef<any>() as any

  useEffect(() => {
    if (dragItemRef.current) {
      const { left, right, top, bottom } = (containerRef === 'parent' ? dragItemRef.current.parentNode : (containerRef && containerRef.current || document.body)).getBoundingClientRect()
      const { left: childLeft, right: childRight, top: childTop, bottom: childBottom } = dragItemRef.current.getBoundingClientRect()
      setBoundary({ left: -(childLeft - left), right: right - childRight, top: -(childTop - top), bottom: bottom - childBottom })
    }
  }, [dragItemRef, containerRef])

  const isMove = useRef(false)

  const touchBind = useDrag(({ movement: [mx, my], lastOffset: [lastX, lastY], event, active, first, last }) => {
    event.stopPropagation()

    let newY = cacheY + (fastenY ? 0 : lastY) + my
    let newX = cacheX + (fastenX ? 0 : lastX) + mx

    // 拖动到视口边界
    if (newY < boundary.top) newY = boundary.top
    if (newY > boundary.bottom) newY = boundary.bottom
    if (newX < boundary.left) newX = boundary.left
    if (newX > boundary.right) newX = boundary.right

    isMove.current = Math.abs(mx) > 5 || Math.abs(my) > 5

    if (first && onDragStart) onDragStart(data)
    if (last && onDragEnd) onDragEnd(data)
    
    if (!active) {
      api.start({ x: fastenX ? 0 : newX, y: fastenY ? 0 : newY })

      if (cacheKey) {
        if (!fastenY) localStorage.setItem(getCacheYKey(cacheKey), String(newY))
        if (!fastenX) localStorage.setItem(getCacheYKey(cacheKey), String(newX))
      }
      return
    }
    
    api.start({ x: newX, y: newY })
  })

  const stopPop: React.DragEventHandler<HTMLDivElement> = useCallback((e) => {
    e.stopPropagation()
    e.preventDefault();
  }, [])

  const onClickHandler: React.MouseEventHandler<HTMLDivElement> = (e) => {
    // 移动后禁止点击事件
    if (isMove.current) {
      isMove.current = false
      e.stopPropagation()
    }
  }
  
  return (
    <animated.div ref={dragItemRef} {...otherPorps} onDragStart={stopPop} className={`${preCls} ${className}`} style={{ ...style, x, y }} {...touchBind()} onClickCapture={onClickHandler}>
      {children}
    </animated.div>
  )
})

export default Magnet
