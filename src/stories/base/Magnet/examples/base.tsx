import React, { FC, useRef } from 'react'
import Magnet from '@/stories/base/Magnet'
import Button from '../../Button'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({ }) => {
  const containerRef = useRef() as React.MutableRefObject<HTMLDivElement>
  return (
    <div ref={containerRef} className="p-10 border">
      <div className='h-36 border shadow-md relative'>
        <Magnet containerRef="parent" fastenX>
          <Button primary>我会一直在左侧</Button>
        </Magnet>
        <Magnet className='absolute right-0' containerRef="parent" fastenX>
          <Button primary>我会一直在右侧</Button>
        </Magnet>
        <Magnet containerRef="parent">
          <Button primary>我不能超出父容器</Button>
        </Magnet>
        <Magnet containerRef={containerRef}>
          <Button primary>我不能超出父容器的父容器</Button>
        </Magnet>
      </div>
    </div>
  )
}

export default React.memo(Base)
