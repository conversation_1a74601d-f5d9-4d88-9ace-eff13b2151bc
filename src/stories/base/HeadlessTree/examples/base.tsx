import React, { FC } from 'react'
import HeadlessTree from '@/stories/base/HeadlessTree'
import Button from '../../Button'
import classNames from 'classnames'

export interface IBaseProps {}

const mockData = [
  {
    title: '根节点1',
    key: '1',
  },
  {
    title: '子节点1-1',
    key: '1-1',
    parent<PERSON>ey: '1',
  },
  {
    title: '子节点1-1-1',
    key: '1-1-1',
    parentKey: '1-1',
    isLeaf: true,
  },
  {
    title: '子节点1-1-2',
    key: '1-1-2',
    parentKey: '1-1',
    isLeaf: true,
  },
  {
    title: '子节点1-2',
    key: '1-2',
    parentKey: '1',
    isLeaf: true,
  },
  {
    title: '根节点2',
    key: '2',
  },
  {
    title: '子节点2-1',
    key: '2-1',
    parentKey: '2',
    isLeaf: true,
  },
  {
    title: '子节点2-2',
    key: '2-2',
    parentKey: '2',
    isLeaf: true,
  },
]

const mockTreeData = [
  {
    title: '根节点1',
    key: '1',
    children: [
      {
        title: '子节点1-1',
        key: '1-1',
        parentKey: '1',
      },
    ],
  },
  {
    title: '根节点2',
    key: '2',
    children: [
      {
        title: '子节点2-1',
        key: '2-1',
        parentKey: '2',
        children: [
          {
            title: '子节点2-1-1',
            key: '2-1-1',
            parentKey: '2-1',
            isLeaf: true,
          },
        ],
      },
      {
        title: '子节点2-2',
        key: '2-2',
        parentKey: '2',
        isLeaf: true,
      },
    ],
  },
]

const Base: FC<IBaseProps> = ({}) => {
  return (
    <div className='space-y-10px'>
      <div>
        <HeadlessTree
          datas={mockData}
          autoFormat={{
            keyField: 'key',
            parentField: 'parentKey',
            isRoot(node) {
              return !node.parentKey
            },
            isLeaf: 'isLeaf',
          }}
          defaultExpandAll
          renderFolder={({
            data,
            changeOpen,
            changeSelected,
            selected,
            open,
            level,
          }) => (
            <div
              className={classNames(
                'p-10px rounded-md border border-slate-300 flex justify-between',
                {
                  'bg-slate-100 text-blue-500': selected,
                }
              )}
              onClick={() => changeSelected(!selected)}
              style={{ marginLeft: level * 10 }}
            >
              <span>{data.title}</span>
              <Button
                shape='square'
                size='least'
                type='text'
                icon='caret-down'
                onClick={(e) => {
                  e.stopPropagation()
                  changeOpen(!open)
                }}
                style={{
                  transform: `rotate(${open ? 0 : -90}deg)`,
                }}
              />
            </div>
          )}
          renderItem={({ data, level, changeSelected, selected }) => (
            <div
              className={classNames(
                'p-10px rounded-md border border-slate-300',
                {
                  'bg-slate-100 text-blue-500': selected,
                }
              )}
              style={{ marginLeft: level * 10 }}
              onClick={() => changeSelected(!selected)}
            >
              {data.title}
            </div>
          )}
        />
      </div>
      <div>
        <HeadlessTree
          datas={mockTreeData}
          defaultExpandAll
          renderFolder={({ data, changeOpen, open, level }) => (
            <div
              className='p-10px rounded-md border border-slate-300 flex justify-between'
              onClick={() => changeOpen(!open)}
              style={{ marginLeft: level * 10 }}
            >
              <span>{data.title}</span>
              <Button
                shape='square'
                size='least'
                type='text'
                icon='caret-down'
                style={{
                  transform: `rotate(${open ? 0 : -90}deg)`,
                }}
              />
            </div>
          )}
          renderItem={({ data, level }) => (
            <div
              className='p-10px rounded-md border border-slate-300'
              style={{ marginLeft: level * 10 }}
            >
              {data.title}
            </div>
          )}
        />
      </div>
    </div>
  )
}

export default React.memo(Base)
