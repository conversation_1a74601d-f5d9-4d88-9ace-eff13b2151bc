import React, { <PERSON> } from 'react'
import HeadlessTree, { IHeadlessTreeData } from '@/stories/base/HeadlessTree'
import Button from '../../Button'
import classNames from 'classnames'
import Nav from '../../Nav'
import CheckBox from '../../CheckBox'

export interface IDrillProps {}

const mockData: IHeadlessTreeData[] = [
  {
    title: '根节点1',
    key: '0',
    isLeaf: true,
  },
  {
    title: '根节点2',
    key: '1',
  },
  {
    title: '子节点1-1',
    key: '1-1',
    parentKey: '1',
  },
  {
    title: '子节点1-1-1',
    key: '1-1-1',
    parentKey: '1-1',
    isLeaf: true,
  },
  {
    title: '子节点1-1-2',
    key: '1-1-2',
    parentKey: '1-1',
    isLeaf: true,
  },
  {
    title: '子节点1-2',
    key: '1-2',
    parentKey: '1',
    isLeaf: true,
  },
  {
    title: '根节点3',
    key: '2',
  },
  {
    title: '子节点2-1',
    key: '2-1',
    parentKey: '2',
    isLeaf: true,
    selectable: false,
  },
  {
    title: '子节点2-2',
    key: '2-2',
    parentKey: '2',
    isLeaf: true,
  },
]

const Drill: FC<IDrillProps> = ({}) => {
  return (
    <div className='space-y-10px'>
      <HeadlessTree
        datas={mockData}
        autoFormat={{
          keyField: 'key',
          parentField: 'parentKey',
          isRoot(node) {
            return !node.parentKey
          },
          isLeaf: 'isLeaf',
        }}
        onSelect={(key) => console.log('select', key)}
        drill
        renderWrapper={({ listContent, drillPathData, drillBack }) => {
          return (
            <div>
              {!!drillPathData.length && (
                <Nav
                  dataSource={[
                    {
                      label: 'Home',
                      key: null,
                    },
                    ...drillPathData.map((item) => ({
                      label: item.title,
                      key: item.key,
                    })),
                  ]}
                  onSelect={(item) => drillBack(item.key)}
                />
              )}
              {listContent}
            </div>
          )
        }}
        renderFolder={({
          data,
          changeOpen,
          changeSelected,
          drill,
          selected,
          partSelected,
          open,
          level,
        }) => {
          return (
            <div
              className={classNames(
                'p-10px rounded-md border border-slate-300 flex justify-between',
                {
                  'bg-slate-100 text-blue-500': selected,
                }
              )}
            >
              <div className='flex items-center space-x-10px'>
                <CheckBox
                  disabled={data.selectable === false}
                  checked={selected}
                  partChecked={partSelected}
                  onChange={() => changeSelected(!selected)}
                />
                <span>{data.title}</span>
              </div>
              <Button
                shape='square'
                size='least'
                type='text'
                icon='H5-right-s'
                onClick={(e) => {
                  e.stopPropagation()
                  drill()
                }}
              />
            </div>
          )
        }}
        renderItem={({ data, level, changeSelected, selected }) => (
          <div
            className={classNames('p-10px rounded-md border border-slate-300', {
              'bg-slate-100 text-blue-500': selected,
            })}
          >
            <div className='flex items-center space-x-10px'>
              <CheckBox
                disabled={data.selectable === false}
                checked={selected}
                onChange={() => changeSelected(!selected)}
              />
              <span>{data.title}</span>
            </div>
          </div>
        )}
      />
    </div>
  )
}

export default React.memo(Drill)
