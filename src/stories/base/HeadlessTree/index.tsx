import { arr2Dic, deepMap } from '@/utils/array'
import { useRefState } from '@tita/hooks'
import { flattenDeepByField } from '@tita/utils'
import { useUpdateEffect } from 'ahooks'
import flatten from 'lodash/flatten'
import uniq from 'lodash/uniq'
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react'
import { arrToTree } from '../Tree/utils'
import './index.scss'

export interface IHeadlessTreeData {
  title: string
  key: string | number
  parentKey?: string | number
  indexPath?: number[]
  keyPath?: (string | number)[]
  isFolder?: boolean
  isRoot?: boolean
  isLeaf?: boolean
  level?: number
  icon?: string | React.ReactNode
  children?: IHeadlessTreeData[]
  selectable?: boolean
}

type AutoFormatConfig = {
  keyField?: string
  parentField: string
  isRoot?: string | ((node: IHeadlessTreeData) => boolean)
  isLeaf?: string | ((node: IHeadlessTreeData) => boolean)
  /** 当没有发现子节点时，视为叶子节点 */
  isLeafByChildren?: boolean
}

export interface IHeadlessTreeProps {
  datas: IHeadlessTreeData[]
  renderFolder: (props: {
    open: boolean
    data: IHeadlessTreeData
    level: number
    changeOpen: (open: boolean) => void
    drill: (key?: string | number) => void
    selected?: boolean
    partSelected?: boolean
    selectAll: (selected: boolean) => void
    selectedKeys: (string | number)[]
    flattenDatas: IHeadlessTreeData[]
    changeSelected: (selected: boolean) => void
  }) => React.ReactNode
  renderItem: (props: {
    data: IHeadlessTreeData
    level: number
    selectAll: (selected: boolean) => void
    selected?: boolean
    selectedKeys: (string | number)[]
    flattenDatas: IHeadlessTreeData[]
    changeSelected: (selected: boolean) => void
  }) => React.ReactNode
  renderWrapper?: (props: {
    listContent: React.ReactNode[]
    drillPath: (string | number)[]
    drillPathData: IHeadlessTreeData[]
    drillBack: (key?: string | number | null) => void
    selectedKeys: (string | number)[]
    flattenDatas: IHeadlessTreeData[]
  }) => React.ReactNode
  /**
   * 下钻模式，一屏只展示当前层级节点
   */
  drill?: boolean
  onDrill?: (
    drillPath: (string | number)[],
    drillPathData: IHeadlessTreeData[]
  ) => void
  selectedKeys?: (string | number)[]
  /**
   * 在触发 onSelect 之前触发，可以修改 keys
   * @param keys 要选择的节点keys
   * @returns
   */
  onSelectBefore?: (info: {
    selectedKeys: (string | number)[]
    flattenDatas: IHeadlessTreeData[]
  }) => (string | number)[]
  onSelect?: (keys: (string | number)[]) => void
  expandedKeys?: (string | number)[]
  defaultExpandAll?: boolean
  onExpand?: (keys: (string | number)[]) => void
  autoFormat?: AutoFormatConfig
  children?: React.ReactNode | React.ReactNode[]
}

export interface IHeadlessTreeRef {
  /**
   * 返回上级节点
   * @param key 要返回的节点key，如果要范围根节点，则传null
   * @returns
   */
  drillBack: (key?: string | number | null) => void
}

export const HeadlessTree = React.memo(
  forwardRef<IHeadlessTreeRef, IHeadlessTreeProps>(
    (
      {
        datas,
        expandedKeys: propsExpandedKeys,
        defaultExpandAll,
        drill,
        onExpand,
        onDrill,
        renderWrapper,
        renderFolder,
        renderItem,
        autoFormat,
        selectedKeys: propsSelectedKeys,
        onSelectBefore,
        onSelect,
      },
      ref
    ) => {
      const [expandedKeys, setExpandedKeys] = useState<(string | number)[]>(
        () => {
          if (propsExpandedKeys) return propsExpandedKeys

          if (defaultExpandAll)
            return flattenDeepByField(datas, 'children').map(
              (item: IHeadlessTreeData) => item.key
            )

          return []
        }
      )
      const [drillPath, drillPathRef, setDrillPath] = useRefState<
        (string | number)[]
      >([])
      useUpdateEffect(() => {
        setExpandedKeys(propsExpandedKeys || [])
      }, [propsExpandedKeys])

      const [selectedKeys, setSelectedKeys] = useState<(string | number)[]>(
        propsSelectedKeys || []
      )
      const [partSelectedKeys, setPartSelectedKeys] = useState<
        (string | number)[]
      >([])

      useUpdateEffect(() => {
        setSelectedKeys(propsSelectedKeys || [])
      }, [propsSelectedKeys])

      // 所有的平铺数据
      const flattenData = useMemo(() => {
        let treeData = autoFormat
          ? // @ts-ignore
            arrToTree(datas || [], autoFormat)
          : datas || []

        treeData = deepMap(treeData, 'children', (item, parent, indexPath) => {
          return {
            ...item,
            level: indexPath.length,
            isFolder: !item.isLeaf,
            parentKey: parent?.key,
            indexPath,
          }
        })

        let flattenData = flattenDeepByField(
          treeData,
          'children'
        ) as IHeadlessTreeData[]

        return flattenData || []
      }, [datas, autoFormat, expandedKeys, drillPath, drill])

      const flattenDataDic = useMemo(() => {
        return arr2Dic(flattenData, 'key')
      }, [flattenData])

      const onSelectHandler = (keys: (string | number)[]) => {
        let _keys = [...keys]
        if (onSelectBefore)
          _keys = onSelectBefore({
            selectedKeys: _keys,
            flattenDatas: flattenData,
          })
        setSelectedKeys(_keys)
        onSelect?.(_keys)
      }

      useEffect(() => {
        const folderKeys = uniq(
          flatten(selectedKeys.map((key) => flattenDataDic[key]?.keyPath || []))
        ).filter((key) => flattenDataDic[key]?.isFolder)

        let partSelectedKeys: (string | number)[] = []
        folderKeys.forEach((key) => {
          const childs = flattenData.filter((item) =>
            item.keyPath?.slice(0, -1)?.includes(key)
          )
          const hasSelected = childs.some((item) =>
            selectedKeys.includes(item.key)
          )
          const allSelected = childs.every((item) =>
            selectedKeys.includes(item.key)
          )
          if (hasSelected && !allSelected) partSelectedKeys.push(key)
        })

        setPartSelectedKeys(partSelectedKeys)
      }, [selectedKeys, flattenData])

      // 用于渲染的平铺数据
      const renderData = useMemo(() => {
        if (drill) {
          const parentKey = drillPath[drillPath.length - 1]
          return flattenData.filter((item) => item.parentKey === parentKey)
        }

        // 根据展开的节点过滤数据
        return flattenData.filter((item) => {
          if (item.level === 1) return true
          const parentKeys = item.keyPath || []
          return parentKeys
            .slice(0, -1)
            .every((key) => expandedKeys.includes(key))
        })
      }, [flattenData])

      const drillPathData = useMemo(() => {
        return drillPath.map((key) => flattenDataDic[key])
      }, [flattenDataDic, drillPath])

      const drillBack = (key?: string | number | null) => {
        if (key === null) {
          setDrillPath([])
          return
        }
        const drillPath = drillPathRef.current
        const index = key ? drillPath.findIndex((k) => k === key) + 1 : -1
        setDrillPath(drillPath.slice(0, index))
      }

      useImperativeHandle(ref, () => ({
        drillBack,
      }))

      const onChangeSelectedHandler = (
        item: IHeadlessTreeData,
        selected: boolean
      ) => {
        if (item.selectable === false) return

        let newSelectedKeys: (string | number)[] = []

        // 当选择某项时，需要处理的逻辑
        // 1. 如果当前节点为文件夹，需要找到其所有子节点，并设置其选中状态
        let childKeys: (string | number)[] = []
        if (item.isFolder) {
          childKeys = flattenData
            .filter(
              (child) =>
                child.key !== item.key &&
                child.keyPath?.slice(0, -1)?.includes(item.key) &&
                child.selectable !== false
            )
            .map((item) => item.key)
        }

        // 如果是选择操作，需要将其子节点也选中
        // 然后检查同级节点是否都被选中了，如果是，则需要将父级也选中，然后再递归检查父级的父级
        if (selected) {
          newSelectedKeys = [...selectedKeys, item.key, ...childKeys]

          let parentKey = item.parentKey
          while (parentKey !== undefined) {
            // 同级的 key
            const siblingKeys = flattenData
              .filter(
                (data) =>
                  data.parentKey === parentKey && data.selectable !== false
              )
              .map((data) => data.key)
            // 同级中是否全部选中
            const allSelected = siblingKeys.every((key) =>
              newSelectedKeys.includes(key)
            )
            if (allSelected) {
              newSelectedKeys.push(parentKey)
              parentKey = flattenDataDic[parentKey]?.parentKey
            } else {
              parentKey = undefined
            }
          }
        } else {
          newSelectedKeys = selectedKeys.filter(
            (key) =>
              // 排除子节点
              !childKeys.includes(key) &&
              // 排除当前节点和父节点
              !item.keyPath?.includes(key)
          )
        }

        onSelectHandler(newSelectedKeys)
      }

      const onSelectAllHandler = (checked?: boolean) => {
        if (checked === false) {
          onSelectHandler([])
          return
        }
        onSelectHandler(
          flattenData
            .filter((item) => item.selectable !== false)
            .map((item) => item.key)
        )
      }

      const listContent = renderData.map((item) => {
        let selected = selectedKeys.includes(item.key)
        if (item.isFolder) {
          const partSelected = !selected && partSelectedKeys.includes(item.key)
          return renderFolder({
            open: expandedKeys.includes(item.key),
            data: item,
            level: item.level || 0,
            selected,
            partSelected,
            selectedKeys,
            flattenDatas: flattenData,
            selectAll: onSelectAllHandler,
            drill: (key?: string | number) => {
              const newDrillPath = [...drillPath, key || item.key]
              setDrillPath(newDrillPath)
              const flattenDataDic = arr2Dic(flattenData, 'key')
              onDrill?.(
                newDrillPath,
                newDrillPath.map((key) => flattenDataDic[key])
              )
            },
            changeOpen: (open: boolean) => {
              const newExpandedKeys = open
                ? [...expandedKeys, item.key]
                : expandedKeys.filter((key) => key !== item.key)
              setExpandedKeys(newExpandedKeys)
              onExpand?.(newExpandedKeys)
            },
            changeSelected: (selected: boolean) => {
              onChangeSelectedHandler(item, selected)
            },
          })
        }

        // 如果为普通节点
        return renderItem({
          data: item,
          level: item.level || 0,
          selected,
          selectedKeys,
          flattenDatas: flattenData,
          selectAll: onSelectAllHandler,
          changeSelected: (selected: boolean) => {
            onChangeSelectedHandler(item, selected)
          },
        })
      })

      if (renderWrapper) {
        return (
          <>
            {renderWrapper({
              listContent,
              drillPath,
              drillPathData,
              drillBack,
              selectedKeys,
              flattenDatas: flattenData,
            })}
          </>
        )
      }

      return <>{listContent}</>
    }
  )
)

export default HeadlessTree
