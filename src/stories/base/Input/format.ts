import { IInputProps } from "./input";

export function number(value: string) {
  return value.match(/\d+\.?\d{0,2}/)
}

function removeLeadingZeros(str: string) {
  if (str.includes('.')) {
    let [num, other] = str?.split('.') || []

    if (num.length > 1) {
      // 使用正则表达式替换开头的零
      num = num.replace(/^0+/, '');
    }
    return `${num}.${other}`;
  } else {
    if (str.length > 1) {
      // 使用正则表达式替换开头的零
      str = str.replace(/^0+/, '');
    }
    return str;
  }
}

export function format(value: string, valueFormat: IInputProps['valueFormat'], maxDecimalBit?: number) {
  if (!valueFormat) return value
  const formats = {
    number(inputValue: string) {
      const decimalBit = (typeof maxDecimalBit === 'number' ? maxDecimalBit : 2) + '';
      const value = removeLeadingZeros(inputValue.replace('-', '')).replace(/,/g, '')
      const reg = new RegExp("\\d+\\.?\\d{0," + decimalBit + "}")
      const res = value.match(reg)
      const result = res ? res.join('') : ''
      return result
    }
  }
  return formats[valueFormat] ? formats[valueFormat](value) : value
}

export function changeFormat(value: string, valueFormat: IInputProps['valueFormat'], maxDecimalBit?: number) {
  if (!valueFormat) return value
  const formats = {
    number(inputValue: string) {
      // 必须传字符串 不然不生效 我吐了
      const decimalBit = (typeof maxDecimalBit === 'number' ? maxDecimalBit : 2) + '';
      const reg = new RegExp("\\d+\\.?\\d{0," + decimalBit + "}")
      const res = inputValue.match(reg)
      const result = res ? res.join('') : ''
      return result
    }
  }
  return formats[valueFormat] ? formats[valueFormat](value) : value
}

export function preview(value: string, valueFormat: IInputProps['valueFormat']) {
  if (!valueFormat) return value
  const formats = {
    // number(inputValue: string) {
    //   const isDot = /\.$/.test(inputValue)
    //   return inputValue ? parseFloat(inputValue).toLocaleString() + (isDot ? '.' : '') : ''
    // }
  }
  // @ts-ignore
  return formats[valueFormat] ? formats[valueFormat](value) : value
}