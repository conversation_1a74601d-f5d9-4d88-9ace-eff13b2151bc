import { useSize } from 'ahooks'
import classNames from 'classnames'
import React, {
  FC,
  ReactNode,
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
  LegacyRef,
  RefAttributes,
  useMemo,
} from 'react'
import Tooltip from '../Tooltip/index'
import './index.scss'
import { Handler } from './types'
import { valuerIsEmpty } from './utils'
import { changeFormat, format, preview } from './format'
import { getLocale } from '@tita/utils'
import { ITooltipProps } from '../Tooltip/tooltip'
import isNil from 'lodash/isNil'

export interface IInputProps
  extends Omit<
    React.DetailedHTMLProps<
      React.InputHTMLAttributes<HTMLInputElement>,
      HTMLInputElement
    >,
    'size' | 'onChange' | 'onFocus' | 'onBlur' | 'onSubmit'
  > {
  mode?: 'edit'
  /**
   * 默认没有左右 padding，鼠标 hover 之后才展示
   */
  autoPaddingX?: boolean
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  errored?: boolean
  placeholder?: string
  arrangeType?: 'row' | 'column'
  valueFormat?: 'number'
  errorTip?: string
  fontSize?: number
  autoResize?: boolean
  maxLength?: number
  /**
   * 限制输入最大值，当 type 为 number 时才生效
   */
  max?: number
  /**
   * 限制输入最小值，当 type 为 number 时才生效
   */
  min?: number
  /**
   * 限制只能输入整数，当 type 为 number 时才生效
   */
  integer?: boolean
  value?: string
  initialValue?: string
  onChange?: Handler
  title?: string
  /** 错误提示的层级 */
  errorTooltipZIndex?: number
  disabled?: boolean
  onEnter?: Handler
  onBlur?: Handler
  blurShowTips?: boolean
  onFocus?: Handler
  emptyBack?: boolean
  autoFocus?: boolean
  size?: 'large' | 'small' | 'little' | 'big'
  bordered?: boolean
  disableAutoResizeDefaultWidth?: boolean
  /** 以毫秒为单位 */
  delay?: number
  prefixContent?: React.ReactNode | string
  suffixContent?: React.ReactNode | string
  tips?: React.ReactNode | string
  tipsPlacement?: ITooltipProps['placement']
  /** 是否禁用滚轮改变事件，type=number 时配置生效
   * @default false
   */
  disableWheelChange?: boolean
  /** 是否使用内部的renderValue */
  formattingValue?: (value: string) => string
  overlayInnerStyle?: React.CSSProperties
  disableBlurFormat?: boolean
  maxDecimalBit?: number
  showTooltip?: boolean
  allowUndefined?: boolean
  getTooltipContainer?: (node: HTMLElement) => HTMLElement
}

const preCls = 'tita-ui--input'

export interface IInputRef {
  autoFocus: any
  blur: () => void
  focus: () => void
  // getRef: ()=>
}

export const Input: React.ForwardRefExoticComponent<
  Omit<IInputProps & RefAttributes<IInputRef | HTMLInputElement>, 'ref'>
> = forwardRef((props, ref) => {
  const text = () => {
    return <div className=''></div>
  }
  const {
    className = '',
    style = {},
    placeholder = getLocale('Pro_page_Plan_Pleasenters') || '请输入',
    errorTip = '',
    mode,
    value,
    valueFormat,
    initialValue,
    onChange,
    title = '',
    maxLength = 500,
    max,
    min,
    integer,
    arrangeType = 'row',
    autoPaddingX,
    errorTooltipZIndex,
    disabled = false,
    fontSize,
    autoResize,
    onEnter,
    onFocus,
    onBlur,
    blurShowTips,
    onWheel,
    errored = false,
    emptyBack = false,
    autoFocus = false,
    allowUndefined,
    size,
    bordered = true,
    delay,
    prefixContent,
    suffixContent,
    tips,
    tipsPlacement,
    disableWheelChange = false,
    overlayInnerStyle,
    disableBlurFormat,
    formattingValue,
    maxDecimalBit,
    showTooltip = false,
    /** 自适应模式下，禁止增加默认的宽度 */
    disableAutoResizeDefaultWidth,
    getTooltipContainer,
    ...rest
  } = props
  const [renderValue, setRenderValue] = useState<string>(
    initialValue || value || ''
  )

  const oldValueRef = useRef<string>(initialValue || value || '')
  // @ts-ignore
  const timerRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (value !== undefined || allowUndefined) setRenderValue(value)
  }, [value])

  const inputRef: LegacyRef<HTMLInputElement> = useRef<HTMLInputElement>(null)

  const autoFocusFunction = () => {
    timerRef.current = setTimeout(() => {
      inputRef.current?.focus()
    }, 300)
  }

  useImperativeHandle(ref, () => ({
    autoFocus: () => autoFocusFunction(),
    blur: () => inputRef.current?.blur(),
    focus: () => inputRef.current?.focus(),
    getRef: () => inputRef.current,
  }))

  useEffect(() => {
    if (autoFocus) autoFocusFunction()
    return () => clearTimeout(timerRef.current)
  }, [autoFocus])

  // changeHandler
  let timeId: any = null
  const changeHandler: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    // setTipsVisible(false)
    let inputValue = changeFormat(e.target.value, valueFormat, maxDecimalBit)
    if (
      (props.type === 'number' || valueFormat === 'number') &&
      !isNil(max) &&
      Number(inputValue) > max
    )
      return
    if (
      ((props.type === 'number' || valueFormat === 'number') &&
        !isNil(min) &&
        Number(inputValue) < min) ||
      ((props.type === 'number' || valueFormat === 'number') &&
        !isNil(min) &&
        min >= 0 &&
        inputValue.startsWith('-'))
    )
      return

    // 如果类型为 number，并且限制只能输入整数
    if ((props.type === 'number' || valueFormat === 'number') && integer) {
      inputValue = inputValue.replace('.', '')
    }

    const updateValue = () => {
      if (formattingValue) {
        const newValue = formattingValue?.(inputValue)
        onChange && onChange(newValue || '')
        setRenderValue(newValue || '')
      } else {
        onChange && onChange(inputValue)
        setRenderValue(inputValue)
      }
    }

    if (delay) {
      if (timeId) clearTimeout(timeId)
      timeId = setTimeout(() => {
        updateValue()
        clearTimeout(timeId)
      }, delay)
    } else {
      updateValue()
    }
  }

  // 处理空数据和错误数据
  const handErrorAndEmpty = (
    inputValue: string,
    callBack: Handler = () => {}
  ) => {
    if (valuerIsEmpty(inputValue) && emptyBack)
      return setRenderValue(oldValueRef.current) // 错误时的具体的处理逻辑
    setRenderValue(inputValue)
    oldValueRef.current = inputValue
    callBack(inputValue)
  }

  const [tipsVisible, setTipsVisible] = useState<boolean>(false)
  const blurHandler: React.FocusEventHandler<HTMLInputElement> = (e) => {
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    setTipsVisible(false)
    const inputValue = disableBlurFormat
      ? e.target.value
      : format(e.target.value, valueFormat, maxDecimalBit)
    handErrorAndEmpty(inputValue, onBlur)
  }
  useEffect(() => {
    if (blurShowTips && errored) {
      setTipsVisible(true)
    }
  }, [errored, blurShowTips])
  const focusHandler: React.FocusEventHandler<HTMLInputElement> = (e) => {
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    setTipsVisible(true)
    const inputValue = format(e.target.value, valueFormat, maxDecimalBit)
    if (onFocus) onFocus(inputValue)
  }
  const enterPressHandler: React.KeyboardEventHandler<HTMLInputElement> = (
    e
  ) => {
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    if (e.key !== 'Enter') return
    // @ts-ignore
    const inputValue = format(e.target.value, valueFormat, maxDecimalBit)
    handErrorAndEmpty(inputValue, onEnter)
  }

  const wheelHandler: React.WheelEventHandler<HTMLInputElement> = (e) => {
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    if (disableWheelChange) {
      inputRef.current?.blur()
    }
    if (onWheel) onWheel(e)
  }

  const containerRef = useRef() as React.MutableRefObject<HTMLDivElement>
  const containerRefSize = useSize(containerRef)
  const containerWidth = (containerRefSize && containerRefSize.width) || 0

  const contentRef = useRef() as React.MutableRefObject<HTMLDivElement>
  const contentRefSize = useSize(contentRef)
  const contentWidth = (contentRefSize && contentRefSize.width) || 0

  let inputNode = (
    <input
      className={classNames(
        preCls,
        { [`${preCls}--disabled`]: disabled },
        { [`${preCls}--autoResize`]: autoResize },
        { [`${preCls}--autoPaddingX`]: autoPaddingX },
        { [`${preCls}--mode-${mode}`]: mode },
        { [`${preCls}-size-${size}`]: size },
        { [`${preCls}-noBorder`]: bordered === false },
        { [`${preCls}-error`]: bordered && errored },
        className
      )}
      style={{
        ...style,
        ...(autoResize
          ? {
              width: `${
                contentWidth + (disableAutoResizeDefaultWidth ? 1 : 30)
              }px`,
              maxWidth:
                contentWidth > containerWidth ? containerWidth : undefined,
            }
          : {}),
      }}
      placeholder={placeholder}
      value={preview(renderValue, valueFormat)}
      // onChange={changeHandler}
      onInput={changeHandler}
      maxLength={maxLength}
      disabled={disabled}
      onFocus={focusHandler}
      onBlur={blurHandler}
      onKeyDown={onEnter && enterPressHandler}
      onWheel={wheelHandler}
      ref={inputRef}
      {...rest}
    />
  )

  const node = autoResize ? (
    <div
      className={`${preCls}__container`}
      style={
        autoResize
          ? {
              width: `${
                contentWidth + (disableAutoResizeDefaultWidth ? 0 : 10)
              }px`,
            }
          : undefined
      }
      ref={containerRef}
    >
      {inputNode}
      <span
        className={classNames(
          `${preCls}--resize`,
          { [`${preCls}-disable`]: disabled },
          { [`${preCls}--mode-${mode}`]: mode },
          { [`${preCls}-size-large`]: size === 'large' },
          { [`${preCls}-size-small`]: size === 'small' },
          { [`${preCls}-noBorder`]: bordered === false },
          className
        )}
        style={style}
        ref={contentRef}
      >
        {renderValue}
      </span>
    </div>
  ) : (
    inputNode
  )

  let inputInner = node

  if (prefixContent || suffixContent) {
    inputInner = (
      <div className={`${preCls}__wrapper`}>
        <div className={`${preCls}__prefix`}>{prefixContent}</div>
        {node}
        <div className={`${preCls}__suffix`}>{suffixContent}</div>
      </div>
    )
  }

  if (errorTip || tips || showTooltip) {
    return (
      // @ts-ignore
      <Tooltip
        getTooltipContainer={getTooltipContainer}
        visible={!!errored || (!!tips && tipsVisible)}
        overlay={errorTip || tips}
        placement={tipsPlacement || 'topLeft'}
        zIndex={errorTooltipZIndex}
        align={{ offset: [0, 8] }}
        overlayInnerStyle={{
          boxShadow: '0 0 15px 0 rgba(0,0,0,.1)',
          color: '#3F4755',
          ...overlayInnerStyle,
        }}
        overlayClassName={classNames('input-tooltip', {
          ['input-error-tooltip']: errored,
        })}
      >
        {inputInner}
      </Tooltip>
    )
  }

  return inputInner
})

export default React.memo(Input)
