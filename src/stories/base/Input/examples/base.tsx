import React, { FC, useState, useCallback } from 'react'
import { Input, IInputProps, TextArea } from '@/stories/base/Input'
import { Status, TextAreaType } from '../text-area';
import { StringAndNumber, Handler } from '../types';
import './base.scss'
import Panel from '../../Panel';

export interface IBaseProps extends IInputProps {
}

const Base: FC<IBaseProps> = (props) => {

  const [inputErrored, setInputErrored] = useState<boolean>(false)

  const [textareaStatus, setTextareaStatus] = useState<Status>('normal')

  const [inputValue, setInputValue] = useState<string | number>()
  const inputChangeHandler: Handler = useCallback((value: StringAndNumber = 0) => {
    setInputValue(value)
  }, [inputValue])

  const enterHandler: Handler = useCallback((value: StringAndNumber = 0) => {
  }, [props])
  const blurHandler: Handler = useCallback((value: StringAndNumber = 0) => {
    setInputErrored(value?.toString() == '111')
  }, [])

  // TODO: 对于错误状态的控制略显复杂
  const textAreaOnchange = (value?: StringAndNumber) => {
    setInputValue(value)
    // if (value === '1111') return setTextareaStatus('error')
    // setTextareaStatus('normal')
  }

  const [textType, setTextType] = useState<TextAreaType>('show')
  const [hasTextAreaBorder, setHasTextAreaBorder] = useState(true)
  const textAreaOnClick = () => {
    setTextType('small')
    setHasTextAreaBorder(false)
  }

  return (
    <div className='flex flex-col space-y-10px'>
      <Panel title='基础用法' className='border'>
        <Input placeholder='请输入' />
      </Panel>
      <Panel title='文本状态、可编辑、自适应宽度' className='border'>
        <Input placeholder='请输入' autoResize mode="edit" value="哈哈哈哈" />
      </Panel>
      <Input
        title='输入框标题'
        value={inputValue}
        onChange={inputChangeHandler}
        onEnter={enterHandler}
        onBlur={blurHandler}
        errored={inputErrored}
        errorTip='错了aaaaaaaa'
        size='large'
        bordered={false}
        type='number'
      />
      <TextArea
        errorMessage="出错了"
        cols={70}
        value={inputValue}
        onChange={textAreaOnchange}
        status={textareaStatus}
        placeholder={'请输入'}
        bordered={hasTextAreaBorder}
        onClick={textAreaOnClick}
        autoStretch={true}
        maxLength={10}
        // TODO: 有缺陷，只能单向撑高不能缩回去
        autoSize={{
          minRows: 0,
          maxRows: 3
        }}
      />
    </div>)
}

export default React.memo(Base)
