import classNames from 'classnames';
import RcTextArea from 'rc-textarea';
import type { AutoSizeType, TextAreaProps as RcTextAreaProps, TextAreaRef } from 'rc-textarea/lib/interface';
import React, { FC, forwardRef, useEffect, useState } from 'react';
import './index.scss';
import { Handler, StringAndNumber } from './types';
import { valuerIsEmpty } from './utils';
import { getLocale } from '@tita/utils';


export type Status = 'normal' | 'error'

export type TextAreaType = "default" | "show" | "small"

export interface ITextAreaProps extends Omit<RcTextAreaProps, 'suffix' | 'onChange' | 'onBlur' | 'onPressEnter' | 'onClick'> {
  mode?: 'edit'
  /**
   * 默认没有左右 padding，鼠标 hover 之后才展示
   */
  autoPaddingX?: boolean
  className?: string
  initialValue?: StringAndNumber
  value?: StringAndNumber
  style?: React.CSSProperties
  errorMessage?: string
  status?: Status
  onChange?: Handler
  onBlur?: Handler
  onPressEnter?: Handler
  onClick?: Handler
  emptyBack?: boolean
  bordered?: boolean
  disabled?: boolean
  type?: TextAreaType
  placeholder?: string
  autoSize?: boolean | AutoSizeType
  autoStretch?: boolean
  rowStretchTo?: number
  /** 回车换行 */
  enterWrapDisabled?: boolean
  maxLength?: number
}

enum textareaType {
  default = 'titaui--textArea-defaultType',
  show = 'titaui--textArea-showType',
  small = 'titaui--textArea-smallType',
}

const preCls = 'tita-ui-textArea'

export const TextArea: FC<ITextAreaProps> = React.memo(
  forwardRef<TextAreaRef, ITextAreaProps>((props, ref) => {
  const {
    className,
    style,
    errorMessage,
    mode,
    autoPaddingX,
    status = 'normal',
    onChange,
    onPressEnter,
    onClick,
    onBlur,
    initialValue,
    value,
    disabled,
    emptyBack,
    bordered,
    type = 'default',
    placeholder = getLocale('Pro_page_Plan_Pleasenters'),
    autoSize,
    rowStretchTo = 3,
    autoStretch = false,
    enterWrapDisabled = false,
    maxLength,
    ...other
  } = props

  const [renderValue, setRenderValue] = useState(initialValue || value)
  const [oldValue, setOldValue] = useState(value)
  const [textAreaType, setTextAreaType] = useState<TextAreaType>(type)
  const [isComposing, setIsComposing] = useState(false)

  useEffect(() => {
    if (value !== undefined) setRenderValue(value)
  }, [value])

  const changeHandler: React.ChangeEventHandler<HTMLTextAreaElement> = (e) => {
    const { value: inputValue } = e.target
    if (typeof maxLength === 'number' && !isComposing) {
      const v = inputValue.length <= maxLength ? inputValue : inputValue.slice(0, maxLength);
      setRenderValue(v)
      if (onChange) onChange(v)
    } else {
      setRenderValue(inputValue)
      if (onChange) onChange(inputValue)
    }
  }

  const handleCompositionStart = () => {
    setIsComposing(true)
  }

  const handleCompositionEnd = (e: React.CompositionEvent<HTMLTextAreaElement>) => {
    setIsComposing(false)
    // 在组合结束后应用maxLength限制
    if (typeof maxLength === 'number') {
      const inputValue = e.target.value
      const v = inputValue.length <= maxLength ? inputValue : inputValue.slice(0, maxLength)
      setRenderValue(v)
      if (onChange) onChange(v)
    }
  }

  const keyDownHandler: React.KeyboardEventHandler<HTMLTextAreaElement> = (e) => {
    if (e.key !== 'Enter' ) return
    if (enterWrapDisabled && e.key === 'Enter') e.preventDefault()
    const { value: inputValue } = e.target as HTMLTextAreaElement
    if (onPressEnter) onPressEnter(inputValue)
  }

  const clickHandler: React.MouseEventHandler<HTMLTextAreaElement> = (e) => {
    if (autoStretch) setTextAreaType('small')
    if (onClick) onClick('')
  }

  // 失焦保存 为空返填
  const blurHandler: React.FocusEventHandler<HTMLTextAreaElement> = (e) => {
    const { value: inputValue } = e.target
    const isEmpty = valuerIsEmpty(inputValue)
    if (isEmpty) {
      if (emptyBack && oldValue) return setRenderValue(oldValue)
      // 开启伸缩且旧值为空
      if (autoStretch && !oldValue) return setTextAreaType('show')
    }
    setOldValue(inputValue)
    onBlur && onBlur(inputValue.trim())
  }
  return (
    <>
      <RcTextArea
        ref={ref}
        name='textArea'
        className={classNames(
          preCls,
          className,
          {
            [`${preCls}-error`]: status === 'error',
            [`${preCls}-noBorder`]: bordered,
            [`${preCls}--disabled`]: disabled,
            [`${preCls}--mode-${mode}`]: mode,
            [`${preCls}--autoPaddingX`]: autoPaddingX,
            [`${preCls}-type-small`]: textAreaType === 'small',
            [`${preCls}-type-show`]: textAreaType === 'show',
           [ 'border-[#F05E5E]']: status === 'error',
          },
          textareaType[textAreaType]
        )}
        style={style}
        autoSize={autoSize}
        disabled={disabled}
        onChange={changeHandler}
        onCompositionStart={handleCompositionStart}
        onCompositionEnd={handleCompositionEnd}
        onKeyDown={keyDownHandler}
        onBlur={blurHandler}
        value={renderValue}
        onClick={clickHandler}
        placeholder={placeholder}
        {...other}
      />
      {(status === 'error' && errorMessage) && <div className={classNames(`${preCls}-errorMessage`, ' text-[#F05E5E] text-[400] leading-[18px] mt-[-7px] ml-[12px] text-[12px]')}>{errorMessage}</div>}
    </>
  )
}))

export default TextArea
