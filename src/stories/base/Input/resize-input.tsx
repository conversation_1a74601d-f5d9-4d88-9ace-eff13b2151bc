import React, { FC, useState, useEffect, useRef } from 'react'
import classNames from 'classnames'
import Tooltip from '../Tooltip/index';
import './index.scss'
import { Handler, StringAndNumber } from './types';
import { valuerIsEmpty } from './utils';
import { useSize } from 'ahooks';
import { getLocale } from '@tita/utils';


export interface IInputProps extends Omit<React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, 'size' | 'onChange' | 'onFocus' | 'onBlur' | 'onSubmit'> {
  mode?: 'edit'
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  errored?: boolean
  placeholder?: string
  arrangeType?: 'row' | 'column'
  valueFormat?: ''
  errorTip?: string
  fontSize?: number
  autoResize?: boolean
  maxLength?: number
  value?: StringAndNumber
  initialValue?: StringAndNumber
  onChange?: Handler
  title?: string
  disabled?: boolean
  onEnter?: Handler
  onBlur?: Handler
  onFocus?: Handler
  emptyBack?: boolean
  autoFocus?: boolean
  size?: 'default' | 'large' | 'small'
  bordered?: boolean
}

const preCls = 'tita-ui--input'

export const Input: FC<IInputProps> = React.memo((props) => {

  const text = () => {
    return (
      <div className=''></div>
    )
  }
  const {
    className = '',
    style = {},
    placeholder = getLocale('Pro_page_Plan_Pleasenters'),
    errorTip = '',
    mode,
    value,
    initialValue,
    onChange = () => { },
    title = '',
    maxLength = 500,
    arrangeType = 'row',
    disabled = false,
    fontSize,
    autoResize,
    onEnter,
    onFocus,
    onBlur,
    errored = false,
    emptyBack = false,
    autoFocus = false,
    size = 'default',
    bordered = true,
    ...rest
  } = props
  const [renderValue, setRenderValue] = useState<StringAndNumber>(initialValue || value || '')

  const oldValueRef = useRef<StringAndNumber>(initialValue || value)

  useEffect(() => {
    if (value !== undefined) setRenderValue(value)
  }, [value])

  const inputRef: React.RefObject<HTMLInputElement> = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (autoFocus) inputRef.current?.focus()
  }, [autoFocus])

  // changeHandler
  const changeHandler: React.ChangeEventHandler = (e: React.ChangeEvent) => {
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    const { value: inputValue = '' } = e.target as HTMLInputElement
    setRenderValue(inputValue)
    onChange && onChange(inputValue)
  }

  // 处理空数据和错误数据
  const handErrorAndEmpty = (inputValue: StringAndNumber, callBack: Handler = () => { }) => {
    if (valuerIsEmpty(inputValue) && emptyBack) return setRenderValue(oldValueRef.current as StringAndNumber)// 错误时的具体的处理逻辑
    setRenderValue(inputValue)
    oldValueRef.current = inputValue
    callBack(inputValue)
  }

  // TODO: 失焦后错误提示 ToolTip 样式出错
  const blurHandler: React.FocusEventHandler = (e: React.FocusEvent) => {
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    const { value: inputValue = '' } = e.target as HTMLInputElement
    handErrorAndEmpty(inputValue, onBlur)
  }
  const focusHandler: React.FocusEventHandler = (e: React.FocusEvent) => {
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    const { value: inputValue = '' } = e.target as HTMLInputElement
    if (onFocus) onFocus(inputValue)
  }
  const enterPressHandler: React.KeyboardEventHandler = (e) => {
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    if (e.key !== 'Enter') return
    const { value: inputValue = '' } = e.target as HTMLInputElement
    handErrorAndEmpty(inputValue, onEnter)
  }

  const containerRef = useRef() as React.MutableRefObject<HTMLDivElement>
  const containerRefSize = useSize(containerRef)
  const containerWidth = containerRefSize && containerRefSize.width || 0

  const contentRef = useRef() as React.MutableRefObject<HTMLDivElement>
  const contentRefSize = useSize(contentRef)
  const contentWidth = contentRefSize && contentRefSize.width || 0

  return (
    <div className={`${preCls}__container`} style={autoResize ? { width: `${contentWidth + 10}px` } : undefined} ref={containerRef}>
      <input
        type='text'
        className={classNames(
          preCls,
          { [`${preCls}-disable`]: disabled },
          { [`${preCls}--mode-${mode}`]: mode },
          { [`${preCls}-size-large`]: size === 'large' },
          { [`${preCls}-size-default`]: size === 'default' },
          { [`${preCls}-size-small`]: size === 'small' },
          { [`${preCls}-noBorder`]: bordered === false },
          className,
        )}
        style={{
          ...style,
          ...(autoResize ? { width: `${contentWidth + 10}px`, maxWidth: contentWidth > containerWidth ? containerWidth : undefined } : {})
        }}
        placeholder={placeholder}
        value={renderValue}
        onChange={changeHandler}
        maxLength={maxLength}
        disabled={disabled}
        onFocus={focusHandler}
        onBlur={blurHandler}
        onKeyDown={onEnter && enterPressHandler}
        ref={inputRef}
        {...rest}
      />
      <span 
        className={classNames(
          `${preCls}--resize`,
          { [`${preCls}-disable`]: disabled },
          { [`${preCls}--mode-${mode}`]: mode },
          { [`${preCls}-size-large`]: size === 'large' },
          { [`${preCls}-size-default`]: size === 'default' },
          { [`${preCls}-size-small`]: size === 'small' },
          { [`${preCls}-noBorder`]: bordered === false },
          className,
        )}
        style={style} ref={contentRef}>{renderValue}</span>
    </div>
  )
})

export default React.memo(Input)