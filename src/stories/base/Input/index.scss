.tita-ui--input {
  height: 36px;
  background: #ffffff;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  border-radius: 8px;
  border: 1px solid #dfe3ea;
  outline: none;
  box-sizing: border-box;
  transition: background-color 0.3s, border .3s;
  &::placeholder{
    color: #bfc7d5;
  }

  &:hover {
    border: 1px solid #2879ff;
  }
  &:focus {
    background-color: #fff;
    border-color: #2879ff !important;
  }

  &__container {
    position: relative;
    display: inline-block;
    max-width: 100%;
    overflow: visible;
  }
  &--autoResize {
    text-overflow: ellipsis;
  }
  &--resize {
    width: auto;
    white-space: nowrap;
    display: inline-block;
    position: absolute;
    opacity: 0;
    left: 0;
    pointer-events: none;
  }

  &--mode-edit {
    border-color: transparent;

    &:hover {
      background-color: #f0f4fa;
      border-color: transparent !important;
    }
    &:focus {
      background-color: #fff;
      border-color: #2879ff !important;
    }
  }
  &--mode-edit.tita-ui--input--disabled {
    border-color: transparent;
    background-color: #fff;

    &:hover {
      background-color: #fff !important;
      border-color: transparent !important;
    }
    &:focus {
      background-color: #fff !important;
      border-color: transparent !important;
    }
  }

  &__title {
    margin-right: 12px;
    font-weight: 400;
    color: #89919f;
    white-space: nowrap;
  }

  &__wrapper {
    display: flex;

    &-row {
      flex-direction: row;
      align-items: center;
    }

    &-column {
      flex-direction: column;
    }
  }

  &-noBorder {
    border: unset;

    &:hover {
      border: unset;
    }
  }
  &-error {
    border: 1px solid #f05e5e !important;
    &:hover {
      border: 1px solid #f05e5e !important;
    }
    &:focus{
      border: 1px solid #f05e5e !important;
    }
  }

  &--disabled {
    background: #f7f8fa;
    border: 1px solid #dfe3ea;
    cursor: not-allowed;

    &:hover {
      border: 1px solid #dfe3ea;
    }
  }

  &-size {
    &-big {
      padding: 0 12px;
      font-size: 16px;
      height: 48px;
      line-height: 24px;
      font-weight: 500;
    }
    &-large {
      padding: 0 12px;
      font-size: 16px;
      height: 40px;
      line-height: 24px;
      font-weight: 500;
    }

    &-small {
      padding: 7px 10px;
      font-size: 14px;
      line-height: 22px;
      font-weight: 400;
    }
    &-little {
      padding: 5px 8px;
      font-size: 14px;
      height: 32px;
      line-height: 22px;
      font-weight: 400;
    }
  }

  &__wrapper{
    display: flex;
    align-items: center;
  }

  &__suffix, &__prefix {
    font-size: 14px;
    color: #83898f;
  }

  &__prefix{
    transform: translateX( calc(100% + 8px) );
  }

  &__suffix {
    transform: translateX( calc(-100% - 8px) );
  }

}

.tita-ui--input.tita-ui--input--autoPaddingX {
  padding-left: 0;
  padding-right: 0;
  transition: all .3s;

  &:hover:not(.tita-ui--input--disabled) {
    padding-left: 12px;
    padding-right: 12px;
  }
  &:focus {
    padding-left: 12px;
    padding-right: 12px;
  }
}

.input-error-tooltip, .input-tooltip {
  .rc-tooltip-content {
    .rc-tooltip-inner {
      background-color: #ffffff;
      color: #141c28!important;
      font-weight: 400;
      box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.17);
    }

    .rc-tooltip-arrow {
      background-color: transparent;
    }

    .rc-tooltip-arrow {
      left: calc(50% - 2px);
      border-top-color: #fff;
      bottom: 5px;
    }
  }
}

.input-error-tooltip.rc-tooltip-placement-topLeft .rc-tooltip-arrow {
  left: 15%;
}

.input-error-tooltip{
  .rc-tooltip-content {
      .rc-tooltip-inner {
        color: #f05e5e !important;
      }
  
    }
}

.tita-ui-textArea {
  outline: none;
  resize: none;
  width: 100%;
  font-size: 14px;
  line-height: 22px;
  padding: 4px 12px;
  border: 1px solid #dfe3ea;
  background: #fff;
  border-radius: 8px;
  box-sizing: border-box;
  transition: background-color 0.3s, border .3s, padding 0.3s;

  &--autoPaddingX {
    padding-left: 0;
    padding-right: 0;

    &:hover {
      padding-left: 12px;
      padding-right: 12px;
    }
    &:focus {
      padding-left: 12px;
      padding-right: 12px;
    }
  }
  &--mode-edit {
    border-color: transparent;

    &:hover {
      background-color: #f0f4fa !important;
      border-color: transparent !important;
    }
    &:focus {
      background-color: #fff !important;
      border-color: #2879ff !important;
    }
  }

  &:hover {
    border: 1px solid #2879ff;
  }

  &-error {
    border: 1px solid #f05e5e !important;
  }

  &-noBorder {
    border: 1px solid #dfe3ea00;

    &:hover {
      border: 1px solid #dfe3ea00;
    }
  }

  &-showType {
    border: 1px solid #dfe3ea00;

    &:hover {
      border: 1px solid #2879ff00;
    }
  }

  &-type {
    &-small {
      width: 460px;
      height: 120px;
      border: 1px solid #2879ff;
    }

    &-show {
      width: 96px;
      height: 30px;
      border: none;
    }
  }
}
.tita-ui-textArea.tita-ui-textArea--mode-edit.tita-ui-textArea--disabled {
  border-color: transparent;
  background-color: #fff;
  pointer-events: none;

  &:hover {
    background-color: #fff;
    border-color: transparent !important;
  }
  &:focus {
    background-color: #fff;
    border-color: transparent !important;
  }
}
.tita-ui-textArea.tita-ui-textArea--disabled {
  border-color: transparent;
  background-color: #fff;
  pointer-events: none;

  &:hover {
    background-color: #fff;
    border-color: transparent !important;
  }

  &:focus {
    background-color: #fff;
    border-color: transparent !important;
  }
}
