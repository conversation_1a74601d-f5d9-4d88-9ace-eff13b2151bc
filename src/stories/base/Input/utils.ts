import { StringAndNumber, Handler } from './types'

type EmptyVerify = (value: string) => boolean

export const valuerIsEmpty: EmptyVerify = (inputValue: StringAndNumber) => {
  if (typeof inputValue === 'number') return false
  if (inputValue?.trim() === '') return true
  return false
}

/**
 * 数字校验方法
 * @param value
 * @returns [newValue, errorTip]
 */
export const handleNumberError = (value: string) => {
  // 不能输入非数字
  const regex = /^\d*\.?\d*$/
  if (!regex.test(value)) return ['', '请输入数字']
  // 仅保留小数点后两位
  const valueStr = value.toString()
  const pointIndex = valueStr.indexOf('.')
  if (pointIndex > -1) {
    const decimal = valueStr.slice(pointIndex + 1)
    if (decimal.length > 2)
      return [valueStr?.substring(0, 4), '仅保留小数点后两位']
    return [valueStr, '']
  }
  return [valueStr, '']
}

/**
 * 数字格式化方法
 * 将传入的数字转化为字符串，保留两位小数
 * @param value
 * @returns number
 */
export const handleNumberFormat = (
  value: number | string,
  decimalLimit = 2,
  integerLimit = 11
) => {
  const decimalSliceIndex = decimalLimit + 1
  const regex = /^\d*\.?\d*$/
  if (!regex.test(value.toString())) return ''

  const valueStr = value.toString()
  const pointIndex = valueStr.indexOf('.')
  let formatValue = value

  // 超过小数点后两位
  if (pointIndex !== -1 && valueStr.length - pointIndex > decimalSliceIndex)
    formatValue = valueStr.slice(0, pointIndex + decimalSliceIndex)

  // 整数位超过十一位
  if (pointIndex > integerLimit || (pointIndex === -1 && valueStr.length > integerLimit)) {
    const integer = valueStr.slice(0, integerLimit)
    const decimal = pointIndex === -1 ? '' : valueStr.slice(pointIndex, pointIndex + decimalSliceIndex)
    formatValue = `${integer}${decimal}`
  }

  return formatValue
}
