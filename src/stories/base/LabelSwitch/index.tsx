import React, { FC, useCallback, useEffect, useState } from 'react'
import classNames from 'classnames'
import './index.scss'
import { Switch } from '../Switch/index';
import { useRefState } from '@tita/hooks';
import { useUpdateEffect } from 'ahooks';

export interface ILabelSwitchProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  /**
   * 标题
   * @editType string
   * @editData 这是标题
   */
  label?: string | React.ReactNode
  checked?: boolean
  onChange?: (checked: boolean) => void
  showSwitch?: boolean
  action?: React.ReactNode
  /** 
   * 开关关闭不显示内容
   * @default false
    */
  hideContent?: boolean
  defaultChecked?: boolean
  /** 禁止关闭 */
  disableClose?: boolean
}

const preCls = 'tita-ui-label-switch'

export const LabelSwitch: FC<ILabelSwitchProps> = React.memo((props) => {

  const { className, style, label, children, onChange, showSwitch = false, hideContent, action, defaultChecked, checked, disableClose } = props

  const [_checked, _checkedRef, setChecked] = useRefState<boolean>(defaultChecked !== undefined ? defaultChecked : checked)
  useUpdateEffect(() => {
    setChecked(!!checked)
  }, [checked])

  const onSwitchChangeHandle = useCallback((checked: boolean) => {
    if (checked || (!checked && !disableClose)) setChecked(checked)
    onChange?.(checked)
  }, [onChange, disableClose])

  return (
    <div className={classNames(preCls, className)} style={style}>
      <div className={`${preCls}__switch`}>
        <div className="flex items-center">
          <span className='text-base text-[500]'>{label}</span>
          {showSwitch && <Switch checked={_checked} onChange={onSwitchChangeHandle} defaultChecked={defaultChecked} />}
        </div>
        <div>{action}</div>
      </div>
      <div className={classNames('pt-20px pl-10px h-full', {
        [`${preCls}__content-hidden`]: !_checked && hideContent,
      })}>
        {children}
      </div>
    </div>
  )
})

export default LabelSwitch
