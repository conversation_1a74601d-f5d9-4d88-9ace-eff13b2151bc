.tita-ui--closeNode {
  &-wrapper {
    display: inline-flex;
    position: relative;
    max-width: 100%;
  }

  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  background: #bfc7d5;
  border-radius: 8px;
  font-size: 12px;
  color: #FFFFFF;
  text-align: center;
  position: absolute;
  cursor: pointer;
  top: -2px;
  right: -2px;
  z-index: 100;
  transition: background-color .3s;

  &:hover {
    background-color: #f05e5e;
  }
  .icon{
    transform: scale(.8);
  }
}
