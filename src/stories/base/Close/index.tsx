import React, { FC, useCallback, useEffect, useState } from 'react'
import { IOffsetNumber, BaseCount, onClose } from './types'
import classNames from 'classnames'
import './index.scss'

export interface ICloseProps {
  wrapperClassName?: string
  className?: string
  /**
   * 关闭图标样式
   * @editType React.CSSProperties
   */
  style?: React.CSSProperties
  /**
   * 关闭图标位置
   * @editType IOffsetNumber
   */
  offset?: IOffsetNumber
  /**
   * 关闭图标大小
   * @editType number ｜ string
   * @default 14
   */
  size?: BaseCount
  /**
   * 关闭图标
   */
  onClose?: onClose
  /**
   * 是否可见
   * @default false
   */
  visible?: boolean
  children?: React.ReactNode
  allowVisible?: boolean
}

const preCls = 'tita-ui--closeNode'
export const Close: FC<ICloseProps> = (props) => {
  const { wrapperClassName, className, onClose, visible, children, allowVisible } =
    props
  const [_visible, setVisible] = useState(false)
  const [_allowVisible, setAllowVisible] = useState(true)

  useEffect(() => {
    if (visible !== undefined) setVisible(visible)
  }, [visible])

  useEffect(() => {
    if (typeof allowVisible === 'boolean') {
      setAllowVisible(allowVisible);
    }
  }, [allowVisible])

  const clickHandler: React.MouseEventHandler = (e) => {
    e.stopPropagation()
    !!onClose && onClose()
  }

  // close样式
  const getCloseStyle = useCallback((): React.CSSProperties => {
    const { style = {}, offset = {}, size = 0 } = props
    const { top = 0, right = 0 } = offset as IOffsetNumber
    const countSize = !!size ? size : 14
    return {
      ...style,
      width: countSize,
      height: countSize,
      lineHeight: countSize,
      transform: `translate(${right}px,  ${top}px)`,
    }
  }, [props])

  if (!onClose) return <>{children}</>

  return (
    <div
      className={`${preCls}-wrapper ${wrapperClassName}`}
      onMouseEnter={() => setVisible(true)}
      onMouseLeave={() => setVisible(false)}
    >
      <div
        className={classNames(preCls, className, 'animation-scale', {
          [`animation-scale-show`]: _visible && _allowVisible,
        })}
        style={{ ...getCloseStyle() }}
        onClick={clickHandler}
      >
        <i className={'tu-icon-canceled icon'} />
      </div>
      {children}
    </div>
  )
}
export default React.memo(Close)
