import React, { FC, ReactNode, useCallback, useState } from 'react'
import Close, { ICloseProps } from '../index';
import './base.scss'
import { onClose } from '../types';

export interface IBaseProps extends ICloseProps { }

const Base: FC<IBaseProps> = (props) => {
  const offset = { top: 0, right: 0 }
  const countStyle = {}

  const closeClickHandler: onClose = useCallback(() => {
  }, [])

  return <Close
    onClose={closeClickHandler}
    offset={offset}
    style={countStyle}>
    <div className='content'>content</div>
  </Close>
}

export default React.memo(Base)
