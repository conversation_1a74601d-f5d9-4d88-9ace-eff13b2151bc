import React, { FC, useRef, useEffect } from 'react'
import classNames from 'classnames'
import { useUpdateEffect } from 'ahooks'
import merge from 'lodash/merge'

import * as echarts from 'echarts/core'
import {
  TooltipComponent,
  TooltipComponentOption,
  LegendComponent,
  LegendComponentOption,
  TitleComponent,
  TitleComponentOption,
} from 'echarts/components'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import type { EChartsType } from 'echarts/types/dist/shared'

import 'echarts-liquidfill'

import './index.scss'
import { getLocale } from '@tita/utils'

interface IData {
  name: string
  value: number
}

export interface ILiquidfillProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  width?: number
  height?: number
  option: ECOption
}

echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer,
  LabelLayout,
])

const preCls = 'tita-ui--liquidfill'

export type ECOption = echarts.ComposeOption<
  TitleComponentOption | TooltipComponentOption | LegendComponentOption
>

export const Liquidfill: FC<ILiquidfillProps> = React.memo(
  ({ className, style, width = 134, height = 134, option = {} }) => {
    const ref = useRef<HTMLElement | null>(null)
    const chartRef = useRef<EChartsType | null>(null)
    useEffect(() => {
      chartRef.current = echarts.init(ref.current as HTMLElement)
      let _option: ECOption = {
        title: {
          // text: (value * 100).toFixed(0) + '{a|%}',
          text: '50%',
          textStyle: {
            fontSize: 20,
            fontFamily: 'PingFangSC-Medium, PingFang SC',
            fontWeight: 'normal',
            color: '#141C28',
            rich: {
              a: {
                fontSize: 20,
              },
            },
          },
          top: '35%',
          left: 'center',
        },
        series: [
          {
            type: 'liquidFill',
            name: getLocale('OKR_MyO_Ew_Completionrate'),
            data: [
              {
                value: 0.5,
                itemStyle: {
                  opacity: 0.16,
                  color: 'rgba(240,94,94,1)',
                },
              },
              {
                value: 0.4,
                itemStyle: {
                  color: '#F05E5E',
                },
              },
            ],
            itemStyle: {
              shadowBlur: 0,
              opacity: 0.4,
            },
            backgroundStyle: {
              color: '#fff',
              opacity: 0.16,
            },
            outline: {
              borderDistance: -10,
              itemStyle: {
                color: 'none',
                borderColor: 'rgba(240,94,94,0.16)',
                borderWidth: 10,
                shadowBlur: 0,
              },
            },
            waveLength: 128,
            label: {
              show: true,
              color: 'black',
              insideColor: '#fff',
              fontSize: 12,
              fontWeight: 400,
              zIndex: 100,
              align: 'center',
              baseline: 'middle',
              // position: 'inside',
              position: ['50%', '60%'],
              formatter: function (param: any) {
                return getLocale('OKR_MyO_Ew_Completionrate')
              },
            },
            radius: '90%',
          },
        ],
      }

      let targetOption = merge(_option, option)
      chartRef.current.setOption(targetOption)
    }, [])
    useEffect(() => {
      const chartSize = () => chartRef.current?.resize()
      window.addEventListener('resize', chartSize)
      return () => {
        window.removeEventListener('resize', chartSize)
      }
    }, [])

    useUpdateEffect(() => {
      chartRef.current?.setOption(option)
    }, [option])
    return (
      <div
        // @ts-ignore
        ref={ref}
        style={{ ...style, width, height }}
        className={classNames(preCls, className)}
      ></div>
    )
  }
)

export default Liquidfill
