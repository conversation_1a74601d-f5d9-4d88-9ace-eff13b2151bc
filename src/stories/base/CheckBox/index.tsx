import React, { useState, useEffect, Children } from "react";
import classNames from "classnames";
export { CheckBoxGroup } from './group'

import "./index.scss";
import { isMobile } from "@/utils/platform";

const pleCls = "tita-ui-checkbox";

export interface ICheckBoxProps {
  className?: string
  style?: React.CSSProperties
  onChange?: (checked: boolean, name: string, e: React.MouseEvent<HTMLInputElement>) => void;
  checked?: boolean;
  partChecked?: Boolean;
  defaultChecked?: boolean;
  size?: 'large' | 'default' | 'small'
  name?: any;
  disabled?: Boolean;
  children?: React.ReactNode
}

export const CheckBox = React.memo(function (props: ICheckBoxProps) {
  const {
    onChange,
    defaultChecked = false,
    name = 0,
    disabled = false,
    partChecked = false,
    size,
    children,
    className,
    style,
  } = props;

  const [checked, setChecked] = useState<boolean | (() => boolean)>((): boolean => {
    if (props.checked !== undefined) return props.checked;
    return defaultChecked;
  });

  useEffect(() => {
    if (props.checked !== undefined) setChecked(props.checked);
  }, [props.checked]);

  const onClickHandle = (e: React.MouseEvent<HTMLInputElement>) => {
    if (disabled) return
    if (props.checked === undefined) setChecked(!checked)
    onChange && onChange(!checked, name, e)
  }

  return (
    <div
      className={classNames(className, 'flex items-center')}
      onClick={(e: React.MouseEvent<HTMLInputElement>) => onClickHandle(e)}
      style={style}
    >
      <span
        className={classNames(
          pleCls,
          {
            [`${pleCls}--checked`]: checked,
            [`${pleCls}--part-checked`]: partChecked,
            [`${pleCls}--disabled`]: disabled,
            [`${pleCls}--size-${size}`]: size,
            [`${pleCls}--isMobile`]: isMobile(),
          },
          'inline-block'
        )}
      />
      {children && (
        <span className={`${pleCls}__label ml-[8px]`}>{children}</span>
      )}
    </div>
  )
})

export default CheckBox
