.tita-ui-checkbox {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #bfc7d5;
  cursor: pointer;
  box-sizing: border-box;
  color: #3f4755;

  &:hover{
    border: 1px solid #2879ff;
  }

  &__label {
    color: inherit;
    cursor: pointer;
    color: #3f4755;
  }

  &--size-small {
    width: 14px;
    height: 14px;
    border-radius: 3px;
  }
  &--size-large {
    width: 20px;
    height: 20px;
  }

  &--isMobile {
    border-radius: 50%;
  }
  &--isMobile:not(&--checked) {
    &:hover {
      border-color: #bfc7d5;
    }
  }

  &.tita-ui-checkbox--part-checked {
    background: url(https://xfile6.tita.com/ux/tita-home-page/release/dist/titaui-checkbox-part-checked.svg) transparent no-repeat 50% 50%;
    border-color: #2879ff;
    background-color: #2879ff;
  }
  &--checked {
    background: url(https://xfile6.tita.com/ux/tita-home-page/release/dist/titaui-checkbox-checked.svg) transparent no-repeat 50% 50%;
    border-color: #2879ff;
    background-size: 100% 100%;
    background-color: #2879ff;
  }

  &.tita-ui-checkbox--disabled {
    background-color: #f0f2f5;
    opacity: 0.5;
    cursor: not-allowed;
    &:hover{
      // border: 1px solid #bfc7d5;
      border: none;
    }
  }
}
