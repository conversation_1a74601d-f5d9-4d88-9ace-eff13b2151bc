import React, { FC, useState } from 'react'
import classNames from 'classnames'
import './group.scss'
import { useUpdateEffect } from 'ahooks'
import CheckBox from '.'
import Tooltip from '../Tooltip'

interface CheckBoxGroupItem {
  label: string | React.ReactNode
  value: string | number
  disabled?: boolean
  [key: string]: any;
}

export interface ICheckBoxGroupProps {
  items: CheckBoxGroupItem[]
  value?: (string | number)[]
  initialValue?: (string | number)[]
  onChange?: (value: (string | number)[]) => void
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  itemStyle?: React.CSSProperties
}

const preCls = 'tita-ui-check-box-group'

export const CheckBoxGroup: FC<ICheckBoxGroupProps> = React.memo(({ items, value: propsValue, initialValue, onChange, className, style, itemStyle }) => {
  const [values, setValues] = useState(initialValue || propsValue || [])

  useUpdateEffect(() => {
    setValues(propsValue || [])
  }, [propsValue])

  const onChangeHandler = (values: (string | number)[]) => {
    onChange && onChange(values)
    setValues(values)
  }

  return (
    <div className={classNames(preCls, className)} style={style}>
      {items.map(({ label, value, disabled, tips }) => (
        <div key={value} className={`${preCls}__item`} style={itemStyle}>
          <CheckBox checked={values.includes(value)} disabled={disabled} onChange={(checked) => {
            if (checked) {
              onChangeHandler([...values, value])
            } else {
              onChangeHandler(values.filter((v) => v !== value))
            }
          }}>
            <div className={`${preCls}__item-label`}>
              {label}
              {tips && (<Tooltip overlay={tips} placement='top'>
                <i className='tu-icon-question-mark' style={{ marginLeft: 4 }}></i>
              </Tooltip>)}
            </div>
            
          </CheckBox>
        </div>
      ))}
    </div>
  )
})

export default CheckBoxGroup
