import React, { FC } from 'react'
import iconPath from './iconPath'
import './index.scss'
import classNames from 'classnames'

export interface IIconProps {
  name: keyof typeof iconPath
  size?: number | string
  color?: string
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-icon'

export const Icon: FC<IIconProps> = React.memo(
  ({ name, size, className, style }) => {
    return (
      <svg
        version='1.1'
        xmlns='http://www.w3.org/2000/svg'
        width={size}
        height={size}
        viewBox={`0 0 32 32`}
        className={classNames(preCls, className)}
        style={{ fontSize: size, ...style }}
      >
        <path d={iconPath[name]}></path>
      </svg>
    )
  }
)

export default Icon
