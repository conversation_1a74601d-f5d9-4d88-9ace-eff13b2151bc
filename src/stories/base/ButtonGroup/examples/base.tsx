import React, { FC } from 'react'
import ButtonGroup, { ButtonGroupOption } from '@/stories/base/ButtonGroup'

export interface IBaseProps {}

const options: ButtonGroupOption[] = [
  {
    label: '流程',
    value: '1',
    icon: 'H5-shoucang-s',
  },
  {
    label: '节点详情',
    value: '2',
    icon: 'pc-card-s',
  },
]

const Base: FC<IBaseProps> = ({}) => {
  return <ButtonGroup options={options} size="small" />
}

export default React.memo(Base)
