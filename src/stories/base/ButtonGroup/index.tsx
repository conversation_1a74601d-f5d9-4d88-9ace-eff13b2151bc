import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'
import Button, { IButtonProps } from '../Button'
import { useFormItemValue } from '@/hooks/useFormItemValue'

export interface ButtonGroupOption extends IButtonProps {
  value: string
  label: React.ReactNode
}

export interface IButtonGroupProps {
  options: ButtonGroupOption[]
  defaultValue?: string
  size?: IButtonProps['size']
  onChange?: (value: string) => void
  disabled?: boolean
  spaceX?: number | string
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-button-group'

export const ButtonGroup: FC<IButtonGroupProps> = React.memo(
  ({ options, className, style, spaceX = 2, size, ...other }) => {
    const formValue = useFormItemValue(other)

    if (!options) return null

    return (
      <div
        className={classNames(preCls, className)}
        style={{
          gap: spaceX,
          ...style,
        }}
      >
        {options.map((option) => {
          const { value, ...otherProps } = option
          return (
            <Button
              key={value}
              {...otherProps}
              shape='square'
              type={value === formValue.value ? undefined : "text"}
              second={value === formValue.value}
              size={size}
              onClick={() => {
                if (value === formValue.value) return
                formValue.set(value)
              }}
              style={{
                padding: '0 6px',
              }}
            >
              {option.label}
            </Button>
          )
        })}
      </div>
    )
  }
)

export default ButtonGroup
