import React, { FC, MutableRefObject, ReactElement, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import classNames from 'classnames'
import icons, { IconNames } from './assets/icons'
import './index.scss'
import { useRefState } from '@tita/hooks'
import { findByIdxPath } from '@/utils/tree'
import Button from '../Button'
import { useUpdateEffect } from 'ahooks'
import Ellipsis from '../Ellipsis'
import { getLocale } from '@tita/utils'
import DivisionLine from '../DivisionLine'
import { useHistory } from '@/hooks/useHistory'
import Flipping, { IFlippingRef } from '../Flipping'

export { findByIdxPath } from '@/utils/tree'

export interface MenusItem<Data = any> {
  label: string | JSX.Element
  key: string | number
  isGroup?: boolean
  hasChild?: boolean
  disableEllipsis?: boolean
  href?: string | ((item: MenusItem<Data>) => string)
  data?: Data
  hidden?: boolean
  disableSelect?: boolean
  onLoad?: (item: MenusItem) => Promise<MenusItem[]>
  icon?: IconNames | React.ReactNode
  children?: MenusItem[]
}

export interface MenusView {
  key: string
  title?: string | React.ReactNode | ((props: unknown) => React.ReactNode)
  /** 用于自定义渲染场景 */
  render?: (props: Record<string, any> & { view: MenusView }) => ReactElement<any, any>
  /** 渲染菜单项 */
  renderMenu?: (props: Record<string, any>, view: MenusView) => Promise<MenusItem[]>
}

export interface IMenusProps<Data = any> {
  /**
   * 菜单配置
   * @editType json
   * @editData [{"label":"menu item 1","key":"1","icon":"my-setup","children":[{"label":"menu item 2","key":"1-1"},{"label":"menu item 3","key":"1-2"}]},{"label":"menu item 2","key":"2","icon":"my-setup"},{"label":"menu item 3","key":"3","icon":"my-setup"},{"label":"分组","key":"group","isGroup":true},{"label":"menu item 4","key":"4","icon":"my-setup"},{"label":"menu item 5","key":"5","icon":"my-setup"}]
   */
  items?: MenusItem<Data>[]
  /**
   * 选中元素的 key 值
   */
  selectedKey?: string
  /**
   * 默认选中元素的 key 值
   */
  initialSelectedKey?: string
  /**
   * 初始化自动跳转选中项的连接
   */
  initialJump?: boolean
  /**
   * 切换菜单事件
   */
  onChange?: (key: string | number, data: Data, viewKey: string) => void
  /**
   * 翻转视图
   */
  views?: MenusView[]
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

export interface IMenusRef {
  changeView: (key: string, props?: Record<string, any>) => void
  goBack: () => void
}


const preCls = 'tita-ui-menus'

const MenuItem: FC<{ item: MenusItem, isGroup?: boolean, hasChild?: boolean, active?: string | number, open?: boolean, onChangeOpen?: (open: boolean) => void, onClick?: () => void, style?: React.CSSProperties }> = React.memo(
  ({ item, hasChild, active, open, onClick, onChangeOpen, style }) => {
    // @ts-ignore
    const icon = typeof item.icon === 'string' ? icons[item.icon] : item.icon
    return (
      <div className={classNames(`${preCls}__item`, {
        [`${preCls}__item--active`]: active === item.key,
        [`${preCls}__item--isGroup`]: item.isGroup,
      })} onClick={item.isGroup ? undefined : onClick} style={style}>
        <div className="flex items-center w-full overflow-hidden">
          {!item.isGroup && item.icon && (
            <div className={`${preCls}__item-icon`}>{icon}</div>
          )}
          {item.disableEllipsis ? item.label : <Ellipsis>{item.label}</Ellipsis>}
        </div>
        {hasChild && <Button onClick={(e) => {
          e.stopPropagation()
          onChangeOpen?.(!open)
        }} icon="APP-xi" className={classNames("ml-12px transition-all", {
          'rotate-90': open
        })} size="least" type="text" shape="square" />}
      </div>
    )
  }
)

const Backup: FC<{ view: MenusView, props?: Record<string, any>, goBack: () => void, children: React.ReactNode }> = React.memo(
  ({ view, props, goBack, children }) => {
    const title = useMemo(() => {
      if (typeof view.title === 'function') return view.title(props)

      return view.title
    }, [view])
    return (
      <>
        <DivisionLine>
          <div className='text-#89919f text-[12px] cursor-pointer focus:text-primary' onClick={goBack}>
            <i className='tu-icon-fanhui mr-2px' />
            <span>{getLocale('Mod_Back') || '返回'}</span>
          </div>
          <span className='text-#89919f text-[12px]'>{title}</span>
        </DivisionLine>
        {children}
      </>
    )
  }
)

const AyncMenu: FC<{ fetch: MenusView['renderMenu'], props?: Record<string, any>, view: MenusView, renderItem: (item: MenusItem, idxPath: number[], level: number) => JSX.Element }> = React.memo(
  ({ fetch, props, view, renderItem }) => {
    const [items, setItems] = useState<MenusItem[]>([])
    useEffect(() => {
      fetch?.(props || {}, view).then((items) => {
        setItems(items)
      })
    }, [])
    return (
      <>
        {items?.map((item, i) => item.hidden ? <span key={item.key}></span> : renderItem(item, [i], 0))}
      </>
    )
  }
)

export const Menus: React.ForwardRefExoticComponent<IMenusProps<any> & React.RefAttributes<IMenusRef>> = React.memo(forwardRef(
  ({ items: propsItems, initialSelectedKey, selectedKey, views, initialJump, onChange, className, style }, ref) => {
    const [active, setActive] = useState(() => {
      return initialSelectedKey || selectedKey || propsItems?.[0]?.key
    })
    useUpdateEffect(() => {
      setActive(selectedKey as (string | number))
    }, [selectedKey])

    // -1 代表默认页面
    // 其他值代表通过 views 传入的 key 值
    const viewHistory = useHistory([{ key: '-1', props: {} }])
    const currentView = viewHistory.current
    const [currentViewKey, setCurrentViewKey] = useState()
    const [items, itemsRef, setItems] = useRefState(propsItems)

    function jump(item: MenusItem) {
      setTimeout(() => {
        if (!item.href) return
        setActive(item.key)
        const href = typeof item.href === 'string' ? item.href : item.href(item)
        location.href = href
      }, 0)
    }

    const [openKeys, openKeysRef, setOpenKeys] = useRefState<(string | number)[]>([])

    async function jumpFirstHref() {
      const firstHasHrefItemIndex = items.findIndex(({ hidden, href }) => !hidden && href)
      if (firstHasHrefItemIndex === -1) return

      const firstHasHrefItem = items[firstHasHrefItemIndex]

      // 如果自身可以点击
      if (!firstHasHrefItem.disableSelect) {
        jump(firstHasHrefItem)
        return
      }

      // 如果需要异步加载
      if (firstHasHrefItem.hasChild && !firstHasHrefItem.children?.length) {
        const childs = await onOpenChangeHandler(firstHasHrefItem, [firstHasHrefItemIndex])
        if (childs?.[0]?.href) {
          jump(childs[0])
        }
        return
      }

      // 如果已经有子元素了
      if (firstHasHrefItem.hasChild && firstHasHrefItem.children?.length) {
        // 找到第一个可点击并且有 href 的元素（这里只向下找一级）
        const childFirstHasHrefItem = firstHasHrefItem.children.find(({ hidden, href, disableSelect }) => !hidden && href && !disableSelect)
        if (childFirstHasHrefItem) jump(childFirstHasHrefItem)
        return
      }
    }

    useEffect(() => {
      if (initialJump) jumpFirstHref()
    }, [])

    const onChangeActiveHandler = useCallback((key: string | number, item: MenusItem, viewKey: string) => {
      setActive(key)
      onChange?.(key, item, viewKey)

      if (item.href) jump(item)
    }, [onChange])

    async function onOpenChangeHandler(item: MenusItem, idxPath: number[]) {
      if (openKeysRef.current.includes(item.key)) setOpenKeys(openKeysRef.current.filter(key => key !== item.key))
      else setOpenKeys([...openKeysRef.current, item.key])
          
      if (item.onLoad && !item.children?.length) {
        const childMenus = await item.onLoad(item)
        if (childMenus && childMenus.length) {
          try {
            const item = findByIdxPath(itemsRef.current, idxPath)
            if (!item) return

            item.children = childMenus
            setItems([...itemsRef.current])
          } catch (error) {
            console.error('匹配菜单错误：', error)
          }
          return childMenus
        }
      }
    }

    function renderItem(item: MenusItem, idxPath: number[], level: number) {
      if (item.children || item.hasChild) {
        const open = openKeysRef.current.includes(item.key)

        return (
          <div key={item.key}>
            <MenuItem item={item} hasChild active={active} open={open}
              onChangeOpen={() => onOpenChangeHandler(item, idxPath)}
              style={{
                paddingLeft: `${level ? level * 22 : 8}px`
              }}
              onClick={() => {
                // 如果该选项禁止选中，则同时触发 open
                if (item.disableSelect) onOpenChangeHandler(item, idxPath)
                else onChangeActiveHandler(item.key, item, view?.key || '-1')
              }} />
            <div className={classNames(`${preCls}__childrens`)} style={{ height: open ? 'auto' : 0 }}>
              {item.children?.filter(({ hidden }) => !hidden).map((item, i) => item.hidden ? <span key={item.key}></span> : renderItem(item, [...idxPath, i], level + 1))}
            </div>
          </div>
        )
      }

      return (
        <MenuItem
          key={item.key}
          item={item}
          active={active}
          style={{
            paddingLeft: `${level ? level * 22 : 8}px`
          }}
          onClick={() => onChangeActiveHandler(item.key, item, view?.key || '-1')}
        />
      )
    }
    const content = useMemo(() => {
      // const view = views?.find(({ key }) => key === currentView.key)

      // if (currentView.key !== '-1') {
        
      //   if (!view) return <></>

      //   if (view.render) {
      //     const Render = view.render
      //     return (
      //       <Backup view={view} props={currentView.props} goBack={viewHistory.pop}>
      //         <Render {...currentView.props} view={view} />
      //       </Backup>
      //     )
      //   }

      //   if (view.renderMenu) {
      //     return (
      //       <Backup view={view} props={currentView.props} goBack={viewHistory.pop}>
      //         <AyncMenu fetch={view.renderMenu} props={currentView.props} view={view} renderItem={renderItem} />
      //       </Backup>
      //     )
      //   }
      // }
      return items?.map((item, i) => item.hidden ? <span key={item.key}></span> : renderItem(item, [i], 0))
    }, [currentView, views, items, active, openKeys])

    useImperativeHandle(ref, () => ({
      changeView: (newKey, props) => {
        // viewHistory.push(newKey, props)
        const view = views?.find(({ key }) => key === newKey)

        if (!view) return <></>

        if (view.render) {
          const Render = view.render
          flippingRef.current.push(
            <div className={classNames(preCls, className)} style={style}>
              <Backup view={view} props={currentView.props} goBack={flippingRef.current.pop}>
                <Render {...currentView.props} view={view} />
              </Backup>
            </div>
          )
        }

        if (view.renderMenu) {
          flippingRef.current.push(
            <div className={classNames(preCls, className)} style={style}>
              <Backup view={view} props={currentView.props} goBack={flippingRef.current.pop}>
                <AyncMenu fetch={view.renderMenu} props={currentView.props} view={view} renderItem={renderItem} />
              </Backup>
            </div>
          )
        }
      },
      goBack: () => {
        flippingRef.current.pop()
      }
    }))

    
    const flippingRef = useRef() as MutableRefObject<IFlippingRef>

    const flippingViews = useMemo(() => {
      return [
        <div className={classNames(preCls, className)} style={style}>
          {content}
        </div>
      ]
    }, [content])

    return (
      <Flipping ref={flippingRef} views={flippingViews} />
    )

    // return (
    //   <div className={classNames(preCls, className)} style={style}>
    //     {content}
    //   </div>
    // )
  }
))

export default Menus
