import React, { FC, ReactElement, RefObject, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import classNames from 'classnames'
import icons, { IconNames } from './assets/icons'
import './index.scss'
import { useRefState } from '@tita/hooks'
import { findByIdxPath } from '@/utils/tree'
import Button from '../Button'
import { useUpdateEffect } from 'ahooks'
import Ellipsis from '../Ellipsis'
import { getLocale } from '@tita/utils'
import DivisionLine, { Line } from '../DivisionLine'
import { useHistory } from '@/hooks/useHistory'
import TitaScroll, { ITitaScrollProps, ITitaScrollRef } from '../TitaScroll'
import { deepFind } from '@/utils/array'

export { findByIdxPath } from '@/utils/tree'

export interface MenusItem<Data = any> {
  label: string | JSX.Element
  key: string | number
  isGroup?: boolean
  hasChild?: boolean
  disableEllipsis?: boolean
  href?: string | ((item: MenusItem<Data>) => string)
  data?: Data
  hidden?: boolean
  disableSelect?: boolean
  onLoad?: (item: MenusItem) => Promise<MenusItem[]>
  icon?: IconNames | React.ReactNode
  children?: MenusItem[]
  showPreconfigure?: boolean
  suffix?: React.ReactNode
}

export interface MenusView {
  key: string
  title?: string | React.ReactNode | ((props: unknown) => React.ReactNode)
  /** 用于自定义渲染场景 */
  render?: (props: Record<string, any> & { view: MenusView }) => ReactElement<any, any>
  /** 渲染菜单项 */
  renderMenu?: (props: Record<string, any>, view: MenusView) => Promise<MenusItem[]>
}

export interface IMenusProps<Data = any> extends Omit<ITitaScrollProps, 'onChange' | 'ref'> {
  /**
   * 菜单配置
   * @editType json
   * @editData [{"label":"menu item 1","key":"1","icon":"my-setup","children":[{"label":"menu item 2","key":"1-1"},{"label":"menu item 3","key":"1-2"}]},{"label":"menu item 2","key":"2","icon":"my-setup"},{"label":"menu item 3","key":"3","icon":"my-setup"},{"label":"分组","key":"group","isGroup":true},{"label":"menu item 4","key":"4","icon":"my-setup"},{"label":"menu item 5","key":"5","icon":"my-setup"}]
   */
  items?: MenusItem<Data>[]
  /**
   * 选中元素的 key 值
   */
  selectedKey?: string
  /**
   * 默认选中元素的 key 值
   */
  initialSelectedKey?: string
  /**
   * 展开的元素
   */
  openKeys?: (string | number)[]
  /**
   * 默认展开的元素
   */
  initialOpenKeys?: (string | number)[]
  /**
   * 初始化自动跳转选中项的连接
   */
  initialJump?: boolean
  /**
   * 子层级左侧不设置 padding
   */
  disableLevelPadding?: boolean
  /**
   * 切换菜单事件
   */
  onChange?: (key: string | number, data: Data, viewKey: string) => void
  onBack?: (currentViewKey: string | number, props?: Record<string, any>) => void
  /**
   * 翻转视图
   */
  views?: MenusView[]
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  /** 是否禁用返回按钮 */
  disableGoback?: boolean
}

export interface IGoBackParams {
  /** 默认跳转地址 */
  defaultBackUrl?: string
}

export interface IMenusRef {
  changeView: (key: string, props?: Record<string, any>) => void
  goBack: (params?: IGoBackParams) => void
}


const preCls = 'tita-ui-menus'

const MenuItem: FC<{ item: MenusItem, isGroup?: boolean, hasChild?: boolean, active?: string | number, open?: boolean, onChangeOpen?: (open: boolean) => void, onClick?: () => void, style?: React.CSSProperties }> = React.memo(
  ({ item, hasChild, active, open, onClick, onChangeOpen, style }) => {
    // @ts-ignore
    const icon = typeof item.icon === 'string' ? icons[item.icon] : item.icon
    return (
      <div data-key={item.key} className={classNames(`${preCls}__item`, {
        [`${preCls}__item--active`]: active === item.key,
        [`${preCls}__item--isGroup`]: item.isGroup && !hasChild,
        [`${preCls}__item--isGroupMenu`]: item.isGroup && hasChild,
      })} onClick={(item.isGroup && !hasChild) ? undefined : onClick} style={style}>
        <div className="flex items-center w-full overflow-hidden">
          {!item.isGroup && item.icon && (
            <div className={`${preCls}__item-icon`}>{icon}</div>
          )}
          {item.disableEllipsis ? item.label : <Ellipsis>{item.label}</Ellipsis>}
          {item.showPreconfigure && <span className={classNames(`${preCls}__item-suffix`)} />}
          {item.suffix}
        </div>
        {hasChild && <Button onClick={(e) => {
          e.stopPropagation()
          onChangeOpen?.(!open)
        }} icon="APP-xi" iconColor={item.isGroup ? '#7e8692' : undefined} className={classNames("ml-12px transition-all", `${preCls}__down-icon`, {
          'rotate-90': open,
        })} size="least" type="text" shape="square" />}
      </div>
    )
  }
)

const Backup: FC<{ view: MenusView, props?: Record<string, any>, goBack: () => void, children: React.ReactNode }> = React.memo(
  ({ view, props, goBack, children }) => {
    const title = useMemo(() => {
      if (typeof view.title === 'function') return view.title(props)

      return view.title
    }, [view])
    return (
      <>
        <div className="flex mb-12px">
          {!props?.disableGoback && (
            <>
              <div className='text-#89919f text-[12px] cursor-pointer focus:text-primary shrink-0' onClick={goBack}>
                <i className='tu-icon-fanhui mr-2px' />
                <span>{getLocale('Mod_Back') || '返回'}</span>
              </div>
              <Line className="shrink-0" margin={6} />
            </>
          )}
          <Ellipsis className='text-#89919f text-[12px]'>{title}</Ellipsis>
        </div>
        {children}
      </>
    )
  }
)

const AyncMenu: FC<{ fetch: MenusView['renderMenu'], props?: Record<string, any>, view: MenusView, renderItem: (item: MenusItem, idxPath: number[], level: number) => JSX.Element }> = React.memo(
  ({ fetch, props, view, renderItem }) => {
    const [items, setItems] = useState<MenusItem[]>([])
    useEffect(() => {
      fetch?.(props || {}, view).then((items) => {
        setItems(items)
      })
    }, [])
    return (
      <>
        {items?.map((item, i) => item.hidden ? <span key={item.key}></span> : renderItem(item, [i], 0))}
      </>
    )
  }
)

export const Menus = React.memo(forwardRef<IMenusRef, IMenusProps<any>>(
  ({ items: propsItems, initialSelectedKey, selectedKey, openKeys: propsOpenKeys, initialOpenKeys, views, initialJump, height, onChange, onBack, className, disableGoback, disableLevelPadding, style, ...other }, ref) => {
    const [active, setActive] = useState(() => {
      return initialSelectedKey || selectedKey || propsItems?.[0]?.key
    })
    const activeRef = useRef(active)
    useUpdateEffect(() => {
      activeRef.current = active
    }, [active])
    useUpdateEffect(() => {
      setActive(selectedKey as (string | number))
    }, [selectedKey])

    const scrollRef = useRef<ITitaScrollRef>() as RefObject<ITitaScrollRef>

    // -1 代表默认页面
    // 其他值代表通过 views 传入的 key 值
    const viewHistory = useHistory([{ key: '-1', props: {} }])
    const currentView = viewHistory.current
    const viewHistoryRef = useRef(viewHistory)
    viewHistoryRef.current = viewHistory
    const [items, itemsRef, setItems] = useRefState(propsItems)

    function jump(item: MenusItem) {
      setTimeout(() => {
        if (!item.href) return
        setActive(item.key)
        const href = typeof item.href === 'string' ? item.href : item.href(item)
        location.href = href
      }, 0)
    }

    // @ts-ignore
    const [openKeys, openKeysRef, setOpenKeys] = useRefState<(string | number)[]>(() => {
      if (initialOpenKeys || propsOpenKeys) return initialOpenKeys || propsOpenKeys || []

      // @ts-ignore
      const { path } = deepFind(items, 'children', (item) => {
        // @ts-ignore
        return item.key === active
      })
      // @ts-ignore
      const defaultOpenKeyByActive = path?.slice(0, -1).map(({ key }) => key) || []
      return defaultOpenKeyByActive || []
    })
    useUpdateEffect(() => {
      setOpenKeys(propsOpenKeys || [])
    }, [propsOpenKeys])

    async function jumpFirstHref() {
      // @ts-ignore
      const firstHasHrefItemIndex = items.findIndex(({ hidden, href }) => !hidden && href)
      if (firstHasHrefItemIndex === -1) return

      const firstHasHrefItem = items[firstHasHrefItemIndex]

      // 如果自身可以点击
      if (!firstHasHrefItem.disableSelect) {
        jump(firstHasHrefItem)
        return
      }

      // 如果需要异步加载
      if (firstHasHrefItem.hasChild && !firstHasHrefItem.children?.length) {
        const childs = await onOpenChangeHandler(firstHasHrefItem, [firstHasHrefItemIndex])
        if (childs?.[0]?.href) {
          jump(childs[0])
        }
        return
      }

      // 如果已经有子元素了
      if (firstHasHrefItem.hasChild && firstHasHrefItem.children?.length) {
        // 找到第一个可点击并且有 href 的元素（这里只向下找一级）
        // @ts-ignore
        const childFirstHasHrefItem = firstHasHrefItem.children.find(({ hidden, href, disableSelect }) => !hidden && href && !disableSelect)
        if (childFirstHasHrefItem) jump(childFirstHasHrefItem)
        return
      }
    }

    useEffect(() => {
      if (initialJump) jumpFirstHref()

      scrollRef.current?.scrollToChild(`div[data-key="${active}"]`)
    }, [])

    const onChangeActiveHandler = useCallback((key: string | number, item: MenusItem, viewKey: string) => {
      setActive(key)
      activeRef.current?.toString() !== key?.toString() && onChange?.(key, item, viewKey)

      if (item.href) jump(item)
    }, [onChange])

    async function onOpenChangeHandler(item: MenusItem, idxPath: number[]) {
      // @ts-ignore
      if (openKeysRef.current.includes(item.key)) setOpenKeys(openKeysRef.current.filter(key => key !== item.key))
      else setOpenKeys([...openKeysRef.current, item.key])

      if (item.onLoad && !item.children?.length) {
        const childMenus = await item.onLoad(item)
        if (childMenus && childMenus.length) {
          try {
            const item = findByIdxPath(itemsRef.current, idxPath)
            if (!item) return

            // @ts-ignore
            item.children = childMenus
            setItems([...itemsRef.current])
          } catch (error) {
            console.error('匹配菜单错误：', error)
          }
          return childMenus
        }
      }
    }

    function onBackHandler(params?: IGoBackParams) {
      if (!viewHistoryRef.current.current.props?.__backActive) return params?.defaultBackUrl && window.open(params?.defaultBackUrl, '_self')

      onBack?.(viewHistoryRef.current.current.key, viewHistoryRef.current.current.props)
      setActive(viewHistoryRef.current.current.props?.__backActive)
      viewHistoryRef.current.pop()
    }

    const content = useMemo(() => {
      const view = views?.find(({ key }) => key === currentView.key)
      function renderItem(item: MenusItem, idxPath: number[], level: number) {
        if (item.children || item.hasChild) {
          const open = openKeysRef.current.includes(item.key)

          return (
            <div key={item.key}>
              <MenuItem item={item} hasChild active={active} open={open}
                onChangeOpen={() => onOpenChangeHandler(item, idxPath)}
                style={{
                  paddingLeft: disableLevelPadding ? undefined : `${level ? level * (level > 1 ? 23 : 31) : 8}px`,
                  fontWeight: (level === 0 && !item.isGroup) ? '600' : 'normal'
                }}
                onClick={() => {
                  // 如果该选项禁止选中，则同时触发 open
                  if (item.disableSelect || item.isGroup) onOpenChangeHandler(item, idxPath)
                  else onChangeActiveHandler(item.key, item, view?.key || '-1')
                }} />
              {item.children && (
                <div className={classNames(`${preCls}__childrens`)} style={{ height: open ? 'auto' : 0 }}>
                  {item.children.filter(({ hidden }) => !hidden).map((item, i) => item.hidden ? <span key={item.key}></span> : renderItem(item, [...idxPath, i], level + 1))}
                </div>
              )}
            </div>
          )
        }

        return (
          <MenuItem
            key={item.key}
            item={item}
            active={active}
            style={{
              paddingLeft: disableLevelPadding ? undefined : `${level ? level * (level > 1 ? 23 : 31) : 8}px`,
              fontWeight: (level === 0 && !item.isGroup) ? '600' : 'normal'
            }}
            onClick={() => onChangeActiveHandler(item.key, item, view?.key || '-1')}
          />
        )
      }

      if (currentView.key !== '-1') {

        if (!view) return <></>

        if (view.render) {
          const Render = view.render
          return (
            <Backup view={view} props={{ ...currentView.props, disableGoback: disableGoback }} goBack={onBackHandler} >
              <Render {...currentView.props} view={view} />
            </Backup>
          )
        }

        if (view.renderMenu) {
          return (
            <Backup view={view} props={{ ...currentView.props, disableGoback: disableGoback }} goBack={onBackHandler}>
              <AyncMenu fetch={view.renderMenu} props={currentView.props} view={view} renderItem={renderItem} />
            </Backup>
          )
        }
      }
      // @ts-ignore
      return items?.map((item, i) => item.hidden ? <span key={item.key}></span> : renderItem(item, [i], 0))
    }, [currentView, views, items, active, openKeys, disableGoback])

    useImperativeHandle(ref, () => ({
      changeView: (key, props) => {
        if (props?.defaultActive) setActive(props.defaultActive)
        // __backActive 用于返回上一页时重置选中状态
        viewHistory.push(key, { ...props, __backActive: active })
      },
      goBack: (params) => {
        onBackHandler(params)
      }
    }))

    return (
      <TitaScroll height={height || "100%"} className={classNames(preCls, className)} innerStyle={{ width: '100%' }} style={style} {...other} ref={scrollRef}>
        {content}
      </TitaScroll>
    )
  }
))

export default Menus
