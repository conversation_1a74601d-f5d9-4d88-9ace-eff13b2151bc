const icons = {
  'my-setup': (
    <svg width="16px" height="14px" viewBox="0 0 14.5574374 13.4109582" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
      <title>my-setup</title>
      <g id="经营目标管理" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g id="目标流程-个人目标已开启执行期" transform="translate(-37.7213, -204.2945)" fill-rule="nonzero">
              <g id="主菜单2" transform="translate(16, 122)">
                  <g id="编组-2" transform="translate(21.7213, 78)">
                      <g id="my-setup" transform="translate(0, 4.2945)">
                          <g id="编组" transform="translate(0, -0)">
                              <path d="M5.77871871,-0.171315823 L2.07323961,1.96804353 C1.14503638,2.50394192 0.573239611,3.49432297 0.573239611,4.56611974 L0.573239611,8.84483845 C0.573239611,9.91663522 1.14503638,10.9070163 2.07323961,11.4429147 L5.77871871,13.582274 C6.70692194,14.1181724 7.85051548,14.1181724 8.77871871,13.582274 L12.4841978,11.4429147 C13.412401,10.9070163 13.9841978,9.91663522 13.9841978,8.84483845 L13.9841978,4.56611974 C13.9841978,3.49432297 13.412401,2.50394192 12.4841978,1.96804353 L8.77871871,-0.171315823 C7.85051548,-0.707214207 6.70692194,-0.707214207 5.77871871,-0.171315823 Z M8.17871871,0.867914662 L11.8841978,3.00727402 C12.4411197,3.32881305 12.7841978,3.92304168 12.7841978,4.56611974 L12.7841978,8.84483845 C12.7841978,9.48791651 12.4411197,10.0821451 11.8841978,10.4036842 L8.17871871,12.5430435 C7.62179677,12.8645826 6.93564065,12.8645826 6.37871871,12.5430435 L2.67323961,10.4036842 C2.11631767,10.0821451 1.77323961,9.48791651 1.77323961,8.84483845 L1.77323961,4.56611974 C1.77323961,3.92304168 2.11631767,3.32881305 2.67323961,3.00727402 L6.37871871,0.867914662 C6.93564065,0.546375631 7.62179677,0.546375631 8.17871871,0.867914662 Z" id="多边形" fill="#141C28" transform="translate(7.2787, 6.7055) rotate(-90) translate(-7.2787, -6.7055)"></path>
                              <path d="M7.27871871,3.83651358 C5.6942328,3.83651358 4.40975319,5.12099319 4.40975319,6.7054791 C4.40975319,8.289965 5.6942328,9.57444461 7.27871871,9.57444461 C8.86320461,9.57444461 10.1476842,8.289965 10.1476842,6.7054791 C10.1476842,5.12099319 8.86320461,3.83651358 7.27871871,3.83651358 Z M7.27871871,5.03651358 C8.20046291,5.03651358 8.94768423,5.78373489 8.94768423,6.7054791 C8.94768423,7.6272233 8.20046291,8.37444461 7.27871871,8.37444461 C6.3569745,8.37444461 5.60975319,7.6272233 5.60975319,6.7054791 C5.60975319,5.78373489 6.3569745,5.03651358 7.27871871,5.03651358 Z" id="椭圆形" fill="#2879FF"></path>
                          </g>
                      </g>
                  </g>
              </g>
          </g>
      </g>
    </svg>
  ),
  'bumen': (
    <svg width="16px" height="16px" viewBox="0 0 240 240" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
      <title>bumen</title>
      <g id="bumen" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <path d="M160.652174,111 C178.313427,111 192.709503,125.016328 192.995659,142.470058 L193,143 L193.000491,154.082188 C205.319318,155.098723 215,165.41884 215,178 L215,190 C215,203.254834 204.254834,214 191,214 L179,214 C165.745166,214 155,203.254834 155,190 L155,178 C155,165.418498 164.681208,155.098163 177.000513,154.082105 L177,143 C177,134.293069 169.876995,127.169387 160.970994,127.002976 L160.652174,127 L79.3478261,127 C70.4001299,127 63.171858,134.021248 63.0030195,142.68972 L63,143 L63.0004908,154.082188 C75.3193182,155.098723 85,165.41884 85,178 L85,190 C85,203.254834 74.254834,214 61,214 L49,214 C35.745166,214 25,203.254834 25,190 L25,178 C25,165.418498 34.6812076,155.098163 47.0005127,154.082105 L47,143 C47,125.477908 61.2194636,111.286358 78.8136472,111.004279 L79.3478261,111 L160.652174,111 Z M191,170 L179,170 C174.665086,170 171.135457,173.447838 171.003807,177.750821 L171,178 L171,190 C171,194.334914 174.447838,197.864543 178.750821,197.996193 L179,198 L191,198 C195.334914,198 198.864543,194.552162 198.996193,190.249179 L199,190 L199,178 C199,173.665086 195.552162,170.135457 191.249179,170.003807 L191,170 Z M61,170 L49,170 C44.6650857,170 41.1354571,173.447838 41.0038068,177.750821 L41,178 L41,190 C41,194.334914 44.4478378,197.864543 48.7508207,197.996193 L49,198 L61,198 C65.3349143,198 68.8645429,194.552162 68.9961932,190.249179 L69,190 L69,178 C69,173.665086 65.5521622,170.135457 61.2491793,170.003807 L61,170 Z" id="形状结合" fill="#141C28" fill-rule="nonzero"></path>
          <polygon id="矩形" fill="#141C28" points="112 74 128 74 128 127 112 127"></polygon>
          <path d="M135,26 C148.254834,26 159,36.745166 159,50 L159,66 C159,79.254834 148.254834,90 135,90 L105,90 C91.745166,90 81,79.254834 81,66 L81,50 C81,36.745166 91.745166,26 105,26 L135,26 Z M135,42 L105,42 C100.665086,42 97.1354571,45.4478378 97.0038068,49.7508207 L97,50 L97,66 C97,70.3349143 100.447838,73.8645429 104.750821,73.9961932 L105,74 L135,74 C139.334914,74 142.864543,70.5521622 142.996193,66.2491793 L143,66 L143,50 C143,45.6650857 139.552162,42.1354571 135.249179,42.0038068 L135,42 Z" id="矩形" fill="#2879FF" fill-rule="nonzero"></path>
      </g>
    </svg>
  ),
  'OKR-gongsi': (
    <svg width="16px" height="16px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
      <title>切片</title>
      <g id="公司目标" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g id="初始化-周期进行中-" transform="translate(-36.000000, -113.000000)">
              <g id="主菜单" transform="translate(16.000000, 74.000000)">
                  <g id="编组-7" transform="translate(12.000000, 28.000000)">
                      <g id="OKR-gongsi" transform="translate(8.000000, 11.000000)">
                          <g id="编组" transform="translate(3.000000, 1.900829)">
                              <path d="M4.12817722,0.206162192 L1.42817722,1.40187648 C0.559904413,1.78639729 0,2.64670777 0,3.59631501 L0,11.784845 C0,13.1103284 1.0745166,14.184845 2.4,14.184845 L9.6,14.184845 L9.63462697,14.1845841 C10.9484378,14.1633024 12,13.0955702 12,11.784845 L12,8.67673449 L11.9996896,8.6392103 L11.9987609,8.60252961 C11.9722834,7.79659137 11.547476,7.06031693 10.8646552,6.63696803 L7.5,4.55089554 L7.5,2.40060073 C7.5,2.06586936 7.42998002,1.73483937 7.29443854,1.42877795 C6.75771563,0.21682301 5.34013216,-0.330560708 4.12817722,0.206162192 Z M6.19721927,1.91468934 C6.26499001,2.06772005 6.3,2.23323504 6.3,2.40060073 L6.3,4.88484497 C6.3,5.09248809 6.40735976,5.2853712 6.5838362,5.39478659 L10.2323276,7.65685126 C10.5789031,7.87172806 10.7925492,8.24786466 10.7998086,8.65524464 L10.8000238,8.68207874 L10.8000238,11.784845 C10.8000238,12.4401587 10.2741675,12.974077 9.62003948,12.9846827 L9.59513932,12.9848647 L2.4,12.9848647 C1.7372583,12.9848647 1.2,12.4475867 1.2,11.784845 L1.2,3.59631501 C1.2,3.12151139 1.47995221,2.69135615 1.91408861,2.49909575 L4.61408861,1.30338146 C5.21249136,1.03837453 5.91115409,1.30195427 6.18695032,1.89212135 L6.19721927,1.91468934 Z" id="形状结合" fill="#141C28" fill-rule="nonzero"></path>
                              <rect id="矩形" fill="#2879FF" x="8.625" y="8.18484497" width="1.2" height="3.75" rx="0.6"></rect>
                              <rect id="矩形" fill="#141C28" x="3.15" y="3.23484497" width="1.2" height="8.7" rx="0.6"></rect>
                              <rect id="矩形" fill="#141C28" x="6.3" y="4.39917054" width="1.2" height="8.85"></rect>
                          </g>
                      </g>
                  </g>
              </g>
          </g>
      </g>
    </svg>
  ),
  'OKR': (
    <svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
      <title>切片</title>
      <g id="公司目标" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g id="初始化-周期进行中-" transform="translate(-36.000000, -193.000000)" stroke-width="1.2">
              <g id="主菜单" transform="translate(16.000000, 74.000000)">
                  <g id="编组-13" transform="translate(12.000000, 108.000000)">
                      <g id="OKR" transform="translate(8.000000, 11.000000)">
                          <g id="编组-2" transform="translate(2.625000, 2.325000)">
                              <path d="M7.14685615,0.342624883 C6.89438347,0.31223132 6.63737051,0.296590909 6.37670455,0.296590909 C2.85494787,0.296590909 -8.8817842e-16,3.15153878 -8.8817842e-16,6.67329545 C-8.8817842e-16,10.1950521 2.85494787,13.05 6.37670455,13.05 C9.89846122,13.05 12.7534091,10.1950521 12.7534091,6.67329545 C12.7534091,6.41019278 12.7374749,6.15081174 12.7065189,5.8960647" id="路径" stroke="#141C28" stroke-linecap="round"></path>
                              <path d="M2.52102273,6.67329545 C2.52102273,8.80272972 4.24727028,10.5289773 6.37670455,10.5289773" id="路径" stroke="#2879FF" stroke-linecap="round" stroke-linejoin="round"></path>
                              <circle id="椭圆形" stroke="#141C28" cx="6.37670455" cy="6.67329545" r="1.18636364"></circle>
                              <line x1="7.26647727" y1="5.78352273" x2="9.19431818" y2="3.85568182" id="路径-5" stroke="#141C28" stroke-linecap="round"></line>
                              <path d="M9.78734169,1.05574061 L10.6421943,1.52202387 L10.6421943,1.52202387 L11.497047,1.05574061 C11.7515923,0.916897682 12.0704966,1.01069304 12.2093395,1.26523842 C12.2514031,1.34235494 12.2734443,1.42879332 12.2734443,1.51663579 L12.2734443,2.98991195 C12.2734443,3.18203266 12.1685036,3.35880971 11.9998417,3.45080712 L10.6421943,4.19134205 L10.6421943,4.19134205 L9.28454695,3.45080712 C9.11588503,3.35880971 9.01094432,3.18203266 9.01094432,2.98991195 L9.01094432,1.51663579 C9.01094432,1.2266863 9.24599483,0.99163579 9.53594432,0.99163579 C9.62378679,0.99163579 9.71022518,1.01367706 9.78734169,1.05574061 Z" id="矩形" stroke="#141C28" stroke-linejoin="round" transform="translate(10.642194, 2.411797) rotate(-315.000000) translate(-10.642194, -2.411797) "></path>
                          </g>
                      </g>
                  </g>
              </g>
          </g>
      </g>
    </svg>
  ),
  'caidan-xiashu': (
    <svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
      <title>切片</title>
      <g id="公司目标" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g id="初始化-周期进行中-" transform="translate(-36.000000, -233.000000)" stroke-width="1.2">
              <g id="主菜单" transform="translate(16.000000, 74.000000)">
                  <g id="编组-14" transform="translate(12.000000, 148.000000)">
                      <g id="caidan-xiashu" transform="translate(8.000000, 11.000000)">
                          <path d="M6.45,2.1 C6.78137085,2.1 7.08137085,2.23431458 7.29852814,2.45147186 C7.51568542,2.66862915 7.65,2.96862915 7.65,3.3 L7.65,4.65 C7.65,4.98137085 7.51568542,5.28137085 7.29852814,5.49852814 C7.08137085,5.71568542 6.78137085,5.85 6.45,5.85 L4.8,5.85 C4.46862915,5.85 4.16862915,5.71568542 3.95147186,5.49852814 C3.73431458,5.28137085 3.6,4.98137085 3.6,4.65 L3.6,3.3 C3.6,2.96862915 3.73431458,2.66862915 3.95147186,2.45147186 C4.16862915,2.23431458 4.46862915,2.1 4.8,2.1 Z" id="矩形" stroke="#141C28"></path>
                          <path d="M13.2,5.7 C13.5313708,5.7 13.8313708,5.83431458 14.0485281,6.05147186 C14.2656854,6.26862915 14.4,6.56862915 14.4,6.9 L14.4,8.25 C14.4,8.58137085 14.2656854,8.88137085 14.0485281,9.09852814 C13.8313708,9.31568542 13.5313708,9.45 13.2,9.45 L11.55,9.45 C11.2186292,9.45 10.9186292,9.31568542 10.7014719,9.09852814 C10.4843146,8.88137085 10.35,8.58137085 10.35,8.25 L10.35,6.9 C10.35,6.56862915 10.4843146,6.26862915 10.7014719,6.05147186 C10.9186292,5.83431458 11.2186292,5.7 11.55,5.7 Z" id="矩形" stroke="#2879FF"></path>
                          <path d="M13.2,12.15 C13.5313708,12.15 13.8313708,12.2843146 14.0485281,12.5014719 C14.2656854,12.7186292 14.4,13.0186292 14.4,13.35 L14.4,14.7 C14.4,15.0313708 14.2656854,15.3313708 14.0485281,15.5485281 C13.8313708,15.7656854 13.5313708,15.9 13.2,15.9 L11.55,15.9 C11.2186292,15.9 10.9186292,15.7656854 10.7014719,15.5485281 C10.4843146,15.3313708 10.35,15.0313708 10.35,14.7 L10.35,13.35 C10.35,13.0186292 10.4843146,12.7186292 10.7014719,12.5014719 C10.9186292,12.2843146 11.2186292,12.15 11.55,12.15 Z" id="矩形" stroke="#141C28"></path>
                          <path d="M5.625,6.075 L5.625,12.225 C5.625,13.2191125 6.43088745,14.025 7.425,14.025 L10.3491986,14.025" id="路径" stroke="#141C28"></path>
                      </g>
                  </g>
              </g>
          </g>
      </g>
    </svg>
  ),
  'ditu': (
    <svg width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
      <title>切片</title>
      <g id="公司目标" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g id="初始化-周期进行中-" transform="translate(-36.000000, -321.000000)">
              <g id="主菜单" transform="translate(16.000000, 74.000000)">
                  <g id="编组-3" transform="translate(12.000000, 196.000000)">
                      <g id="ditu" transform="translate(8.000000, 51.000000)">
                          <rect id="矩形" stroke="#141C28" stroke-width="1.2" x="6.675" y="2.55" width="4.65" height="3.6" rx="1.8"></rect>
                          <rect id="矩形" stroke="#141C28" stroke-width="1.2" x="12.6" y="12.15" width="3.3" height="3.3" rx="1.65"></rect>
                          <rect id="矩形" stroke="#141C28" stroke-width="1.2" x="2.1" y="12.15" width="3.3" height="3.3" rx="1.65"></rect>
                          <polygon id="矩形" fill="#141C28" points="8.4 5.55 9.6 5.55 9.6 12.3 8.4 12.3"></polygon>
                          <rect id="矩形" stroke="#2879FF" stroke-width="1.2" x="7.35" y="12.15" width="3.3" height="3.3" rx="1.65"></rect>
                          <path d="M3.75,12.15 L3.75,10.725 C3.75,9.73088745 4.56756698,8.925 5.57608696,8.925 L12.423913,8.925 C13.432433,8.925 14.25,9.73088745 14.25,10.725 L14.25,12.15" id="路径" stroke="#141C28" stroke-width="1.2"></path>
                      </g>
                  </g>
              </g>
          </g>
      </g>
    </svg>
  ),
  'indicators': (
    <svg width="18px" height="18px" viewBox="0 0 240 240" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
    <title>my-uniEA10</title>
    <g id="my-uniEA10" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <path d="M112,178 L112,62 C112,44.326888 97.673112,30 80,30 L62,30 C44.326888,30 30,44.326888 30,62 L30,178 C30,195.673112 44.326888,210 62,210 L80,210 C97.673112,210 112,195.673112 112,178 Z M96,62 L96,178 C96,186.836556 88.836556,194 80,194 L62,194 C53.163444,194 46,186.836556 46,178 L46,62 C46,53.163444 53.163444,46 62,46 L80,46 C88.836556,46 96,53.163444 96,62 Z M210,94 L210,62 C210,44.326888 195.673112,30 178,30 L160,30 C142.326888,30 128,44.326888 128,62 L128,94 C128,111.673112 142.326888,126 160,126 L178,126 C195.673112,126 210,111.673112 210,94 Z M194,62 L194,94 C194,102.836556 186.836556,110 178,110 L160,110 C151.163444,110 144,102.836556 144,94 L144,62 C144,53.163444 151.163444,46 160,46 L178,46 C186.836556,46 194,53.163444 194,62 Z" id="形状结合" fill="#141C28" fill-rule="nonzero"></path>
        <path d="M171,135 L167,135 C149.326888,135 135,149.326888 135,167 L135,185 C135,202.673112 149.326888,217 167,217 L171,217 C188.673112,217 203,202.673112 203,185 L203,167 C203,149.326888 188.673112,135 171,135 Z M167,151 L171,151 C179.836556,151 187,158.163444 187,167 L187,185 C187,193.836556 179.836556,201 171,201 L167,201 C158.163444,201 151,193.836556 151,185 L151,167 C151,158.163444 158.163444,151 167,151 Z" id="矩形" fill="#2879FF" fill-rule="nonzero" transform="translate(169.000000, 176.000000) rotate(-270.000000) translate(-169.000000, -176.000000) "></path>
    </g>
</svg>
  ),
  'kpi-dashboard': (
    <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" viewBox="0 0 16 16" version="1.1">
    <title>yibiao</title>
    <g id="四期" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="数据统计-概览" transform="translate(-37, -324)">
            <g id="公司战略目标-一级菜单" transform="translate(16, 76)">
                <g id="编组-3" transform="translate(12, 196)">
                    <g id="yibiao" transform="translate(9.5, 52.5)">
                        <path d="M7.5,0 C8.48304384,0 9.44080709,0.189612042 10.3326087,0.553650941 C10.639403,0.678886266 10.7865857,1.02911549 10.6613504,1.33590981 C10.536115,1.64270413 10.1858858,1.78988677 9.87909149,1.66465145 C9.130644,1.35913058 8.32684786,1.2 7.5,1.2 C5.80591146,1.2 4.2195027,1.87104543 3.04527407,3.04527407 C1.87104543,4.2195027 1.2,5.80591146 1.2,7.5 C1.2,9.19372667 1.87102713,10.780175 3.04527407,11.9550709 C4.21934021,13.1291371 5.80570934,13.8 7.5,13.8 C9.1939288,13.8 10.7803377,13.1291553 11.9549537,11.9551881 C13.1291553,10.7803377 13.8,9.1939288 13.8,7.5 C13.8,6.58271912 13.6042811,5.69428554 13.2311066,4.87947104 C13.0931256,4.57819396 13.2255032,4.22210492 13.5267803,4.0841239 C13.8280574,3.94614289 14.1841464,4.07852051 14.3221274,4.37979759 C14.7667571,5.3506321 15,6.4093998 15,7.5 C15,9.51537081 14.2005709,11.4058553 12.8034819,12.8037163 C11.4058553,14.2005709 9.51537081,15 7.5,15 C5.48420566,15 3.59364284,14.200496 2.19662875,12.8034819 C0.799637596,11.4057187 0,9.5152083 0,7.5 C0,5.48436818 0.799712587,3.59377928 2.19674593,2.19674593 C3.59377928,0.799712587 5.48436818,0 7.5,0 Z" id="路径" fill="#141C28" fill-rule="nonzero"/>
                        <path d="M9.06428764,2.90248766 C9.12606842,2.91355287 9.17442806,2.96191251 9.18549327,3.02369329 L10.0496473,7.83724023 C10.0745392,7.92686362 10.0878428,8.0213095 10.0878428,8.1188626 L10.0828428,8.0311126 L10.0878428,8.06181135 L10.0838428,8.0611126 L10.0878428,8.1188626 C10.0878428,8.66010166 9.67833256,9.10569448 9.15225184,9.16270135 L9.03784278,9.1688626 C8.45794379,9.1688626 7.98784278,8.69876159 7.98784278,8.1188626 C7.98784278,8.09953264 7.98836511,8.08032467 7.98939643,8.06125204 L7.99096887,8.03723328 C7.99652604,7.96496069 8.00940013,7.89473708 8.02885162,7.82730201 L8.89019228,3.02369329 C8.90479737,2.94214817 8.98274252,2.88788256 9.06428764,2.90248766 Z" id="形状结合" fill="#2879FF" transform="translate(9.0378, 6.0345) rotate(-315) translate(-9.0378, -6.0345)"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
  ),
  'daiban': (
    <svg width="18px" height="18px" viewBox="0 0 240 240" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
    <title>daiban</title>
    <g id="daiban" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="daiban-s" transform="translate(20.000000, 20.000000)">
            <rect id="矩形" x="4.25531915" y="4.25531915" width="191.489362" height="191.489362" rx="2.12765957"></rect>
            <g id="编组" fill-rule="nonzero">
                <path d="M68.7746033,46.2720784 C73.1095175,46.2720784 76.6391461,49.7199162 76.7707963,54.022899 L76.7746033,54.2720784 L76.7746033,78.2720784 C76.7746033,82.6069927 80.222441,86.1366213 84.5254239,86.2682715 L84.7746033,86.2720784 L182.798989,86.5563496 C187.217268,86.5563496 190.798989,90.1380716 190.798989,94.5563496 C190.798989,98.8912638 187.351152,102.420892 183.048169,102.552543 L182.798989,102.55635 L84.7746033,102.272078 C71.6523175,102.272078 60.9897575,91.7407412 60.7778186,78.6689622 L60.7746033,78.2720784 L60.7746033,54.2720784 C60.7746033,49.8538003 64.3563252,46.2720784 68.7746033,46.2720784 Z" id="路径" fill="#2879FF" transform="translate(125.786796, 74.414214) rotate(-45.000000) translate(-125.786796, -74.414214) "></path>
                <path d="M100,0 C118.725829,0 136.7138,5.16520443 152.332566,14.7732799 C156.095802,17.0882811 157.26983,22.0156664 154.954828,25.7789031 C152.639827,29.5421397 147.712441,30.7161665 143.949205,28.4011653 C130.833628,20.332957 115.743982,16 100,16 C77.4121528,16 56.2600361,24.9472724 40.6036543,40.6036543 C24.9472724,56.2600361 16,77.4121528 16,100 C16,122.583022 24.9470284,143.735667 40.6036543,159.400946 C56.2578695,175.055161 77.4094579,184 100,184 C122.585717,184 143.737835,175.055404 159.399383,159.402509 C175.055404,143.737835 184,122.585717 184,100 C184,95.581722 187.581722,92 192,92 C196.418278,92 200,95.581722 200,100 C200,126.871611 189.340945,152.07807 170.713091,170.716217 C152.07807,189.340945 126.871611,200 100,200 C73.1227421,200 47.9152378,189.339947 29.2883833,170.713091 C10.6618346,152.076249 0,126.869444 0,100 C0,73.124909 10.6628345,47.917057 29.2899457,29.2899457 C47.917057,10.6628345 73.124909,0 100,0 Z" id="形状结合" fill="#141C28"></path>
            </g>
        </g>
    </g>
</svg>
  ),
}

export type IconNames = keyof typeof icons

export default icons