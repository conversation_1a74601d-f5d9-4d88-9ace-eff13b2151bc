import React, { <PERSON> } from 'react'
import Menus from '@/stories/base/Menus'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({ }) => {
  return (
    <Menus selectedKey='1-2' height={200} items={[
      {
        label: 'menu item 1', key: '1', icon: 'my-setup', children: [
          { label: 'menu item 2', key: '1-1' },
          { label: 'menu item 3', key: '1-2' },
      ] },
      { label: 'menu item 2', key: '2', icon: 'my-setup' },
      { label: 'menu item 3', key: '3', icon: 'my-setup' },
      { label: '分组', key: 'group', isGroup: true, children: [
        { label: 'group item 2', key: 'group-1' },
        { label: 'group item 3', key: 'group-2' },
    ] },
      { label: 'menu item 4', key: '4', icon: 'my-setup' },
      { label: 'menu item 5', key: '5', icon: 'my-setup' },
    ]} />
  )
}

export default React.memo(Base)
