import React, { FC, MutableRefObject, useMemo, useRef } from 'react'
import Menus, { IMenusRef, MenusView } from '@/stories/base/Menus'
import Button from '@/stories/base/Button'

export interface IBaseProps { }

const items = [
  {
    label: 'menu item 1', key: '1', icon: 'my-setup', children: [
      { label: 'menu item 2', key: '1-1' },
      { label: 'menu item 3', key: '1-2', children: [
        { label: 'menu item 2', key: '1-1-1' },
        { label: 'menu item 3', key: '1-2-1' },
    ] },
  ] },
  { label: 'menu item 2', key: '2', icon: 'my-setup' },
  { label: 'menu item 3', key: '3', icon: 'my-setup' },
  { label: '分组', key: 'group', isGroup: true },
  { label: 'menu item 4', key: '4', icon: 'my-setup' },
  { label: 'menu item 5', key: '5', icon: 'my-setup' },
]

const Base: FC<IBaseProps> = ({ }) => {
  const views = useMemo<MenusView[]>(() => {
    return [
      {
        key: 'view1',
        title: '主页',
        async renderMenu(props, view) {
          return Promise.resolve([
            {
              label: '1111',
              key: '1111'
            },
            {
              label: '2222',
              key: '2222'
            },
          ])
        },
      }
    ]
  }, [])

  const menuRef = useRef() as MutableRefObject<IMenusRef>
  return (
    <div style={{ width: 200 }}>
      <Menus ref={menuRef} views={views} items={items} onChange={(key, item, viewKey) => {
        console.log('key', key)
        console.log('item', item)
        console.log('viewKey', viewKey)
      }} />
      <div>
        <Button onClick={() => {
          menuRef.current.changeView('view1')
        }}>加载 view1</Button>
      </div>
    </div>
  )
}

export default React.memo(Base)
