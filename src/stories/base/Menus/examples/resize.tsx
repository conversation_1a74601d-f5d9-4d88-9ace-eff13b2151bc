import React, { <PERSON> } from 'react'
import Menus from '@/stories/base/Menus'
import ResizeWrapper from '@/stories/base/Resize'

export interface IResizeProps {}

const Resize: FC<IResizeProps> = ({ }) => {
  return (
    <ResizeWrapper width={180} minWidth={180} className="rounded-xl border border-gray-100 overflow-hidden shadow-lg">
      <Menus selectedKey='13' height={400} shadowTheme="white" items={[
        {
          label: 'menu item 1', key: '1', icon: 'my-setup', children: [
            { label: 'menu item 2', key: '1-1' },
            { label: 'menu item 3', key: '1-2' },
        ] },
        { label: 'menu item 2', key: '2', icon: 'my-setup' },
        { label: 'menu item 3', key: '3', icon: 'my-setup' },
        { label: '分组', key: 'group', isGroup: true },
        { label: 'menu item 4', key: '4', icon: 'my-setup' },
        { label: 'menu item 5', key: '5', icon: 'my-setup' },
        { label: 'menu item 6', key: '6', icon: 'my-setup' },
        { label: 'menu item 7', key: '7', icon: 'my-setup' },
        { label: 'menu item 8', key: '8', icon: 'my-setup' },
        { label: 'menu item 9', key: '9', icon: 'my-setup' },
        { label: 'menu item 10', key: '10', icon: 'my-setup' },
        { label: 'menu item 11', key: '11', icon: 'my-setup' },
        { label: 'menu item 12', key: '12', icon: 'my-setup' },
        { label: 'menu item 13', key: '13', icon: 'my-setup' },
        { label: 'menu item 14', key: '14', icon: 'my-setup' },
        { label: 'menu item 15', key: '15', icon: 'my-setup' },
        { label: 'menu item 16', key: '16', icon: 'my-setup' },
        { label: 'menu item 17', key: '17', icon: 'my-setup' },
        { label: 'menu item 18', key: '18', icon: 'my-setup' },
        { label: 'menu item 19', key: '19', icon: 'my-setup' },
        { label: 'menu item 20', key: '20', icon: 'my-setup' },
      ]} />
    </ResizeWrapper>
  )
}

export default React.memo(Resize)
