.tita-ui-menus {
  min-width: 180px;
  padding: 20px 12px;
  background-color: #fff;
  box-shadow: 0px 0px 16px 0px rgba(127, 145, 180, 0.1);
  border-radius: 12px;
  box-sizing: border-box;

  &__item {
    height: 40px;
    border-radius: 12px;
    padding: 0 11px;
    height: 40px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color .3s;
    display: flex;
    justify-content: space-between;

    &--isGroup {
      padding: 0 12px;
      color: #89919f;
      font-size: 12px;
      font-weight: 400;
      cursor: default;
    }

    &--isGroupMenu {
      padding: 0 12px;
      color: #89919f;
      font-size: 12px;
      font-weight: 400;
      cursor: pointer;
    }

    &--active {
      background-color: #E9F1FF;
      color: #2878ff;
    }

    &-icon {
      width: 16px;
      height: 16px;
      margin-right: 7px;
      position: relative;
      flex-shrink: 0;

      img {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    &-suffix {
      min-width: 48px;
      width: 48px;
      height: 18px;
      margin-left: 8px;
      display: inline-block;
      background-image: url('./assets/icons/setting.svg');
      background-size: contain;
      background-repeat: no-repeat;
    }
  }

  &__childrens {
    transition: height .3s;
    overflow: hidden;
  }

  &__down-icon {
    i {
      font-size: 12px !important;
    }
  }
}