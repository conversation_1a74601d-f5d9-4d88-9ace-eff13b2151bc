import React, { FC } from 'react'
import classNames from 'classnames'
import styled from 'styled-components'
import { getRgba } from '@/utils/color'
import './index.scss'

export interface IInnerShadowProps {
  top?: boolean
  right?: boolean
  bottom?: boolean
  left?: boolean
  theme?: 'default' | 'white'
  /** 是否线性, 只有设置了 customColor 才生效 */
  linear?: boolean
  size?: number
  customColor?: string
  zIndex?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-inner-shadow'

export const InnerShadow: FC<IInnerShadowProps> = React.memo((props) => {
  const { top, left, bottom, right, theme = 'default', zIndex } = props;
  const commonCls = classNames({
    [`${preCls}--theme-${theme}`]: theme !== 'default',
  })
  return (
    <>
      <InnerShadowTopStyle {...props} show={top} zIndex={zIndex} className={classNames(`${preCls}--top`, commonCls)} />
      <InnerShadowRightStyle {...props} show={right} zIndex={zIndex} className={classNames(`${preCls}--right`, commonCls)} />
      <InnerShadowBottomStyle {...props} show={bottom} zIndex={zIndex} className={classNames(`${preCls}--bottom`, commonCls)} />
      <InnerShadowLeftStyle {...props} show={left} zIndex={zIndex} className={classNames(`${preCls}--left`, commonCls)} />
    </>
  )
})

function getShadow(props: IInnerShadowProps) {
  if (props.linear && props.customColor) return '0 0 0 0px rgba(0, 0, 0, 0)'
  if (props.customColor) {
    const { r, g, b, a } = getRgba(props.customColor)
    return `0 0 ${(props.size || 20) - 5}px 0px rgba(${r}, ${g}, ${b}, ${a})`
  }
  return `0 0 ${(props.size || 20) - 5}px 0px rgba(0, 0, 0, 0.12)`
}

const InnerShadowTopStyle = styled.div<IInnerShadowProps & { show?: boolean, zIndex?: number }>`
  position: absolute;
  pointer-events: none !important;
  top: 0;
  left: 0;
  width: 100%;
  height: ${props => `${props.size || 20}px`};
  overflow: hidden;
  z-index: ${({ zIndex = 10 }) => zIndex} !important;
  transition: opacity 0.3s;
  opacity: ${props => props.show ? 1 : 0};

  ${({ linear, customColor }) => {
    if (!linear || !customColor) return ``
    return `background-image: linear-gradient(to bottom, ${customColor} 0%, rgba(255, 255, 255, 0) 100%);`
  }}

  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    transform: translateY(-100%);
    border-radius: 20%;
    box-shadow: ${getShadow};
  }
`
const InnerShadowRightStyle = styled.div<IInnerShadowProps & { show?: boolean, zIndex?: number }>`
  position: absolute;
  pointer-events: none !important;
  top: 0;
  right: 0;
  width: ${props => `${props.size || 20}px`};
  height: 100%;
  overflow: hidden;
  z-index: ${({ zIndex = 10 }) => zIndex} !important;
  transition: opacity 0.3s;
  opacity: ${props => props.show ? 1 : 0};

  ${({ linear, customColor }) => {
    if (!linear || !customColor) return ``
    return `background-image: linear-gradient(to left, ${customColor} 0%, rgba(255, 255, 255, 0) 100%);`
  }}

  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    transform: translateX(100%);
    border-radius: 20%;
    box-shadow: ${getShadow};
  }
`
const InnerShadowBottomStyle = styled.div<IInnerShadowProps & { show?: boolean, zIndex?: number }>`
  position: absolute;
  pointer-events: none !important;
  bottom: 0;
  left: 0;
  width: 100%;
  height: ${props => `${props.size || 20}px`};
  overflow: hidden;
  z-index: ${({ zIndex = 10 }) => zIndex} !important;
  transition: opacity 0.3s;
  opacity: ${props => props.show ? 1 : 0};

  ${({ linear, customColor }) => {
    if (!linear || !customColor) return ``
    return `background-image: linear-gradient(to top, ${customColor} 0%, rgba(255, 255, 255, 0) 100%);`
  }}

  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    bottom: 0;
    transform: translateY(100%);
    border-radius: 20%;
    box-shadow: ${getShadow};
  }
`
const InnerShadowLeftStyle = styled.div<IInnerShadowProps & { show?: boolean, zIndex?: number }>`
  position: absolute;
  pointer-events: none !important;
  top: 0;
  left: 0;
  width: ${props => `${props.size || 20}px`};
  height: 100%;
  overflow: hidden;
  z-index: ${({ zIndex = 10 }) => zIndex} !important;
  transition: opacity 0.3s;
  opacity: ${props => props.show ? 1 : 0};

  ${({ linear, customColor }) => {
    if (!linear || !customColor) return ``
    return `background-image: linear-gradient(to right, ${customColor} 0%, rgba(255, 255, 255, 0) 100%);`
  }}

  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    transform: translateX(-100%);
    border-radius: 20%;
    box-shadow: ${getShadow};
  }
`

export default InnerShadow
