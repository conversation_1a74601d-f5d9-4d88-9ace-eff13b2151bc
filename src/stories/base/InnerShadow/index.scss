.tita-ui-inner-shadow {
  
  &--theme-white.tita-ui-inner-shadow--top {
    background-image: linear-gradient(to bottom, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    &::before {
      box-shadow: 0 0 0 0px rgba(0, 0, 0, 0);
    }
  }
  &--theme-white.tita-ui-inner-shadow--right {
    background-image: linear-gradient(to left, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    &::before {
      box-shadow: 0 0 0 0px rgba(0, 0, 0, 0);
    }
  }
  &--theme-white.tita-ui-inner-shadow--bottom {
    background-image: linear-gradient(to top, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    &::before {
      box-shadow: 0 0 0 0px rgba(0, 0, 0, 0);
    }
  }
  &--theme-white.tita-ui-inner-shadow--left {
    background-image: linear-gradient(to right, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    &::before {
      box-shadow: 0 0 0 0px rgba(0, 0, 0, 0);
    }
  }
}