import React, { <PERSON> } from 'react'
import InnerShadow from '@/stories/base/InnerShadow'
import Panel from '../../Panel'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({ }) => {
  return (
    <div className="space-y-10px">
      <Panel title="默认样式">
        <div className="relative h-20">
          <InnerShadow customColor="#7c0ac8" top right bottom left />
        </div>
      </Panel>
      <Panel title="自定义阴影尺寸">
        <div className="relative h-20">
          <InnerShadow customColor="#ffdd00" size={30} top right bottom left />
        </div>
      </Panel>
      <Panel title="线性阴影">
        <div className="relative h-20">
          <InnerShadow customColor="#ffdd00" size={30} linear top right bottom left />
        </div>
      </Panel>
      <Panel title="支持 rgba 透明度">
        <div className="relative h-20">
          <InnerShadow customColor="rgba(255, 221, 0, .2)" linear size={30} top right bottom left />
        </div>
      </Panel>
    </div>
  )
}

export default React.memo(Base)
