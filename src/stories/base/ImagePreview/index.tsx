import React, { FC, MutableRefObject, useEffect, useRef, forwardRef, useImperativeHandle, useState } from 'react'
import classNames from 'classnames'
import Viewer from 'viewerjs'
import 'viewerjs/dist/viewer.min.css'
import './index.scss'

export interface IImagePreviewProps {
  images?: string[]
  index?: number
  onClose?: () => void
  options?: Viewer.Options
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

export interface IImagePreviewRef {
  show: (images?: string | string[]) => void
  showIndex: (index?: number) => void
  destroy: () => void
}

const preCls = 'tita-ui-image-preview'

export const ImagePreview = React.memo(forwardRef<IImagePreviewRef, IImagePreviewProps>(({ images: imagesProps, index, children, onClose, options: propsOptions, className, style }, ref) => {
  const [images, setImages] = useState<string[]>(imagesProps || [])
  const imagePreviewRef = useRef() as MutableRefObject<HTMLDivElement>
  const viewerRef = useRef<Viewer>()

  useImperativeHandle(ref, () => ({
    showIndex(index) {
      viewerRef.current?.view(index)
    },
    show(images) {
      if (images) setImages(() => Array.isArray(images) ? images : [images])
      setTimeout(() => {
        viewerRef.current?.view()
      }, 0)
    },
    destroy() {
      viewerRef.current?.destroy()
    },
  }))

  useEffect(() => {
    let options = {
      ...propsOptions,
      hidden: () => {
        onClose?.()
      }
    };
    viewerRef.current = new Viewer(imagePreviewRef.current, options);
    return () => {
      viewerRef.current?.destroy()
    }
  }, [images])

  return (
    <>
      <div style={{ display: 'none' }} ref={imagePreviewRef}>
        {
          images?.map((url, index) => (
            <img src={url} key={index} style={{display: 'none'}}/>
          ))
        }
      </div>
      {children && (
        <div className={classNames(preCls, className)} style={style} onClick={() => viewerRef.current?.view()}>
          {children}
        </div>
      )}
    </>
  )
}))

export default ImagePreview
