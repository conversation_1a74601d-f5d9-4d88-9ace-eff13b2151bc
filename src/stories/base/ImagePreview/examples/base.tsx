import React, { FC, MutableRefObject, useRef } from 'react'
import ImagePreview, { IImagePreviewRef } from '@/stories/base/ImagePreview'
import Button from '../../Button'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({ }) => {
  const imagePreviewRef = useRef<IImagePreviewRef>() as MutableRefObject<IImagePreviewRef>
  return (
    <>
      <ImagePreview ref={imagePreviewRef} images={['https://img.zcool.cn/community/0165655d445222a8012187f47cee91.jpg@1280w_1l_2o_100sh.jpg']}>
        <Button onClick={() => imagePreviewRef.current.show()}>预览</Button>
      </ImagePreview>
    </>
  )
}

export default React.memo(Base)
