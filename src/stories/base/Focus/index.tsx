import React, { FC, useRef, useEffect, useMemo } from 'react'
import './index.scss'
import classNames from 'classnames'
import { getRandomStr } from '@tita/utils'

export interface IFocusProps
  extends Omit<
    React.DetailedHTMLProps<
      React.HTMLAttributes<HTMLDivElement>,
      HTMLDivElement
    >,
    'onFocus' | 'onBlur'
  > {
  /**
   * 严格模式，约束 onFocus 和 onBlur 的触发时机
   * focus 默认为 false
   * 点击过一次之后，才会触发 onBlur，再次点击后，才会触发下一次 onBlur
   * */
  strict?: boolean
  onFocus?: (state: true) => void
  onBlur?: (state: false) => void
  /** 排除指定的类名，当命中指定的类名时，不会触发失焦事件 */
  excludeClassName?: string[]
  children?: React.ReactNode
}

const preCls = 'tita-ui-focus'

export const Focus: FC<IFocusProps> = React.memo(
  ({
    children,
    onFocus,
    onBlur,
    strict,
    excludeClassName,
    className,
    ...divProps
  }) => {
    const isFocusRef = useRef(strict ? false : true)
    const focusRef = useRef<any>()
    const downRef = useRef({ x: 0, y: 0 })
    useEffect(() => {
      const focusDom: HTMLDivElement = focusRef.current
      function mouseupHandler(this: HTMLElement, ev: MouseEvent) {
        // 禁止在拖拽时触发
        if (Math.abs(ev.clientX - downRef.current.x) > 10) return
        if (Math.abs(ev.clientY - downRef.current.y) > 10) return

        // @ts-ignore
        let parentNode: HTMLElement = ev.target
        while (parentNode !== window.document.body) {
          if (parentNode === focusDom) {
            if (onFocus && !isFocusRef.current) onFocus(true)
            isFocusRef.current = true
            return
          }
          // 排除指定的类名
          if (
            excludeClassName &&
            parentNode?.className &&
            parentNode?.className
              ?.split(' ')
              .find((className) => excludeClassName.includes(className))
          ) {
            if (onFocus && !isFocusRef.current) onFocus(true)
            isFocusRef.current = true
            return
          }
          parentNode = parentNode?.parentNode as HTMLElement
        }
        if (onBlur) {
          if (!strict) onBlur(false)
          if (strict && isFocusRef.current) onBlur(false)
        }
        isFocusRef.current = false
      }
      function mouseDownHandler(e: MouseEvent) {
        downRef.current = { x: e.clientX, y: e.clientY }
      }
      window.document.body.addEventListener('mouseup', mouseupHandler, true)
      window.document.body.addEventListener('mousedown', mouseDownHandler, true)
      return () => {
        window.document.body.removeEventListener(
          'mouseup',
          mouseupHandler,
          true
        )
        window.document.body.removeEventListener(
          'mousedown',
          mouseDownHandler,
          true
        )
      }
    }, [])
    return (
      <div
        ref={focusRef}
        className={classNames(preCls, className)}
        {...divProps}
      >
        {children}
      </div>
    )
  }
)
export interface IFocusV2Props {
  /**
   * 严格模式，约束 onFocus 和 onBlur 的触发时机
   * focus 默认为 false
   * 点击过一次之后，才会触发 onBlur，再次点击后，才会触发下一次 onBlur
   * */
  strict?: boolean
  onFocus?: (state: true) => void
  onBlur?: (state: false) => void
  /** 排除指定的类名，当命中指定的类名时，不会触发失焦事件 */
  excludeClassName?: string[]
  children: React.ReactElement
}

/**
 * V2 版本跟 V1 版本区别：不会生成多余的 DOM 节点
 */
export const FocusV2: FC<IFocusV2Props> = React.memo(
  ({ children, onFocus, onBlur, strict, excludeClassName }) => {
    const isFocusRef = useRef(strict ? false : true)
    const downRef = useRef({ x: 0, y: 0 })
    const id = useMemo(() => getRandomStr(), [])
    useEffect(() => {
      function mouseupHandler(this: HTMLElement, ev: MouseEvent) {
        // 根据 data-docus-id 找到元素
        const focusDom: HTMLDivElement | null = document.querySelector(
          `[data-docus-id="${id}"]`
        )
        if (!focusDom) return

        // 禁止在拖拽时触发
        if (Math.abs(ev.clientX - downRef.current.x) > 10) return
        if (Math.abs(ev.clientY - downRef.current.y) > 10) return

        // @ts-ignore
        let parentNode: HTMLElement = ev.target
        while (parentNode !== window.document.body) {
          if (parentNode === focusDom) {
            if (onFocus && !isFocusRef.current) onFocus(true)
            isFocusRef.current = true
            return
          }
          // 排除指定的类名
          if (
            excludeClassName &&
            parentNode?.className &&
            parentNode?.className
              ?.split(' ')
              .find((className) => excludeClassName.includes(className))
          ) {
            return
          }
          parentNode = parentNode?.parentNode as HTMLElement
        }
        if (onBlur) {
          if (!strict) onBlur(false)
          if (strict && isFocusRef.current) onBlur(false)
        }
        isFocusRef.current = false
      }
      function mouseDownHandler(e: MouseEvent) {
        downRef.current = { x: e.clientX, y: e.clientY }
      }
      window.document.body.addEventListener('mouseup', mouseupHandler, true)
      window.document.body.addEventListener('mousedown', mouseDownHandler, true)
      return () => {
        window.document.body.removeEventListener(
          'mouseup',
          mouseupHandler,
          true
        )
        window.document.body.removeEventListener(
          'mousedown',
          mouseDownHandler,
          true
        )
      }
    }, [id])
    const _children = React.cloneElement(children, {
      ['data-docus-id']: id,
      ...children.props,
    })
    return _children
  }
)

export default Focus
