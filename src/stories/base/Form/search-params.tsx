import React, { FC, JSXElementConstructor, ReactElement } from 'react'
import classNames from 'classnames'
import './index.scss'

export interface ISearchParamsProps {
  label?: string
  name: string
  value?: any
  initialValue?: any
  require?: boolean
  message?: string
  initialValueName?: string
  valueName?: string
  point?: string
  format?: (...args: any[]) => any
  onChange?: (name: string, value: string | {},) => void
  icon?: string
  children: ReactElement<any, string | JSXElementConstructor<any>>
  className?: string
  style?: React.CSSProperties
  onChangeHandleName?: string
}

const preCls = 'tita-ui-form-search-item'

export const SearchParamsItem: FC<ISearchParamsProps> = (props) => {
  const { require, message, point, name, label, onChange, children, className, style, onChangeHandleName, initialValue, value, initialValueName, valueName  } = props

  const childrenProps = {
    ...children?.props,
    initialValue,
    value,
    ...(initialValueName ? {
      [initialValueName]: initialValue
    } : {}),
    ...(valueName ? {
      [valueName]: value
    } : {}),
    ...(onChangeHandleName ? {
      [onChangeHandleName]: (value: any) => {
        onChange && onChange(name, value)
      }
    } : {
      onChange: (value: any) => {
        onChange && onChange(name, value)
      }
    }),
  }
  
  const _children = React.cloneElement(children, childrenProps)

  return (
    <div className={classNames(preCls, className)} style={style}>
      {_children}
    </div>
  )
}

export default React.memo(SearchParamsItem)
