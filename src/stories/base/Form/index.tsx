import classNames from 'classnames'
import React, {
  JSXElementConstructor,
  ReactElement,
  forwardRef,
  useImperativeHandle,
  useRef,
} from 'react'
import './index.scss'
import { useRefState } from '@tita/hooks'
export * from './item'
export * from './search-params'

export interface IFormProps {
  type?: 'form' | 'search-params'
  space?: 8 | 16 | 20 | 22 | 32 | 0 | 12
  initialValues?: any
  titleMinWidth?: number | string
  onFieldsChange?: (
    name: string,
    value: any,
    formData: Record<string, any>
  ) => void
  onFinish?: (formData: Record<string, any>) => void
  onReady?: (ready: boolean) => void
  getFormContainer?: () => HTMLDivElement
  children?:
    | ReactElement<any, string | JSXElementConstructor<any>>
    | ReactElement<any, string | JSXElementConstructor<any>>[]
  className?: string
  style?: React.CSSProperties
}
export interface IFormRef {
  setField: (
    name: string,
    value: any,
    options?: {
      /** 仅修改值，不触发 onFiledsChange */
      notEmitChange?: boolean
    }
  ) => void
  getFormData: () => any
  clear: () => void
  set: (formData: Record<string, any>) => void
}

const preCls = 'tita-ui-form'

export const Form = React.memo(
  forwardRef<IFormRef, IFormProps>(
    (
      {
        type = 'form',
        initialValues,
        children,
        onFieldsChange,
        titleMinWidth,
        space = 8,
        onReady,
        onFinish,
        getFormContainer,
        className,
        style,
      },
      ref
    ) => {
      const [fieldsValue, fieldsValueRef, setFieldsValue] = useRefState<
        Record<string, any>
      >(initialValues || {})
      const fieldRefs = useRef<any>({})
      const initedRef = useRef(false)

      const requireFieldsRef = useRef<string[]>([])
      const readyRef = useRef<boolean | -1>(-1)

      const onFieldChangeHandler = (
        name: string,
        value: any,
        options?: {
          notEmitChange?: boolean
        }
      ) => {
        const { notEmitChange } = options || {}
        const values = { ...fieldsValueRef.current }
        values[name] = value
        setFieldsValue(values)
        if (!notEmitChange && onFieldsChange)
          onFieldsChange(name, value, values)
        dispatchReady()
      }

      const onStoreRef = (name: string, ref: any) => {
        fieldRefs.current[name] = ref
      }

      const getContainer = () => {
        if (getFormContainer) return getFormContainer()
        return window.document.body
      }

      useImperativeHandle(ref, () => ({
        // verifing: (opts) => {
        //   const { onlyFirstError } = opts || {};
        //   let errors = [];
        //   const fieldsKeys = Object.keys(fieldRefs.current);
        //   for (let i = 0; i < fieldsKeys.length; i += 1) {
        //     const fieldRef = fieldRefs.current[fieldsKeys[i]];
        //     const fieldErrors = fieldRef.verifing();
        //     if (fieldErrors && fieldErrors.length) {
        //       errors = errors.concat(fieldErrors);
        //       if (onlyFirstError) {
        //         break;
        //       }
        //     }
        //   }
        //   return errors;
        // },
        set: (formData: Record<string, any>) => {
          setFieldsValue(formData)
        },
        setField: onFieldChangeHandler,
        getFormData: () => fieldsValueRef.current,
        clear: () => (fieldsValueRef.current = {}),
      }))

      const dispatchReady = () => {
        if (!onReady) return

        if (!requireFieldsRef.current.length && !readyRef.current) {
          onReady(true)
          readyRef.current = true
          return
        }

        // 查找是否有必填且未填写的字段
        const notReady = requireFieldsRef.current.find((name) => {
          if (fieldsValueRef.current[name] === undefined) return true
          if (
            Array.isArray(fieldsValueRef.current[name]) &&
            fieldsValueRef.current[name].length === 0
          )
            return true
          if (
            typeof fieldsValueRef.current[name] === 'object' &&
            JSON.stringify(fieldsValueRef.current[name]) === '{}'
          )
            return true
          if (fieldsValueRef.current[name] === '') return true
        })
        if (notReady) {
          onReady(false)
          readyRef.current = false
        } else {
          onReady(true)
          readyRef.current = true
        }
      }

      const renderChilds = () => {
        const childs = React.Children.map(children, (child, i: number) => {
          if (!child) return null
          const { name, format, require } = child.props
          if (require && !requireFieldsRef.current.includes(name))
            requireFieldsRef.current.push(name)
          // @ts-ignore
          if (i === children.length - 1) {
            initedRef.current = true
          }
          return React.cloneElement(child, {
            ref: (ref: any) => onStoreRef(name, ref),
            onChange: (name: string, value: any) => {
              onFieldChangeHandler(name, format ? format(value) : value)
            },
            titleMinWidth,
            initialValue: initialValues?.[name] ?? undefined,
            value: fieldsValueRef.current[name],
            getContainer,
            ...child.props,
          })
        })

        dispatchReady()

        return childs
      }

      return (
        <div
          className={classNames(
            preCls,
            {
              [`${preCls}--type-${type}`]: type,
              ['flex flex-wrap child-b-8px']: type === 'form' && space === 8,
              ['flex space-x-8px']: type === 'search-params' && space === 8,
              ['flex flex-wrap child-b-16px']: type === 'form' && space === 16,
              ['flex space-x-16px']: type === 'search-params' && space === 16,
              ['flex flex-wrap child-b-20px']: type === 'form' && space === 20,
              ['flex space-x-20px']: type === 'search-params' && space === 20,
              ['flex flex-wrap child-b-22px']: type === 'form' && space === 22,
              ['flex space-x-22px']: type === 'search-params' && space === 22,
              ['flex flex-wrap child-b-32px']: type === 'form' && space === 32,
              ['flex space-x-32px']: type === 'search-params' && space === 32,
              ['flex space-x-12px']: type === 'search-params' && space === 12,
            },
            className
          )}
          style={style}
        >
          {renderChilds()}
        </div>
      )
    }
  )
)

export default Form
