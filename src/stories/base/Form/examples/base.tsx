import React, { FC, useCallback } from 'react'
import Form, { FormItem } from '@/stories/base/Form'
import { TextArea } from '../../Input'
import Input from '../../Input/input'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  const onFieldsChangeHandler = useCallback((name: string, value: any) => {
    console.log('name, value', name, value)
  }, [])
  return (
    <Form onFieldsChange={onFieldsChangeHandler}>
      <FormItem name='name' label='文本域'>
        <Input placeholder='请输入' />
      </FormItem>
      <FormItem name='content' label='文本域'>
        <TextArea placeholder='请输入' autoSize />
      </FormItem>
    </Form>
  )
}

export default React.memo(Base)
