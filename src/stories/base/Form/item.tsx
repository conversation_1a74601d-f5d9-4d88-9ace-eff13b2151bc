import classNames from 'classnames'
import React, { FC, JSXElementConstructor, ReactElement, useCallback, useMemo, useState } from 'react'
import LineItem from '../LineItem'
import './index.scss'
import Switch from '../Switch'
import { useUpdateEffect } from 'ahooks'
import { SwitchChangeEventHandler } from 'rc-switch'

export interface IFormItemProps {
  label?: string | React.ReactNode
  name: string
  value?: any
  initialValue?: any
  direction?: 'row' | 'column'
  require?: boolean
  message?: string
  span?: number
  onChangeHandleName?: string
  initialValueName?: string
  valueName?: string
  titleMinWidth?: number | string
  /** 是否展示开关 */
  showSwitch?: boolean
  defaultChecked?: boolean
  onCheckedChange?: (checked: boolean) => void
  checked?: boolean
  point?: string
  format?: (...args: any[]) => any
  onChange?: (name: string, value: string) => void
  // rules={[{ required: require, message: message }]}
  icon?: string
  children: ReactElement<any, string | JSXElementConstructor<any>>
  className?: string
  contentClassName?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-form-item'

export const FormItem: FC<IFormItemProps> = ({ direction, require, span: spanWidth = 24, value, initialValue, titleMinWidth, message, showSwitch, defaultChecked, onCheckedChange, checked, icon, point, name, label, onChange, children, className, onChangeHandleName, valueName, initialValueName, contentClassName, style }) => {

  const [isChecked, setIsChecked] = useState(defaultChecked !== undefined ? defaultChecked : checked)
  
  useUpdateEffect(() => {
    setIsChecked(checked)
  }, [checked])

  const childrenProps = {
    ...children?.props,
    initialValue,
    value,
    ...(initialValueName ? {
      [initialValueName]: initialValue
    } : {}),
    ...(valueName ? {
      [valueName]: value
    } : {}),
    ...(onChangeHandleName ? {
      [onChangeHandleName]: (value: any) => {
        onChange && onChange(name, value)
      }
    } : {
      onChange: (value: any) => {
        onChange && onChange(name, value)
      }
    }),
  }

  const _children = React.cloneElement(children, childrenProps)

  const onCheckedChangeHandler: SwitchChangeEventHandler = useCallback((checked: boolean) => {
    onCheckedChange?.(checked)
    setIsChecked(checked)
  }, [onCheckedChange])

  const renderLabel = useMemo(() => {
    if (showSwitch) {
      return (
        <span className="flex items-center space-x-20px">
          <span>{label}</span>
          <Switch className='mb-2' checked={isChecked} onChange={onCheckedChangeHandler} />
        </span>
      )
    } else {
      return label
    }
  }, [isChecked, label])

  const width = spanWidth / 24 * 100

  return (
    <LineItem require={require} title={renderLabel} icon={icon} point={point} titleMinWidth={titleMinWidth} direction={direction} className={classNames(preCls, className)} contentClassName={contentClassName} style={{ ...style, width: `${width}%` }}>
      {_children}
    </LineItem>
  )
}

export default React.memo(FormItem)
