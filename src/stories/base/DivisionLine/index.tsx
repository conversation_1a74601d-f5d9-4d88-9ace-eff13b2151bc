import classNames from 'classnames'
import React, { FC, useMemo } from 'react'
import { ILinkProps, Link } from '../Link'
import './index.scss'

export interface IDivisionLineProps extends ILinkProps {
  direction?: 'row' | 'column'
  /** 垂直排列线条渐变 */
  columnGradient?: boolean
  margin?: number
  noWrap?: boolean
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-division-line'

export const Line: FC<IDivisionLineProps> = React.memo(
  ({ direction = 'row', className, margin = 10, columnGradient, style }) => {
    return (
      <div
        className={classNames(preCls, className, {
          [`${preCls}--column`]: direction === 'column',
          [`${preCls}--column--columnGradient`]: direction === 'column' && columnGradient,
        })}
        style={{
          marginLeft: direction === 'row' ? margin : undefined,
          marginRight: direction === 'row' ? margin : undefined,
          marginTop: direction === 'column' ? margin : undefined,
          marginBottom: direction === 'column' ? margin : undefined,
          width: direction === 'column' ? '100%' : undefined,
          ...style,
        }}
      />
    )
  }
)

export const DivisionLine: FC<IDivisionLineProps> = React.memo(
  ({
    direction = 'row',
    children,
    className,
    margin = 10,
    linkItem,
    columnGradient,
    ...other
  }) => {
    const line = useMemo(
      () => (
        <div
          className={classNames(preCls, {
            [`${preCls}--column`]: direction === 'column',
          [`${preCls}--column--columnGradient`]: direction === 'column' && columnGradient,
          })}
        />
      ),
      []
    )
    return (
      <Link
        linkItem={linkItem || line}
        direction={direction}
        margin={margin}
        {...other}
      >
        {children}
      </Link>
    )
  }
)

export default DivisionLine
