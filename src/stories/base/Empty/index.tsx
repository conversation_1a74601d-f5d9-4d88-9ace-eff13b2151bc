import React, { FC, useMemo } from 'react'
import classNames from 'classnames'
import { emptyPng } from './img'
import './index.scss'

export interface IEmptyProps {
  minHeight?: number | string
  children?: React.ReactNode
  className?: string
  emptyImage?: string | React.ReactElement
  disableLogo?: boolean
  size?: 'large' | 'default' | 'small' | 'mini'
  style?: React.CSSProperties
  contentClassName?: string
  contentStyle?: React.CSSProperties
}

const preCls = 'tita-ui-empty'

export const Empty: FC<IEmptyProps> = React.memo(({ className, style, emptyImage, contentClassName, disableLogo, contentStyle, children, minHeight, size = 'default' }) => {
  const empty = useMemo(() => {
    if (disableLogo) return <></>

    if (emptyImage) {
      if (typeof emptyImage === 'string') {
        return <img className={`${preCls}__empty-png`} src={emptyImage} alt="empty" />
      } else {
        return emptyImage
      }
    }

    return <img className={`${preCls}__empty-png`} src={emptyPng} alt="empty" />
  }, [disableLogo])

  return (
    <div className={classNames(preCls, className, {
      [`${preCls}--size-${size}`]: size
    })} style={{ minHeight, ...style }}>
      {empty}
      <div className={classNames(`${preCls}__content`, contentClassName)} style={contentStyle}>
        {children}
      </div>
    </div>
  )
})

export default Empty
