import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'

export interface IFilePreviewProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-file-preview'

export const FilePreview: FC<IFilePreviewProps> = React.memo(({ className, style }) => {
  return (
    <div className={classNames(preCls, className)} style={style}>
      FilePreview
    </div>
  )
})

export default FilePreview
