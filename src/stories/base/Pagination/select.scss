.titaui-pagination__select {
  display: inline-block;
  font-size: 12px;
  width: 100px;
  position: relative;
}
.titaui-pagination__select-disabled,
.titaui-pagination__select-disabled input {
  cursor: not-allowed;
}
.titaui-pagination__select-disabled .titaui-pagination__select-selector {
  opacity: 0.3;
}
.titaui-pagination__select-show-arrow.titaui-pagination__select-loading
  .titaui-pagination__select-arrow-icon::after {
  box-sizing: border-box;
  width: 12px;
  height: 12px;
  border-radius: 100%;
  border: 2px solid #999;
  border-top-color: transparent;
  border-bottom-color: transparent;
  transform: none;
  margin-top: 4px;
  animation: rcSelectLoadingIcon 0.5s infinite;
}
.titaui-pagination__select .titaui-pagination__select-selection-placeholder {
  opacity: 0.4;
  pointer-events: none;
}
.titaui-pagination__select .titaui-pagination__select-selection-search-input {
  appearance: none;
}
.titaui-pagination__select
  .titaui-pagination__select-selection-search-input::-webkit-search-cancel-button {
  display: none;
  appearance: none;
}
.titaui-pagination__select-single .titaui-pagination__select-selector {
  display: flex;
  position: relative;
}
.titaui-pagination__select-single
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-search {
  width: 100%;
}
.titaui-pagination__select-single
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-search-input {
  width: 100%;
}
.titaui-pagination__select-single
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-item,
.titaui-pagination__select-single
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-placeholder {
  position: absolute;
  top: 1px;
  left: 3px;
  pointer-events: none;
}
.titaui-pagination__select-single:not(.titaui-pagination__select-customize-input)
  .titaui-pagination__select-selector {
  padding: 1px;
  border: 1px solid #000;
}
.titaui-pagination__select-single:not(.titaui-pagination__select-customize-input)
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-search-input {
  border: none;
  outline: none;
  background: rgba(255, 0, 0, 0.2);
  width: 100%;
}
.titaui-pagination__select-multiple .titaui-pagination__select-selector {
  display: flex;
  flex-wrap: wrap;
  padding: 1px;
  border: 1px solid #000;
}
.titaui-pagination__select-multiple
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-item {
  flex: none;
  background: #bbb;
  border-radius: 4px;
  margin-right: 2px;
  padding: 0 8px;
}
.titaui-pagination__select-multiple
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-item-disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.titaui-pagination__select-multiple
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-overflow {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.titaui-pagination__select-multiple
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-overflow-item {
  flex: none;
  max-width: 100%;
}
.titaui-pagination__select-multiple
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-search {
  position: relative;
  max-width: 100%;
}
.titaui-pagination__select-multiple
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-search-input,
.titaui-pagination__select-multiple
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-search-mirror {
  padding: 1px;
  font-family: system-ui;
}
.titaui-pagination__select-multiple
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-search-mirror {
  position: absolute;
  z-index: 999;
  white-space: nowrap;
  position: none;
  left: 0;
  top: 0;
  visibility: hidden;
}
.titaui-pagination__select-multiple
  .titaui-pagination__select-selector
  .titaui-pagination__select-selection-search-input {
  border: none;
  outline: none;
  background: rgba(255, 0, 0, 0.2);
  width: 100%;
}
.titaui-pagination__select-allow-clear.titaui-pagination__select-multiple
  .titaui-pagination__select-selector {
  padding-right: 20px;
}
.titaui-pagination__select-allow-clear .titaui-pagination__select-clear {
  position: absolute;
  right: 20px;
  top: 0;
}
.titaui-pagination__select-show-arrow.titaui-pagination__select-multiple
  .titaui-pagination__select-selector {
  padding-right: 20px;
}
.titaui-pagination__select-show-arrow .titaui-pagination__select-arrow {
  pointer-events: none;
  position: absolute;
  right: 5px;
  top: 0;
}
.titaui-pagination__select-show-arrow
  .titaui-pagination__select-arrow-icon::after {
  content: '';
  border: 5px solid transparent;
  width: 0;
  height: 0;
  display: inline-block;
  border-top-color: #999;
  transform: translateY(5px);
}
.titaui-pagination__select-focused .titaui-pagination__select-selector {
  border-color: blue !important;
}
.titaui-pagination__select-dropdown {
  border: 1px solid green;
  min-height: 100px;
  position: absolute;
  background: #fff;
}
.titaui-pagination__select-dropdown-hidden {
  display: none;
}
.titaui-pagination__select-item {
  font-size: 16px;
  line-height: 1.5;
  padding: 4px 16px;
}
.titaui-pagination__select-item-group {
  color: #999;
  font-weight: bold;
  font-size: 80%;
}
.titaui-pagination__select-item-option {
  position: relative;
}
.titaui-pagination__select-item-option-grouped {
  padding-left: 24px;
}
.titaui-pagination__select-item-option
  .titaui-pagination__select-item-option-state {
  position: absolute;
  right: 0;
  top: 4px;
  pointer-events: none;
}
.titaui-pagination__select-item-option-active {
  background: green;
}
.titaui-pagination__select-item-option-disabled {
  color: #999;
}
.titaui-pagination__select-item-empty {
  text-align: center;
  color: #999;
}
.titaui-pagination__select-selection__choice-zoom {
  transition: all 0.3s;
}
.titaui-pagination__select-selection__choice-zoom-appear {
  opacity: 0;
  transform: scale(0.5);
}
.titaui-pagination__select-selection__choice-zoom-appear.titaui-pagination__select-selection__choice-zoom-appear-active {
  opacity: 1;
  transform: scale(1);
}
.titaui-pagination__select-selection__choice-zoom-leave {
  opacity: 1;
  transform: scale(1);
}
.titaui-pagination__select-selection__choice-zoom-leave.titaui-pagination__select-selection__choice-zoom-leave-active {
  opacity: 0;
  transform: scale(0.5);
}
.titaui-pagination__select-dropdown-slide-up-enter,
.titaui-pagination__select-dropdown-slide-up-appear {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  transform-origin: 0 0;
  opacity: 0;
  animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  animation-play-state: paused;
}
.titaui-pagination__select-dropdown-slide-up-leave {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  transform-origin: 0 0;
  opacity: 1;
  animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
  animation-play-state: paused;
}
.titaui-pagination__select-dropdown-slide-up-enter.titaui-pagination__select-dropdown-slide-up-enter-active.titaui-pagination__select-dropdown-placement-bottomLeft,
.titaui-pagination__select-dropdown-slide-up-appear.titaui-pagination__select-dropdown-slide-up-appear-active.titaui-pagination__select-dropdown-placement-bottomLeft {
  animation-name: rcSelectDropdownSlideUpIn;
  animation-play-state: running;
}
.titaui-pagination__select-dropdown-slide-up-leave.titaui-pagination__select-dropdown-slide-up-leave-active.titaui-pagination__select-dropdown-placement-bottomLeft {
  animation-name: rcSelectDropdownSlideUpOut;
  animation-play-state: running;
}
.titaui-pagination__select-dropdown-slide-up-enter.titaui-pagination__select-dropdown-slide-up-enter-active.titaui-pagination__select-dropdown-placement-topLeft,
.titaui-pagination__select-dropdown-slide-up-appear.titaui-pagination__select-dropdown-slide-up-appear-active.titaui-pagination__select-dropdown-placement-topLeft {
  animation-name: rcSelectDropdownSlideDownIn;
  animation-play-state: running;
}
.titaui-pagination__select-dropdown-slide-up-leave.titaui-pagination__select-dropdown-slide-up-leave-active.titaui-pagination__select-dropdown-placement-topLeft {
  animation-name: rcSelectDropdownSlideDownOut;
  animation-play-state: running;
}

//========paagination select reset css=========
.titaui-pagination__select {
  width: 100%;
  cursor: pointer;

  &-selector {
    display: inline-block;
    position: relative;
    border: 0 !important;
    height: 22px;
    padding: 0 14px 0 0 !important;
    overflow: hidden;
    text-overflow: ellipsis;
    overflow: hidden;

    &:hover {
      .titaui-pagination__select-selection-item {
        color: #2879ff;
      }
    }

    &:before {
      position: absolute;
      display: inline-block;
      content: '';
      width: 8px;
      height: 8px;
      right: 0;
      top: calc(50% - 4px);
      background: url(./images/arrow.svg) transparent no-repeat 50% 50%;
      cursor: pointer;
    }
  }

  &-selection-item,
  &-selection-placeholder {
    position: unset !important;
    top: unset !important;
    left: unset !important;
    height: 22px;
    font-size: 16px;
    font-weight: 600;
    line-height: 23px;
    color: #3f4755;
  }

  &-selection-search {
    display: none;
    position: absolute;
  }

  &-arrow {
    position: unset;
    line-height: 22px;
    vertical-align: text-bottom;

    &-icon::after {
      transform: unset !important;
    }
  }

  &-dropdown {
    min-width: unset !important;
    width: unset !important;
    padding: 16px 0;
    background: #ffffff;
    box-shadow: 0px 4px 12px 0px rgba(127, 145, 180, 0.2);
    border: 1px solid #f0f2f5 !important;

    .titaui-pagination__select {
      &-item-option {
        padding: 0 24px;
        height: 36px;
        line-height: 36px;

        &-active {
          background: #f7f8fa;
          cursor: pointer;
        }

        &-content {
          font-size: 14px;
          font-weight: 400;
        }

        &-selected {
          .titaui-pagination__select-item-option-content {
            color: #2879ff;
          }
        }

        &-state {
          display: none;
        }
      }
    }
  }
}
//========paagination select reset css=========

@keyframes rcSelectDropdownSlideUpIn {
  0% {
    opacity: 0;
    transform-origin: 0% 0%;
    transform: scaleY(0);
  }
  100% {
    opacity: 1;
    transform-origin: 0% 0%;
    transform: scaleY(1);
  }
}
@keyframes rcSelectDropdownSlideUpOut {
  0% {
    opacity: 1;
    transform-origin: 0% 0%;
    transform: scaleY(1);
  }
  100% {
    opacity: 0;
    transform-origin: 0% 0%;
    transform: scaleY(0);
  }
}
@keyframes rcSelectLoadingIcon {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
