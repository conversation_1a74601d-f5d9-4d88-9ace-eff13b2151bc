import React, { <PERSON> } from 'react'
import Pagination from '@/stories/base/Pagination'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  return (
    <Pagination
      total={50}
      onChange={() => {}}
      onShowSizeChange={(current: number, size: number) => {
        // to(current);
        // setPageSize(size);
      }}
      current={1}
      pageSize={10}
    />
  )
}

export default React.memo(Base)
