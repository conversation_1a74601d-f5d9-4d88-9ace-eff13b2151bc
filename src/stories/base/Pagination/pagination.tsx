import React, { useState, useEffect, useCallback } from 'react'
import classnames from 'classnames'
import Pagination, { PaginationProps } from '@titaui/rc-pagination2'
import Select from 'rc-select'
import '@titaui/rc-pagination2/assets/index.css'
import './index.scss'
import './select.scss'
import { getBSGlobal, isEn } from '@tita/utils'
import zh_CN from './locale/zh_CN'
import en_US from './locale/en_US'

interface Props extends PaginationProps {
  size?: 'md' | 'sm';
  cacheKey?: string;
  isCache?: boolean;
}

export default function (props: Props) {
  const {
    className,
    size = 'md',
    selectProps,
    showLessItems = false,
    pageSize,
    isCache = false,
    cacheKey,
    onShowSizeChange,
    total,
    ...restProps
  } = props
  const userId = getBSGlobal('tenantInfo')?.Id
  const hash = window.location?.hash
  const [loaclCacheKey] = useState(isCache ? (cacheKey?.trim() || hash + '-' + userId) : '')

  const itemRender = (current: number, type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next', element: React.ReactNode) => {
    if (type === 'page') {
      return <a className="titaui-pagination-item_text">{current}</a>
    }
    if (type === 'prev') {
      return <a className="titaui-pagination-item_text tu-icon tu-icon-left" />
    }
    if (type === 'next') {
      return (
        <a className="titaui-pagination-item_text tu-icon tu-icon-APP-xi" />
      )
    }
    if (type === 'jump-prev') {
      return (
        <a className="titaui-pagination-jump-icon">
          <span className="tu-icons tu-icon-double-left" />
          <span className="tu-icon-more1" />
        </a>
      )
    }
    if (type === 'jump-next') {
      return (
        <a className="titaui-pagination-jump-icon">
          <span className="tu-icons tu-icon-double-right" />
          <span className="tu-icon-more1" />
        </a>
      )
    }
    return element
  }

  const classNames = classnames(className, {
    [`titaui-pagination__size--${size}`]: size,
  })

  const [selfPageSize, setSelfPageSize] = useState<number>(pageSize || 20)
  useEffect(() => {
    pageSize && setSelfPageSize(pageSize)
  }, [pageSize])

  useEffect(() => {
    if (!loaclCacheKey) return
    const cacheValue = localStorage.getItem(loaclCacheKey)
    if (cacheValue && JSON.parse(cacheValue) !== selfPageSize) {
      JSON.parse(cacheValue) !== selfPageSize &&
        onShowSizeChange?.(1, JSON.parse(cacheValue))
      return setSelfPageSize(JSON.parse(cacheValue))
    }
  }, [selfPageSize, loaclCacheKey, onShowSizeChange])

  const onShowSizeChangeHandler = useCallback(
    (current: number, size: number) => {
      loaclCacheKey && localStorage.setItem(loaclCacheKey, size?.toString())
      onShowSizeChange?.(current, size)
    },
    [onShowSizeChange, loaclCacheKey],
  )
  if(!(total && (total > 0))) return false;

  return (
    <Pagination
      className={classNames}
      itemRender={itemRender}
      prefixCls="titaui-pagination"
      selectComponentClass={Select}
      selectPrefixCls="titaui-pagination__select"
      showLessItems={showLessItems}
      showQuickJumper
      showSizeChanger
      showTitle={false}
      locale={isEn ? en_US : zh_CN}
      selectProps={{ dropdownAlign: { offset: [-25, 0] }, ...selectProps }}
      pageSize={selfPageSize}
      onShowSizeChange={onShowSizeChangeHandler}
      showTotal={() => isEn ? `${total} total` : `共 ${total} 条`}
      total={total}
      {...restProps}
    />
  )
}
