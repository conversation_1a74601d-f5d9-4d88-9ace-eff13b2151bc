.titaui-pagination {
  width: 100%;
  display: inline-flex;
  justify-content: flex-end;
  align-items: center;
  &-total-text {
    margin-right: 12px;
    color: #3F4755;
  }
  &-prev,
  &-next,
  &-jump-prev,
  &-jump-next,
  &-item {
    transition: all 0.3s ease;
    box-sizing: border-box;
    color: #3f4755;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    outline: none !important;

    &_text {
      font-size: 14px;
      font-weight: 400;
      line-height: 28px;
      transition: all 0.3s ease;
      color: #3f4755;

      &.tu-icon {
        font-size: 12px;
      }
    }
  }

  &-jump,
  &-jump {
    &-prev,
    &-next {
      border: none !important;

      &:hover {
        .titaui-pagination-jump-icon {
          .tu-icons {
            opacity: 1;
            visibility: visible;
          }
          .tu-icon-more1 {
            opacity: 0;
            visibility: hidden;
          }
        }
      }
    }

    &-icon {
      width: 100%;
      height: 100%;
      font-size: 12px;
      color: #3f4755;
      display: inline-block;
      position: relative;

      .tu-icons,
      .tu-icon-more1 {
        transition: all 0.3s ease;
        position: absolute;
        top: 50%;
        left: 50%;
      }

      .tu-icons {
        color: #2879ff;
        opacity: 0;
        transform: translate(-50%, -50%);
        visibility: hidden;
      }

      .tu-icon-more1 {
        font-size: 16px;
        transform: translate(-50%, -50%) rotate(90deg);
        display: inline-block;
      }
    }
  }

  &-options {
    display: inline-flex;
    justify-content: flex-end;
    align-items: center;

    &-quick-jumper {
      margin-left: 4px;
      display: inline-flex;
      align-items: center;
      white-space: nowrap;

      input {
        width: 48px;
        margin: 0 8px;
        padding: 0 4px;
        border: 1px solid #e9ecf0;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-sizing: border-box;

        &:not(:disabled) {
          &:hover,
          &:focus {
            box-shadow: 0px 0px 4px 0px rgba(40, 121, 255, 0.3);
            border-color: #2879ff;
          }
        }
      }
    }
  }

  // ============== Size ===============
  &__size {
    &--md {
      .titaui-pagination {
        &-prev,
        &-next,
        &-jump-prev,
        &-jump-next,
        &-item {
          min-width: 28px;
          height: 28px;
          margin: 0 4px;
          padding: 0 6px;
          border: 1px solid #e9ecf0;
          border-radius: 8px;
          background: #ffffff;

          &-active,
          &:hover {
            border-color: #2879ff;

            .icon,
            .titaui-pagination-item_text {
              color: #2879ff;
            }
          }
        }

        &-options {
          &-quick-jumper {
            input {
              height: 28px;
            }
          }
        }

        &-jump-prev,
        &-jump-next {
          width: 24px;
        }

        &__select {
          margin-left: 4px;

          &-selector {
            border: 1px solid #e9ecf0 !important;
            border-radius: 8px;
            background: #ffffff;
            transition: all 0.3s ease;

            &:hover {
              border-color: #2879ff !important;
            }
          }
        }
      }
    }

    &--sm {
      .titaui-pagination {
        &-prev,
        &-next,
        &-jump-prev,
        &-jump-next,
        &-item {
          min-width: 24px;
          height: 24px;
          margin: 0 2px;
          padding: 0 4px;
          border: none;
          border-radius: 0;
          background: transparent;

          &-active,
          &:hover {
            .icon,
            .titaui-pagination-item_text {
              color: #2879ff;
            }
          }
        }
        &-jump-next {
          width: 30px !important;
        }

        &-options {
          &-quick-jumper {
            input {
              height: 24px;
            }
          }
        }

        &-jump-prev,
        &-jump-next {
          width: 24px;
        }

        &__select {
          margin-left: 2px;
        }
      }
    }
  }

  // ============== Disabled ===============
  &-disabled {
    background: #f7f8fa !important;
    border-color: #e9ecf0;
    color: #bfc7d5;
    cursor: not-allowed;

    &:hover {
      border-color: #e9ecf0 !important;

      .titaui-pagination-item_text {
        color: #bfc7d5 !important;
      }
    }

    .titaui-pagination-item_text {
      color: #bfc7d5;
      cursor: not-allowed;
    }
  }

  // ============== Reset ===============
  &__select {
    // width: 98px !important;

    &-selector {
      // width: 98px;
      height: 28px !important;
      margin: 0;
      box-sizing: border-box;
      display: flex !important;
      align-items: center;
      justify-content: space-between;

      &::before {
        display: none !important;
      }

      &::after {
        content: '\ea64';
        font-family: 'tu-icon' !important;
        font-size: 12px;
        color: #3f4755;
        speak: never;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
        position: relative;
        left: 6px;
      }
    }

    &-arrow {
      display: none;
    }

    &-selection-item {
      margin-left: 8px;
      font-size: 14px !important;
      font-weight: normal !important;
    }

    &-dropdown {
      // width: 98px !important;
      border: none !important;
      border-radius: 8px;
      z-index: 2;
    }

    &-item-option {
      padding: 0 20px;
      white-space: nowrap;
    }
  }
}
