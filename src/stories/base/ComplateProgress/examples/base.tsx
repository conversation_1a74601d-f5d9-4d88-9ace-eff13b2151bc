import React, { FC } from 'react'
import ComplateProgress from '@/stories/base/ComplateProgress'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  return (
    <>
      <div style={{ width: 100 }}>
        <ComplateProgress status={0} milepost={false} />
        <ComplateProgress status={1} milepost={false} percent={50} />
        <ComplateProgress status={2} milepost={false} />
        <ComplateProgress status={3} milepost={false} percent={40} />
        <ComplateProgress status={4} milepost={false} />
        <ComplateProgress status={5} milepost={false} />
        <ComplateProgress status={6} milepost={false} />
        <ComplateProgress status={0} milepost={true} />
        <ComplateProgress status={1} milepost={true} />
        <ComplateProgress status={2} milepost={true} />
        <ComplateProgress status={3} milepost={true} />
        <ComplateProgress status={4} milepost={true} />
        <ComplateProgress status={5} milepost={true} />
        <ComplateProgress status={6} milepost={true} />
      </div>
    </>
  )
}

export default React.memo(Base)
