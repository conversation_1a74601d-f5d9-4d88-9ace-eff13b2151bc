import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'

enum ComplateStatus {
  NotStarted, // 未开始
  InProgress, // 进行中
  Complated, // 已完成
  Deferred, // 已延期
  Paused, // 暂停中
  Unaccepted, // 未接受
  Cancelled, // 已取消
}

export interface IComplateProgressProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  status: ComplateStatus
  percent?: number
  milepost: boolean
}

const preCls = 'tita-ui--complate-progress'

export const ComplateProgress: FC<IComplateProgressProps> = React.memo(
  ({ className, style, status, percent = 0, milepost }) => {
    /**
     * 渲染每个状态对应的内容
     *
     */
    const renderStatusContent = () => {
      switch (status) {
        case 0:
          return <div className='not-start'>-</div>
        case 1:
          return (
            <div className='in-progress'>
              {`${percent}/%`}
              <div
                className='percent'
                style={{
                  width: `${percent > 100 ? 100 : percent}%`,
                  backgroundColor: milepost
                    ? 'rgba(255,205,65,0.36)'
                    : 'rgba(40,121,255,0.3)',
                }}
              ></div>
            </div>
          )
        case 2:
          return (
            <div className='complated'>
              100/%
              <div
                className='percent'
                style={{
                  width: '100%',
                  backgroundColor: milepost
                    ? 'rgba(240,94,94,0.3)'
                    : 'rgba(0,214,132,0.3)',
                }}
              ></div>
            </div>
          )
        case 3:
          return (
            <div className='deferred'>
              {`${percent}/%`}
              <div
                className='percent'
                style={{
                  width: `${percent > 100 ? 100 : percent}%`,
                  backgroundColor: 'rgba(240,94,94,0.3)',
                }}
              ></div>
            </div>
          )
        case 4:
          return (
            <div className='paused'>
              {`${percent}/%`}
              <div
                className='percent'
                style={{
                  width: `${percent > 100 ? 100 : percent}%`,
                  backgroundColor: 'rgba(240,94,94,0.3)',
                }}
              ></div>
            </div>
          )
        case 5:
          return <div className='unaccepted'>-</div>
        case 6:
          return <div className='cancled'>-</div>
      }
    }

    return (
      <div className={classNames(preCls, className)} style={style}>
        {renderStatusContent()}
      </div>
    )
  }
)

export default ComplateProgress
