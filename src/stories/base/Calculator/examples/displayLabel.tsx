import React, { FC } from 'react'
import Calculator from '@/stories/base/Calculator'
import './index.scss'

export interface IDemoProps {}

const DisplayLabelDemo: FC<IDemoProps> = ({ }) => {
  return (
    <section className='tita-ui--calculator-demo-readonly'>
      <Calculator displayerLabel='得分 =' placeholder='示例："完成值" / "目标值" * 100' />
      <Calculator.DeveloperModel displayerLabel='得分 =' placeholder='示例："完成值" / "目标值" * 100' />
    </section>
  )
}

export default React.memo(DisplayLabelDemo)
