import React, { Ref, forwardRef, useImperativeHandle, useState } from 'react'
import classNames from 'classnames'
import cloneDeep from 'lodash/cloneDeep'
import Keyboard, { IKeyboardKeyObj } from '@/stories/base/Keyboard'
import TitaScroll from '@/stories/base/TitaScroll'
import { ICalculatorProps, ICalculatorRef } from '../../index'
import './index.scss'

export type IStandardCalculatorProps = ICalculatorProps & {}

export interface IStandardCalculatorRef extends ICalculatorRef {}

const preCls = 'tita-ui--calculator-standard-mode'

export const StandardCalculator = React.memo(
  forwardRef(
    (
      {
        className,
        displayerLabel,
        initialValue = [],
        onChange,
        onKeyboardSelect,
        placeholder,
        readOnly = false,
        keyboardOptions,
        scrollOptions,
        height = 410,
        width = 300,
        ...restProps
      }: IStandardCalculatorProps,
      ref: Ref<IStandardCalculatorRef>
    ) => {
      const [computationElements, setComputationElements] =
        // @ts-ignore
        useState<IKeyboardKeyObj[]>(initialValue)

      const handleKeyboardClick = (selectedKey: IKeyboardKeyObj) => {
        let nextData = cloneDeep(computationElements)
        if (onKeyboardSelect) return onKeyboardSelect(nextData, selectedKey)
        const { type, value } = selectedKey
        if (type !== 'Function') {
          nextData.push(selectedKey)
        } else {
          switch (value) {
            case 'delete':
              nextData.pop()
              break
            case 'clear':
              nextData = []
              break
            case 'confirm':
              onChange?.(nextData)
              return
            default:
              break
          }
        }
        setComputationElements(nextData)
      }

      useImperativeHandle(ref, () => ({
        insert: (data) =>
          setComputationElements((prevData) => prevData.concat(data)),
      }))

      const displayerContent = computationElements.map((element) => element.value).join('')
      return (
        <section
          className={classNames(preCls, className)}
          style={{
            height,
            width,
          }}
        >
          <div className={`${preCls}__displayer`}>
            {displayerLabel && <label>{displayerLabel}</label>}
            <div className='displayer-content-container'>
              <TitaScroll
                height='100%'
                innerStyle={{
                  maxWidth: '100%',
                }}
                {...{ ...restProps, ...(scrollOptions as any) }}
              >
                <p className={`${preCls}__displayer-content`}>
                  {displayerContent || <span className="placeholder">{placeholder}</span>}
                </p>
              </TitaScroll>
            </div>
          </div>
          <div className={`${preCls}__keyboard`}>
            <Keyboard
              {...{ ...restProps, disabled: readOnly, ...keyboardOptions }}
              onSelect={handleKeyboardClick}
            />
          </div>
        </section>
      )
    }
  )
)

export default StandardCalculator
