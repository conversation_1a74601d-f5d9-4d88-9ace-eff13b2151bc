import React, { Ref, forwardRef, useImperativeHandle, useRef } from 'react'
import classNames from 'classnames'
import JsCodeEditor, { IJsCodeEditorRef } from '@/stories/base/JsCodeEditor'
import Keyboard, { IKeyboardProps } from '@/stories/base/Keyboard'
import { IKeyboardKeyObj } from '@/stories/base/Keyboard/interface'
import TitaScroll from '@/stories/base/TitaScroll'
import { ICalculatorProps, ICalculatorRef } from '../../index'
import { KeyboardColumnLayout, KeyboardKeys } from './keyboard-datas'
import './index.scss'

export type IDeveloperModelCalculatorProps = ICalculatorProps & Partial<IKeyboardProps> & {}

export interface IDeveloperModelCalculatorRef extends ICalculatorRef {}

const preCls = 'tita-ui--calculator-developer-mode'

export const DeveloperModelCalculator = React.memo(
  forwardRef(
    (
      {
        className,
        initialValue = [],
        onKeyboardSelect,
        readOnly = false,
        height = 418,
        width = 510,
        jsCodeEditorOptions,
        keyboardOptions,
        scrollOptions,
        ...restProps
      }: IDeveloperModelCalculatorProps,
      ref: Ref<IDeveloperModelCalculatorRef>
    ) => {
      const jsCodeEditorRef = useRef<IJsCodeEditorRef>({} as IJsCodeEditorRef)

      const handleKeyboardClick = (selectedKey: IKeyboardKeyObj) => {
        // @ts-ignore
        if (onKeyboardSelect) return onKeyboardSelect(initialValue, selectedKey)
        const { type, value } = selectedKey
        if (type !== 'Function') {
          jsCodeEditorRef.current?.insert(value)
        } else {
          switch (value) {
            case 'delete':
              jsCodeEditorRef.current?.remove()
              break
            case 'clear':
              jsCodeEditorRef.current?.clear()
              break
            default:
              break
          }
        }
      }

      const handleInsertContent = (data: IKeyboardKeyObj[]) => {
        const content = data.map((item) => item.value).join('')
        jsCodeEditorRef.current?.insert(content)
      }

      useImperativeHandle(ref, () => ({
        insert: handleInsertContent,
        jsCodeEditorInstanceMethod: { ...jsCodeEditorRef.current },
      }))

      const defaultValue =
        typeof initialValue === 'string'
          ? initialValue
          : initialValue
              .map(
                (element) =>
                  `${element.value}${
                    ['Function', 'Keyword'].includes(element.type) ? ' ' : ''
                  }`
              )
              .join('')
      return (
        <div
          className={classNames(preCls, className)}
          style={{
            height,
            width,
          }}
        >
          <div className={`${preCls}__displayer`}>
            <TitaScroll
              height='100%'
              {...{ ...restProps, ...(scrollOptions as any) }}
            >
              <JsCodeEditor
                defaultValue={defaultValue}
                minLines={7}
                maxLines={9999}
                height='auto'
                width="calc(100% - 24px)"
                {...{
                  ...restProps,
                  readOnly,
                  ...(jsCodeEditorOptions as any),
                }}
                ref={jsCodeEditorRef}
              />
            </TitaScroll>
          </div>
          <div className={`${preCls}__keyboard`}>
            <Keyboard
              keys={KeyboardKeys}
              layout={KeyboardColumnLayout}
              {...{ ...restProps, disabled: readOnly, ...keyboardOptions }}
              onSelect={handleKeyboardClick}
            />
          </div>
        </div>
      )
    }
  )
)

export default DeveloperModelCalculator
