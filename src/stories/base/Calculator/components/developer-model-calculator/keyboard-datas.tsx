import { IKeyboardColumn } from '@/stories/base/Keyboard'

const KeyboardKeys: IKeyboardColumn[] = [
  [
    {
      key: 'if',
      label: 'if',
      value: 'if () {}',
      type: 'Keyword',
    },
    {
      key: 'else',
      label: 'else',
      value: 'else {}',
      type: 'Punctuator',
    },
    {
      key: 'and',
      label: 'and',
      value: '&&',
      type: 'Punctuator',
    },
    {
      key: 'or',
      label: 'or',
      value: '||',
      type: 'Punctuator',
    },
    {
      key: '<=',
      label: '<=',
      value: '<=',
      type: 'Punctuator',
    },
    {
      key: '>=',
      label: '>=',
      value: '>=',
      type: 'Punctuator',
    },
    {
      key: '<',
      label: '<',
      value: '<',
      type: 'Punctuator',
    },
    {
      key: '>',
      label: '>',
      value: '>',
      type: 'Punctuator',
    },
  ],
  [
    {
      key: '1',
      label: '1',
      value: '1',
      type: 'Numeric',
    },
    {
      key: '2',
      label: '2',
      value: '2',
      type: 'Numeric',
    },
    {
      key: '3',
      label: '3',
      value: '3',
      type: 'Numeric',
    },
    {
      key: '4',
      label: '4',
      value: '4',
      type: 'Numeric',
    },
    {
      key: '5',
      label: '5',
      value: '5',
      type: 'Numeric',
    },
    {
      key: '6',
      label: '6',
      value: '6',
      type: 'Numeric',
    },
    {
      key: '7',
      label: '7',
      value: '7',
      type: 'Numeric',
    },
    {
      key: '8',
      label: '8',
      value: '8',
      type: 'Numeric',
    },
    {
      key: '9',
      label: '9',
      value: '9',
      type: 'Numeric',
    },
    {
      key: '0',
      label: '0',
      value: '0',
      type: 'Numeric',
    },
    {
      key: '.',
      label: '.',
      value: '.',
      type: 'Punctuator',
    },
    {
      key: '()',
      label: '()',
      value: 'parenthesis',
      type: 'Punctuator',
      children: [
        {
          key: '(',
          label: '(',
          value: '(',
          type: 'Punctuator',
        },
        {
          key: ')',
          label: ')',
          value: ')',
          type: 'Punctuator',
        },
      ],
    },
  ],
  [
    {
      key: '+',
      label: '+',
      value: '+',
      type: 'Punctuator',
    },
    {
      key: '-',
      label: '-',
      value: '-',
      type: 'Punctuator',
    },
    {
      key: '*',
      label: '*',
      value: '*',
      type: 'Punctuator',
    },
    {
      key: '/',
      label: '/',
      value: '/',
      type: 'Punctuator',
    },
  ],
  [
    {
      key: 'delete',
      label: 'delete',
      value: 'delete',
      type: 'Function',
      containerProps: {
        orange: true,
      },
      render: () => (
        <span className='tu-icon-cleanup-s' style={{ fontSize: 24 }} />
      ),
    },
    {
      key: 'clear',
      label: '清空',
      value: 'clear',
      type: 'Function',
      containerProps: {
        orange: true,
      },
    },
    {
      key: '=',
      label: '=',
      value: '=',
      type: 'Punctuator',
      rows: 2,
    },
  ],
]

const KeyboardColumnLayout = [
  {
    columns: 2,
    rows: 4,
  },
  {
    columns: 3,
    rows: 4,
  },
  {
    columns: 1,
    rows: 4,
  },
  {
    columns: 1,
    rows: 4,
  },
]

export { KeyboardKeys, KeyboardColumnLayout }
