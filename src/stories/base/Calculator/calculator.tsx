import StandardCalculator, {
  IStandardCalculatorProps,
  IStandardCalculatorRef,
} from './components/standard-calculator'
import DeveloperModelCalculator from './components/developer-model-calculator'
import { IJsCodeEditorProps, IJsCodeEditorRef } from '../JsCodeEditor'
import { IKeyboardKeyObj, IKeyboardProps } from '../Keyboard'
import { ITitaScrollProps } from '../TitaScroll'

export interface ICalculatorProps {
  /** 自定义类名 */
  className?: string
  /** 显示器标签，开发者模式不支持设置标签 */
  displayerLabel?: string
  /** 只读模式
   * @default false
   */
  readOnly?: boolean
  /** JS 代码编辑器配置项 */
  jsCodeEditorOptions?: IJsCodeEditorProps
  /** 键盘配置项 */
  keyboardOptions?: IKeyboardProps
  /** 占位符 */
  placeholder?: string;
  /** 滚动条配置项 */
  scrollOptions?: ITitaScrollProps
  /** 计算器默认高度
   * @default 410
   */
  height?: number | string
  /** 计算器默认宽度
   * @default 348
   */
  initialValue?: string | IKeyboardKeyObj[]
  width?: number | string
  /** 点击「确认」按钮触发事件 */
  onChange?: (computationElements: IKeyboardKeyObj[]) => void
  /** 自定义键盘选择事件 */
  onKeyboardSelect?: (
    computationElements: IKeyboardKeyObj[],
    element: IKeyboardKeyObj
  ) => void
}

export type ICalculatorRef = {
  insert: (data: IKeyboardKeyObj[]) => void
  /** 仅开发者模式下使用 */
  jsCodeEditorInstanceMethod?: IJsCodeEditorRef
}

export type ICalculator = React.MemoExoticComponent<
  React.ForwardRefExoticComponent<
    IStandardCalculatorProps & React.RefAttributes<IStandardCalculatorRef>
  >
> & {
  DeveloperModel: typeof DeveloperModelCalculator
}

export const Calculator: ICalculator = StandardCalculator as ICalculator

Calculator.DeveloperModel = DeveloperModelCalculator

export default Calculator
