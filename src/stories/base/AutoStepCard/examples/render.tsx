import React, { <PERSON> } from 'react'
import AutoStepCard from '@/stories/base/AutoStepCard'
import classNames from 'classnames'

export interface IRenderProps {}

const Render: FC<IRenderProps> = ({}) => {
  return (
    <AutoStepCard
      datas={[
        {
          label: '步骤一',
          value: '1',
        },
        {
          label: '步骤二',
          value: '2',
        },
        {
          label: '步骤三',
          value: '3',
        },
      ]}
      render={({ data, active, index }) => (
        <div
          className={classNames('px-10px', {
            'text-primary': active,
          })}
        >
          {index} {data.label}
        </div>
      )}
    />
  )
}

export default React.memo(Render)
