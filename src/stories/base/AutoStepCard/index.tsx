import React, { FC, Key, useMemo, useRef, useState } from 'react'
import classNames from 'classnames'
import './index.scss'
import StepCard from './components/Card'
import { useSize, useUpdateEffect } from 'ahooks'

export interface AutoStepCardDataItem {
  value: Key
  label: React.ReactNode
  /** 自定义节点背景色 */
  background?: string
  hidden?: boolean
  [key: string]: any
}

export interface IAutoStepCardProps {
  datas: AutoStepCardDataItem[]
  value?: Key
  onChange?: (value: Key, data: AutoStepCardDataItem) => void
  render?: (info: {
    active: boolean
    /** 是否是当前激活节点前面的节点 */
    activeBefore: boolean
    value?: Key
    index: number
    data: AutoStepCardDataItem
  }) => React.ReactNode
  /**
   * 节点偏移量，默认 -3
   */
  offsetX?: number
  disabled?: boolean
  activeBackground?: string
  defaultBackground?: string
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-auto-step-card'

export const AutoStepCard: FC<IAutoStepCardProps> = React.memo(
  ({
    value: propsValue,
    offsetX = -3,
    datas,
    onChange,
    disabled,
    render,
    activeBackground = '#e9f1ff',
    defaultBackground = '#f0f4fa',
    className,
    style,
  }) => {
    const [value, setValue] = useState<Key | undefined>(propsValue)
    useUpdateEffect(() => {
      setValue(propsValue)
    }, [propsValue])

    const activeIndex = useMemo(() => {
      return datas?.findIndex((item) => item.value === value)
    }, [value, datas])

    const containerRef = useRef<HTMLDivElement>(null)
    const { height } = useSize(containerRef) || { height: 0 }

    return (
      <div
        className={classNames(preCls, className)}
        style={style}
        ref={containerRef}
      >
        {datas
          ?.filter((item) => item.hidden !== true)
          .map((data, index) => {
            const active = value === data.value
            let type: 'center' | 'left' | 'right' = 'center'
            if (index === 0) type = 'left'
            if (index === datas.length - 1) type = 'right'

            return (
              <StepCard
                type={type}
                height={height}
                onClick={() => {
                  if (disabled) return
                  onChange?.(data.value, data)
                  setValue(data.value)
                }}
                className={classNames({
                  'cursor-pointer': !disabled,
                })}
                backgroundColor={active ? activeBackground : defaultBackground}
                style={{
                  height: '100%',
                  marginLeft: !!index ? `${offsetX}px` : undefined,
                }}
              >
                {render?.({
                  active,
                  activeBefore: activeIndex > index,
                  value,
                  index,
                  data,
                }) || <div className='px-16px'>{data.label}</div>}
              </StepCard>
            )
          })}
      </div>
    )
  }
)

export default AutoStepCard
