import classNames from 'classnames'
import React, { FC } from 'react'
import styled from 'styled-components'

export interface IStepCardProps
  extends React.DetailedHTMLProps<
    React.HTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  type: 'left' | 'right' | 'center'
  height: number
  backgroundColor?: string
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

export const StepCard: FC<IStepCardProps> = React.memo(
  ({
    type,
    backgroundColor = '#F0F4FA',
    className,
    height,
    style,
    children,
    ref,
    ...other
  }) => {
    return (
      <StepCardStyle
        className={classNames('tita-ui-step-card', className)}
        style={style}
        {...other}
      >
        <StepCardBGStyle>
          {type !== 'left' && (
            <div
              className='step-bg-left'
              style={{
                // 避免在 Safari 浏览器中出现错位，不知道什么原理
                transform: `translateY(0px)`,
              }}
            >
              <svg
                width='auto'
                height={height || '100%'}
                viewBox='0 0 15 30'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                xmlnsXlink='http://www.w3.org/1999/xlink'
              >
                <g
                  id='考核详情'
                  stroke='none'
                  stroke-width='1'
                  fill='none'
                  fill-rule='evenodd'
                >
                  <g
                    transform='translate(-81.000000, -164.000000)'
                    fill={backgroundColor}
                  >
                    <g
                      id='头部-1'
                      transform='translate(-375.000000, 88.000000)'
                    >
                      <g id='编组-6' transform='translate(0.000000, 76.000000)'>
                        <g
                          id='编组-2'
                          transform='translate(455.000000, 0.000000)'
                        >
                          <path
                            d='M3.53238076,0 L16,0 L16,0 L16,15 L16,30 L3.53238076,30 C2.42781126,30 1.53238076,29.1045695 1.53238076,28 C1.53238076,27.6375121 1.63089642,27.2818393 1.81739491,26.9710085 L7.76521019,17.057983 C8.52524792,15.7912535 8.52524792,14.2087465 7.76521019,12.942017 L1.81739491,3.02899151 C1.24909859,2.08183098 1.55622872,0.853310468 2.50338925,0.285014149 C2.81422005,0.0985156667 3.16989287,5.10677155e-16 3.53238076,0 Z'
                            id='输入'
                          ></path>
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </div>
          )}
          <div
            className={classNames('step-bg-center', {
              [`step-bg-center--${type}`]: type,
            })}
            style={{ backgroundColor }}
          ></div>
          {type !== 'right' && (
            <div
              className='step-bg-right'
              style={{
                // 避免在 Safari 浏览器中出现错位，不知道什么原理
                transform: `translateY(0px)`,
              }}
            >
              <svg
                width='auto'
                height={height || '100%'}
                viewBox='0 0 12 30'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                xmlnsXlink='http://www.w3.org/1999/xlink'
              >
                <g
                  id='考核详情'
                  stroke='none'
                  stroke-width='1'
                  fill='none'
                  fill-rule='evenodd'
                >
                  <g
                    transform='translate(-196.000000, -164.000000)'
                    fill={backgroundColor}
                  >
                    <g
                      id='头部-1'
                      transform='translate(-375.000000, 88.000000)'
                    >
                      <g id='编组-6' transform='translate(0.000000, 76.000000)'>
                        <g
                          id='编组'
                          transform='translate(455.000000, 0.000000)'
                        >
                          <g
                            id='编组-8'
                            transform='translate(116.000000, 0.000000)'
                          >
                            <path
                              d='M0,0 C1.77442666,-3.25956889e-16 3.42065639,0.924514396 4.34417951,2.43966951 L10.7310621,12.9181487 C11.5103456,14.1966608 11.5103456,15.8033392 10.7310621,17.0818513 L4.34417951,27.5603305 C3.42065639,29.0754856 1.77442666,30 0,30 L0,30 L0,30 L0,15 L0,0 Z'
                              id='输入'
                            ></path>
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </div>
          )}
        </StepCardBGStyle>
        <div className='relative z-10'>{children}</div>
      </StepCardStyle>
    )
  }
)

const StepCardStyle = styled.div`
  position: relative;
  display: inline-flex;
  width: auto;
`

const StepCardBGStyle = styled.div`
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  width: 100%;
  height: 100%;
  z-index: 0;

  .step-bg-left,
  .step-bg-right {
    flex-shrink: 0;

    svg {
      max-height: 100%;
    }
  }
  .step-bg-left {
    display: flex;
    justify-content: end;
  }
  .step-bg-right {
    display: flex;
    justify-content: start;
  }

  .step-bg-center {
    width: 100%;
    overflow: hidden;

    &--left {
      border-radius: 8px 0 0 8px;
    }

    &--right {
      border-radius: 0 8px 8px 0;
    }
  }
`

export default StepCard
