import React, { FC, useEffect, useState } from 'react'
import Loading, { LazyLoading } from '@/stories/base/Loading'
import Panel from '../../Panel'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  const [loading, setLoading] = useState(true)
  useEffect(() => {
    // 模拟异步请求
    const timer = setInterval(() => {
      setLoading((value) => !value)
    }, 2000)
    return () => {
      clearInterval(timer)
    }
  }, [])

  return (
    <div className='space-y-10px'>
      <Panel title='默认：白色背景' height='200px'>
        <Loading loading={loading} />
      </Panel>
      <Panel title='灰色背景' height='200px'>
        <Loading type='list-grey' loading={loading} />
      </Panel>
      <Panel title='弹窗中使用的' height='200px'>
        <Loading type='modal' loading={loading} />
      </Panel>
      <Panel title='移动端' height='200px'>
        <Loading type='mobile' loading={loading} />
      </Panel>

      <LazyLoading className='h-[200px]' loading={loading}>
        balabala....
      </LazyLoading>

      <LazyLoading className='h-[200px]' type='mobile' loading={loading}>
        balabala....
      </LazyLoading>
    </div>
  )
}

export default React.memo(Base)
