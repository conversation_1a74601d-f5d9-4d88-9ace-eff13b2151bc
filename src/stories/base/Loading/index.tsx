import React, { FC, useEffect, useState } from 'react'
import classNames from 'classnames'
import './index.scss'

export interface ILoadingProps {
  /**
   * loading 类型
   * @default list-white
   */
  type?: 'list-grey' | 'list-white' | 'modal' | 'mobile'
  imgWidth?: number | string
  /**
   * loading 状态
   * @default true
   */
  loading?: boolean
  /** 延迟显示，避免出现闪烁 */
  delay?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-loading'

export const Loading: FC<ILoadingProps> = React.memo(
  // @ts-ignore
  ({
    children,
    type = 'list-white',
    imgWidth,
    delay,
    className,
    style,
    loading,
  }) => {
    const [show, setShow] = useState(delay ? false : true)
    useEffect(() => {
      if (!delay) return
      setTimeout(() => {
        setShow(true)
      }, delay)
    }, [])

    if (loading) {
      if (!show) return <></>

      const url =
        type !== 'mobile'
          ? `https://xfile6.tita.com/ux/tita-home-page/release/dist/loading-${type}.gif`
          : 'https://xfile6.tita.com/ux/tita-home-page/public/tita-ui-loading-mobile.gif'

      return (
        <div
          className={classNames(preCls, className, {
            [`${preCls}--type-${type}`]: type,
          })}
          style={style}
        >
          <div className={`${preCls}__content`}>
            <img src={url} style={{ width: imgWidth }} alt='loading' />
          </div>
        </div>
      )
    } else return children || <></>
  }
)

export interface ILazyLoadingProps {
  /**
   * loading 类型
   * @default list-white
   */
  type?: 'list-grey' | 'list-white' | 'modal' | 'mobile'
  imgWidth?: number | string
  /**
   * loading 状态
   * @default true
   */
  loading?: boolean
  /** 延迟显示，避免出现闪烁 */
  delay?: number
  /**
   * 禁用初始化延迟
   */
  disableInitialLazy?: boolean
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preClsV2 = 'tita-ui-lazy-loading'

export const LazyLoading: FC<ILazyLoadingProps> = React.memo(
  ({
    children,
    type = 'list-white',
    imgWidth,
    delay = 300,
    disableInitialLazy,
    className,
    style,
    loading,
  }) => {
    const [open, setOpen] = useState(() => {
      if (disableInitialLazy && loading) return true;
      return false
    })
    const [show, setShow] = useState(() => {
      if (disableInitialLazy && loading) return true;
      return false
    })
    useEffect(() => {
      if (!loading) {
        setOpen(false)
        setShow(false)
        return
      }
      setOpen(true)
      const timer = setTimeout(() => {
        setShow(true)
      }, delay)

      return () => clearTimeout(timer)
    }, [loading])

    const url =
      type !== 'mobile'
        ? `https://xfile6.tita.com/ux/tita-home-page/release/dist/loading-${type}.gif`
        : 'https://xfile6.tita.com/ux/tita-home-page/public/tita-ui-loading-mobile.gif'

    return (
      <div
        className={classNames(preClsV2, className, {
          [`${preClsV2}--type-${type}`]: type,
        })}
        style={style}
      >
        <div className={classNames(`${preClsV2}__loading-mask`, {
          [`${preClsV2}__loading-mask--open`]: open,
          [`${preClsV2}__loading-mask--show`]: show
        })}>
          <img src={url} style={{ width: imgWidth }} alt='loading' />
        </div>
        {children}
      </div>
    )
  }
)

export default Loading
