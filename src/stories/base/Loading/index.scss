.tita-ui-loading {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;

  &--type-list-grey img {
    width: 100px;
  }
  &--type-list-white img {
    width: 100px;
  }
  &--type-modal img {
    width: 100px;
  }
  &--type-mobile {
    .tita-ui-loading__content {
      width: 52px;
      height: 52px;
      background: #FFFFFF;
      box-shadow: 0px 0px 16px 0px rgba(127, 145, 180, 0.1);
      border-radius: 8px;
    }
  }
  &--type-mobile img {
    width: 100%;
  }
}

.tita-ui-lazy-loading {
  position: relative;

  &--type-list-grey > &__loading-mask > img {
    width: 100px;
  }
  &--type-list-white > &__loading-mask > img {
    width: 100px;
  }
  &--type-modal > &__loading-mask > img {
    width: 100px;
  }
  &--type-mobile > &__loading-mask > img {
    width: 52px;
    height: 52px;
    background: #FFFFFF;
    box-shadow: 0px 0px 16px 0px rgba(127, 145, 180, 0.1);
    border-radius: 8px;
  }

  &__loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: all .3s;
    pointer-events: none;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    
    &--open {
      pointer-events: auto;
    }
    
    &--show {
      opacity: 1;
      background: rgba(255, 255, 255, 0.7);
    }
  }
}