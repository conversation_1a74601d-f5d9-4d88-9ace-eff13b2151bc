import React, { <PERSON> } from 'react'
import LineItem from '@/stories/base/LineItem'
import Button from '../../Button'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({ }) => {
  return (
    <div className='space-y-8px'>
      <LineItem icon='project-s' title='名称'>
        <Button>哈哈</Button>
      </LineItem>
      <LineItem icon='label-s' title='标签'>
        <Button primary>哈哈</Button>
      </LineItem>
      <LineItem icon='label-s' title='必填' require point='这是提示信息！'>
        <Button primary>哈哈</Button>
      </LineItem>
      <LineItem icon='describe-s' title='描述'>
        <Button type='border'>哈哈</Button>
      </LineItem>
      <LineItem title='纵向布局' icon='describe-s' point='这是提示信息！' direction='column'>
        <Button type='border'>内容在标题下面展示</Button>
      </LineItem>
    </div>
  )
}

export default React.memo(Base)
