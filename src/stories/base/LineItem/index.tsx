import classNames from 'classnames'
import React, { FC } from 'react'
import Tooltip from '../Tooltip/tooltip'
import './index.scss'

export interface ILineItemProps {
  title?: string | React.ReactNode
  icon?: string
  require?: boolean
  point?: string
  direction?: 'row' | 'column'
  titleMinWidth?: number | string
  titleColor?: string
  spaceX?: number
  children?: React.ReactNode
  className?: string
  contentClassName?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-line-item'

export const LineItem: FC<ILineItemProps> = React.memo(({ direction, icon, title, require, point, spaceX, titleMinWidth, titleColor, className, contentClassName, style, children }) => {
  
  return (
    <div className={classNames('flex', preCls, className, {
      [`${preCls}--require`]: require,
      [`${preCls}--direction-${direction}`]: direction,
    })} style={style}>
      <div className={`${preCls}__title`} style={{ marginRight: spaceX, minWidth: titleMinWidth, color: titleColor }}>
        <span className="flex items-center space-x-6px">
          {icon && <i className={`${preCls}__icon tu-icon-${icon}`} />}
          {title && <span className={`${preCls}__text relative`}>{title}</span>}
          {point && <Tooltip overlay={point} placement="top" action={['hover', 'click']}><i className='tu-icon-wiki cursor-pointer' /></Tooltip>}
        </span>
      </div>
      <div className={classNames(`${preCls}__content`, contentClassName)}>
        {children}
      </div>
    </div>
  )
})

export default LineItem
