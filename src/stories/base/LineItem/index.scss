.tita-ui-line-item {
  
  &__title {
    position: relative;
    font-size: 14px;
    font-weight: 400;
    color: #89919F;
    line-height: 32px;
    min-width: 78px;
    margin-right: 40px;
    flex-shrink: 0;
  }
  &__content {
    width: 100%;
    line-height: 32px;
    min-height: 32px;
    position: relative;
    display: flex;
    align-items: center;
  }

  &__icon {
    font-size: 16px;
  }

  &--require &__text::after {
    content: '*';
    position: absolute;
    top: 6px !important;
    right: -10px;
    font-size: 16px;
    font-weight: 600;
    color: #F05E5E;
    line-height: 22px;
  }

  &--direction-column {
    flex-direction: column;
  }
  &--direction-column &__title {
    margin-right: 0;
    font-size: 14px;
    font-weight: 500;
    color: #3F4755;
    line-height: 22px;
    margin-bottom: 8px;
  }

  .tu-icon-wiki {
    color: #bfc7d5
  }
}
