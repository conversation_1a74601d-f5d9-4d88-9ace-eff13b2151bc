import Alert from '@/stories/base/Alert'
import React, { FC } from 'react'

export interface ITextHighlightProps {}

const TextHighlight: FC<ITextHighlightProps> = ({}) => {
  return (
    <div className='space-y-8px'>
      <Alert size='least' type='success' message='提示信息' textHighlight />
      <Alert size='least' type='info' message='提示信息' textHighlight />
      <Alert size='least' type='warning' message='提示信息' textHighlight />
      <Alert size='least' type='error' message='提示信息' textHighlight />
    </div>
  )
}

export default React.memo(TextHighlight)
