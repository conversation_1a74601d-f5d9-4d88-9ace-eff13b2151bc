import Alert from '@/stories/base/Alert'
import React, { FC } from 'react'

export interface IDisableBackgroundProps {}

const DisableBackground: FC<IDisableBackgroundProps> = ({}) => {
  return (
    <div className='space-y-8px'>
      <Alert size='mini' type='success' message='提示信息' textHighlight disableBackground />
      <Alert size='mini' type='info' message='提示信息' textHighlight disableBackground />
      <Alert size='mini' type='warning' message='提示信息' textHighlight disableBackground />
      <Alert size='mini' type='error' message='提示信息' textHighlight disableBackground />
    </div>
  )
}

export default React.memo(DisableBackground)
