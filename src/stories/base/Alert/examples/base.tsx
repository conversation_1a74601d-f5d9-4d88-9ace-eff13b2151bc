import Alert from '@/stories/base/Alert'
import React, { FC } from 'react'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({ }) => {
  return (
    <div className="space-y-8px">
      <Alert type="success" message="提示信息" />
      <Alert type="info" message="提示信息" />
      <Alert type="warning" message="提示信息" />
      <Alert type="error" message="提示信息" />
      <Alert type="success" style={{ height: 70, lineHeight: '30px' }} iconSize="16px" message={`巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉
1. 巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉
2. 巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉
3. 巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉
4. 巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉巴拉`} />
    </div>
  )
}

export default React.memo(Base)
