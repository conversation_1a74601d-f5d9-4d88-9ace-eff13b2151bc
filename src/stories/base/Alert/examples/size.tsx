import Alert from '@/stories/base/Alert'
import React, { FC } from 'react'

export interface ISizeProps {}

const Size: FC<ISizeProps> = ({}) => {
  return (
    <div className='space-y-8px'>
      <Alert type='error' size='mini' message='使用场景：标签提示' />
      <Alert type='error' size='least' message='使用场景：卡片内提示' />
      <Alert type='error' size='small' message='使用场景：吸底提示' />
      <Alert type='error' message='使用场景：PC 端提示' />
    </div>
  )
}

export default React.memo(Size)
