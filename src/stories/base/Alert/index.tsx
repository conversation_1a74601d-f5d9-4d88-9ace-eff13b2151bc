import classNames from 'classnames'
import React, { FC, useMemo } from 'react'
import './index.scss'
import TitaScroll from '../TitaScroll'

export interface IAlertProps {
  type?: 'success' | 'info' | 'warning' | 'error'
  align?: 'left' | 'center' | 'right'
  iconSize?: string | number
  icon?: React.ReactNode
  /**
   * 尺寸
   * @default default
   */
  size?: 'default' | 'small' | 'least' | 'mini'
  /**
   * 隐藏 icon
   */
  disableIcon?: boolean
  /**
   * 文字是否高亮
   */
  textHighlight?: boolean
  /**
   * 禁用背景色
   */
  disableBackground?: boolean
  /**
   * 内容
   * @default 请注意
   */
  message: React.ReactNode
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  contentStyle?: React.CSSProperties
}

const preCls = 'tita-ui-alert'

export const Alert: FC<IAlertProps> = React.memo(
  ({
    size = 'default',
    textHighlight,
    disableBackground,
    icon: customIcon,
    type = 'info',
    message,
    align = 'left',
    iconSize,
    className,
    style,
    contentStyle,
  }) => {
    const icon = useMemo(() => {
      if (customIcon)
        return typeof customIcon === 'string' ? (
          <i className={`${preCls}__icon tu-icon-${customIcon}`} />
        ) : (
          <span className={`${preCls}__icon`}>{customIcon}</span>
        )
      return {
        success: (
          <i
            className={classNames(`${preCls}__icon`, 'tu-icon-H5-finish-m1')}
            style={{ fontSize: iconSize }}
          />
        ),
        info: (
          <i
            className={classNames(`${preCls}__icon`, 'tu-icon-tishi')}
            style={{ fontSize: iconSize }}
          />
        ),
        warning: (
          <i
            className={classNames(`${preCls}__icon`, 'tu-icon-tishi')}
            style={{ fontSize: iconSize }}
          />
        ),
        error: (
          <i
            className={classNames(`${preCls}__icon`, 'tu-icon-tishi')}
            style={{ fontSize: iconSize }}
          />
        ),
      }[type]
    }, [type])
    return (
      <div
        className={classNames(preCls, className, {
          [`${preCls}--type-${type}`]: type,
          [`${preCls}--textHighlight`]: textHighlight,
          [`${preCls}--disableBackground`]: disableBackground,
          [`${preCls}--size-${size}`]: size !== 'default',
          'justify-center': align === 'center',
          'justify-end': align === 'right',
        })}
        style={style}
      >
        {icon}
        <TitaScroll
          height='100%'
          className='w-full'
          innerStyle={{ width: '100%' }}
        >
          <pre className={`${preCls}__content`} style={contentStyle}>{message}</pre>
        </TitaScroll>
      </div>
    )
  }
)

export default Alert
