.tita-ui-alert {
  width: 100%;
  height: auto;
  min-height: 40px;
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
  color: #3f4755;
  padding: 10px 12px;
  border-radius: 8px;
  display: flex;
  align-items: flex-start;
  box-sizing: border-box;

  &__icon {
    line-height: inherit !important;
    font-size: 16px;
  }
  &__content {
    line-height: inherit;
    width: 100%;
    white-space: pre-wrap;
    margin-bottom: 0;
  }

  &--size-small {
    padding: 8px 12px;
    min-height: auto;
    font-size: 12px;
    line-height: 18px;
  }

  &--size-least {
    padding: 5px 8px;
    min-height: auto;
    font-size: 12px;
    line-height: 18px;
    border-radius: 6px;
  }

  &--size-mini {
    padding: 1px 4px;
    min-height: auto;
    font-size: 12px;
    line-height: 18px;
    border-radius: 4px;
  }

  > * + * {
    margin-left: 4px;
  }

  &--disableBackground {
    background-color: transparent !important;
  }

  &--type-success {
    background-color: rgba(0, 189, 22, 0.1);
  }
  &--type-success &__icon {
    color: #00bd16;
  }
  &--textHighlight.tita-ui-alert--type-success {
    color: #00bd16;
  }

  &--type-info {
    background-color: rgba(40, 121, 255, 0.1);
  }
  &--type-info &__icon {
    color: #1677ff;
  }
  &--textHighlight.tita-ui-alert--type-info {
    color: #1677ff;
  }

  &--type-warning {
    background-color: rgba(255, 125, 0, 0.1);
  }
  &--type-warning &__icon {
    color: #ff7d00;
  }
  &--textHighlight.tita-ui-alert--type-warning {
    color: #ff7d00;
  }

  &--type-error {
    background-color: rgba(240, 61, 38, 0.1);
  }
  &--type-error &__icon {
    color: #f03d26;
  }
  &--textHighlight.tita-ui-alert--type-error {
    color: #f03d26;
  }
}
