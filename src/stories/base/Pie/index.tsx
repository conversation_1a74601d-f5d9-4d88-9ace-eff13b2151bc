import React, { FC, useRef, useEffect } from 'react'
import classNames from 'classnames'
import merge from 'lodash/merge'

import * as echarts from 'echarts/core'
import {
  TooltipComponent,
  TooltipComponentOption,
  LegendComponent,
  LegendComponentOption,
  TitleComponent,
  TitleComponentOption,
} from 'echarts/components'
import { Pie<PERSON>hart, PieSeriesOption } from 'echarts/charts'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { useUpdateEffect } from 'ahooks'
import type { EChartsType } from 'echarts/types/dist/shared'

import './index.scss'
import { getLocale } from '@tita/utils';

interface IData {
  name: string
  value: number
}

export interface IPieProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  width?: number
  height?: number
  option: ECOption
}

echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  <PERSON><PERSON><PERSON>,
  <PERSON>vas<PERSON>ender<PERSON>,
  LabelLayout,
])

const preCls = 'tita-ui--pie'

type ECOption = echarts.ComposeOption<
  | PieSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | LegendComponentOption
>

export const Pie: FC<IPieProps> = React.memo(
  ({ className, style, width = 200, height = 240, option = {} }) => {
    const ref = useRef<HTMLElement | null>(null)
    const chartRef = useRef<EChartsType | null>(null)

    useEffect(() => {
      chartRef.current = echarts.init(ref.current as HTMLElement)
      let _option: ECOption = {
        title: {
          show: true, //显示策略，默认值true,可选为：true（显示） | false（隐藏）
          text: '28.6%', //主标题文本，'\n'指定换行
          link: '', //主标题文本超链接,默认值true
          subtext: getLocale('Pro_Mil_ToCoRate'), //副标题文本，'\n'指定换行
          sublink: '', //副标题文本超链接
          top: 70, //水平安放位置，默认为'left'，可选为：'center' | 'left' | 'right' | {number}（x坐标，单位px）
          left: 90, //垂直安放位置，默认为top，可选为：'top' | 'bottom' | 'center' | {number}（y坐标，单位px）
          backgroundColor: 'rgba(0,0,0,0)', //标题背景颜色，默认'rgba(0,0,0,0)'透明
          borderColor: '#fff', //标题边框颜色,默认'#ccc'
          borderWidth: 0, //标题边框线宽，单位px，默认为0（无边框）
          padding: 5, //标题内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距
          itemGap: 10, //主副标题纵向间隔，单位px，默认为10
          textStyle: {
            //主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
            fontFamily: 'PingFangSC-Medium, PingFang SC',
            fontSize: 20,
            color: '#141C28',
            fontStyle: 'normal',
            fontWeight: 500,
          },
          subtextStyle: {
            //副标题文本样式{"color": "#aaa"}
            fontFamily: 'PingFangSC-Medium, PingFang SC',
            fontSize: 12,
            color: '#89919F',
            fontStyle: 'normal',
            fontWeight: 400,
          },
        },
        tooltip: {
          show: false,
          trigger: 'item',
        },
        legend: {
          type: 'scroll',
          bottom: 32,
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          icon: 'circle',
          selectedMode: false,
        },
        series: [
          {
            name: getLocale('OKR_MyO_Ew_Completionrate'),
            type: 'pie',
            radius: [48, 80],
            center: ['50%', 90],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 4,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: undefined,
            labelLine: {
              show: false,
            },
            data: [
              {
                value: 1048,
                name: getLocale('OKR_MyO_Pop_Upd_Postoned'),
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#FF8C8D',
                      },
                      {
                        offset: 1,
                        color: '#FF595A',
                      },
                    ],
                  },
                },
              },
              {
                value: 735,
                name: getLocale('OKR_MyO_Butt_Inprogress'),
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#94C2FD',
                      },
                      {
                        offset: 1,
                        color: '#5B8FF9',
                      },
                    ],
                  },
                },
              },
              {
                value: 580,
                name: getLocale('OKR_MyO_E_From_Completed'),
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#5AD8A6',
                      },
                      {
                        offset: 1,
                        color: '#92F0D3',
                      },
                    ],
                  },
                },
              },
            ],
          },
        ],
      }
      let targetOption = merge(_option, option)
      chartRef.current.setOption(targetOption)
    }, [])

    useEffect(() => {
      const chartSize = () => chartRef.current?.resize()
      window.addEventListener('resize', chartSize)
      return () => {
        window.removeEventListener('resize', chartSize)
      }
    }, [])

    useUpdateEffect(() => {
      chartRef.current?.setOption(option)
    }, [option])

    return (
      <div
        // @ts-ignore
        ref={ref}
        style={{ ...style, width, height }}
        className={classNames(preCls, className)}
      ></div>
    )
  }
)

export default Pie
