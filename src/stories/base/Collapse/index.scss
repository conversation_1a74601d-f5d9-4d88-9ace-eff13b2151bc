.tita-ui-collapse {
  &__header {
    &--sticky {
      position: sticky;
      z-index: 10;
    }
  }
  &__content {
    transition: height 0.3s, opacity 0.3s;
    // 不能默认加 overflow: hidden，会导致嵌套的元素吸顶定位错误。
    // overflow: hidden;

    &--open {
      opacity: 1;
      pointer-events: auto;
    }
    &--hide {
      opacity: 0;
      pointer-events: none;

      // 避免嵌套元素被点击
      .tita-ui-collapse__content--open {
        pointer-events: none;
      }
    }
  }
}
