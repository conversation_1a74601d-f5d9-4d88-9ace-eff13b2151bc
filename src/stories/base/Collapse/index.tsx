import React, {
  FC,
  forwardRef,
  useContext,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'
import classNames from 'classnames'
import './index.scss'
import { useSize, useUpdateEffect } from 'ahooks'
import { CollapseContext } from './context'

export interface ICollapseProps
  extends Omit<
    React.DetailedHTMLProps<
      React.HTMLAttributes<HTMLDivElement>,
      HTMLDivElement
    >,
    'children'
  > {
  stickyHeader?: boolean
  stickyTop?: number
  headerStyle?: React.CSSProperties
  open?: boolean
  defaultOpen?: boolean
  renderTitle: (info: {
    open: boolean
    onChangeOpen: (open: boolean) => void
  }) => React.ReactNode
  contentStyle?: React.CSSProperties
  children:
    | React.ReactNode
    | ((info: {
        open: boolean
        onChangeOpen: (open: boolean) => void
      }) => React.ReactNode)
  className?: string
  style?: React.CSSProperties
}

export interface ICollapseRef {
  open: () => void
  close: () => void
}

const preCls = 'tita-ui-collapse'

export const Collapse = React.memo(
  forwardRef<ICollapseRef, ICollapseProps>(
    (
      {
        renderTitle,
        children,
        open: propsOpen,
        defaultOpen = true,
        headerStyle,
        contentStyle,
        stickyHeader,
        stickyTop,
        className,
        style,
        ...other
      },
      ref
    ) => {
      const { parentTitleHeight } = useContext(CollapseContext)
      const [open, setOpen] = useState(
        propsOpen !== undefined ? propsOpen : defaultOpen
      )
      useUpdateEffect(() => {
        if (propsOpen !== undefined) setOpen(propsOpen)
      }, [propsOpen])

      useImperativeHandle(ref, () => ({
        open: () => setOpen(true),
        close: () => setOpen(false),
      }))

      const titleRef = useRef<HTMLDivElement>(null)
      const titleHeight = useSize(titleRef)?.height || 0
      const innerRef = useRef<HTMLDivElement>(null)
      const innerHeight = useSize(innerRef)?.height || (open ? 'auto' : 0)

      return (
        <CollapseContext.Provider value={{ parentTitleHeight: titleHeight }}>
          <div
            className={classNames(preCls, className)}
            style={style}
            {...other}
          >
            <div
              ref={titleRef}
              className={classNames(`${preCls}__header`, {
                [`${preCls}__header--sticky`]: stickyHeader,
              })}
              style={{
                top: stickyHeader ? stickyTop || parentTitleHeight : undefined,
                ...headerStyle,
              }}
            >
              {renderTitle?.({ open, onChangeOpen: setOpen })}
            </div>
            <div
              className={classNames(`${preCls}__content`, {
                [`${preCls}__content--open`]: open,
                [`${preCls}__content--hide`]: !open,
              })}
              style={{ ...contentStyle, height: open ? innerHeight : 0 }}
            >
              <div ref={innerRef} className={`${preCls}__inner`}>
                {typeof children === 'function'
                  ? children?.({ open, onChangeOpen: setOpen })
                  : children || null}
              </div>
            </div>
          </div>
        </CollapseContext.Provider>
      )
    }
  )
)

export default Collapse
