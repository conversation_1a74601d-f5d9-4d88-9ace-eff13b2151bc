import React, { <PERSON> } from 'react'
import Collapse from '@/stories/base/Collapse'
import Button from '../../Button'
import { TextArea } from '../../Input'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  const [state, setState] = React.useState(0)
  console.log('render Collapse')

  return (
    <>
      <Collapse
        stickyHeader
        renderTitle={({ open, onChangeOpen }) => (
          <div>
            <Button
              onClick={() => {
                onChangeOpen(!open)
                setState((state) => state + 1)
                setState((state) => state + 1)
              }}
            >
              {open ? '收起' : '展开'}
            </Button>
          </div>
        )}
      >
        <TextArea
          autoSize={{
            minRows: 2,
          }}
        />
        <Collapse
          stickyHeader
          disableWatchHeight
          renderTitle={({ open, onChangeOpen }) => (
            <div>
              <Button onClick={() => onChangeOpen(!open)}>
                child{open ? '收起' : '展开'}
              </Button>
            </div>
          )}
        >
          <div className='h-[200px] bg-tablecloth'></div>
        </Collapse>
      </Collapse>
      <Collapse
        stickyHeader
        disableWatchHeight
        renderTitle={({ open, onChangeOpen }) => (
          <div>
            <Button onClick={() => onChangeOpen(!open)}>
              {open ? '收起' : '展开'}
            </Button>
          </div>
        )}
      >
        <div className='h-[200px] bg-tablecloth'></div>
      </Collapse>
      <Collapse
        stickyHeader
        disableWatchHeight
        renderTitle={({ open, onChangeOpen }) => (
          <div>
            <Button onClick={() => onChangeOpen(!open)}>
              {open ? '收起' : '展开'}
            </Button>
          </div>
        )}
      >
        <div className='h-[200px] bg-tablecloth'></div>
      </Collapse>
    </>
  )
}

export default React.memo(Base)
