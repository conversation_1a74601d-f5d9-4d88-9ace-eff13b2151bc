interface Size {
  width: number
  textNum: number
  fontSize: number
}
export type SizeCategory =
  | 150
  | 130
  | 100
  | 80
  | 64
  | 56
  | 50
  | 48
  | 44
  | 40
  | 36
  | 33
  | 32
  | 30
  | 28
  | 26
  | 24
  | 23
  | 22
  | 20
  | 16
  | 'default'

const size = (type: SizeCategory): Size => {
  switch (type) {
    case 150:
      return {
        width: 150,
        textNum: 5,
        fontSize: 24,
      }
    case 130:
      return {
        width: 130,
        textNum: 5,
        fontSize: 22,
      }
    case 100:
      return {
        width: 100,
        textNum: 4,
        fontSize: 20,
      }
    case 80:
      return {
        width: 80,
        textNum: 4,
        fontSize: 16,
      }
    case 64:
      return {
        width: 64,
        textNum: 3,
        fontSize: 16,
      }
    case 56:
      return {
        width: 56,
        textNum: 3,
        fontSize: 16,
      }
    case 50:
      return {
        width: 50,
        textNum: 1,
        fontSize: 16,
      }
    case 48:
      return {
        width: 48,
        textNum: 1,
        fontSize: 16,
      }
    case 44:
      return {
        width: 44,
        textNum: 1,
        fontSize: 16,
      }
    case 40:
      return {
        width: 40,
        textNum: 1,
        fontSize: 12,
      }
    case 36:
      return {
        width: 36,
        textNum: 1,
        fontSize: 16,
      }
    case 33:
      return {
        width: 33,
        textNum: 1,
        fontSize: 12,
      }
    case 32:
      return {
        width: 32,
        textNum: 1,
        fontSize: 12,
      }
    case 30:
      return {
        width: 30,
        textNum: 1,
        fontSize: 12,
      }
    case 28:
      return {
        width: 28,
        textNum: 1,
        fontSize: 12,
      }
    case 26:
      return {
        width: 26,
        textNum: 1,
        fontSize: 12,
      }
    case 24:
      return {
        width: 24,
        textNum: 1,
        fontSize: 12,
      }
    case 23:
      return {
        width: 23,
        textNum: 1,
        fontSize: 12,
      }
    case 22:
      return {
        width: 22,
        textNum: 1,
        fontSize: 12,
      }
    case 20:
      return {
        width: 20,
        textNum: 1,
        fontSize: 12,
      }
    case 16:
      return {
        width: 16,
        textNum: 1,
        fontSize: 12,
      }
    default:
      return {
        width: 24,
        textNum: 1,
        fontSize: 12,
      }
  }
}

export default size
