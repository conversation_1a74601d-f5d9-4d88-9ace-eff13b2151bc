.tita-ui--avatar-container {
  position: relative;
  display: inline-block;
}

.tita-ui--avatar-wrapper {
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}

.tita-ui--avatar-avatar--text {
  width: 100%;
  height: 100%;
  text-align: center;
  line-height: 1;
  color: #fff;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
}

.tita-ui--avatar__delete-wrapper {
  position: absolute;
  top: -4px;
  right: -4px;
  z-index: 1;
  width: 14px;
  height: 14px;
  background: #F0F2F5;
  box-shadow: 0px 2px 4px 0px rgba(127, 145, 180, 0.2);
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  display: none;

  &:hover {
    cursor: pointer;
  }
}

.tita-ui--avatar--enter {
  display: flex;
}

.tita-ui--avatar__disable-mark {
  border-radius: 50%;
  background-color: rgba(20, 28, 40, 0.5);
  color: #ffffff;
  display: inline-block;
  text-align: center;
  position: absolute;
  // top: 2px;
  // left: 3px;
  left: 0!important;
  top: 0!important;
}

.tita-ui--avatar-iconAvatar {
  background-color: #2879ff;
}

.tita-ui--avatar--dashed {
  border: 1px dashed #BFC7D5;
  border-radius: 50%;
  padding: 1px;

}