import React, { FC, useState, useCallback, useMemo, CSSProperties } from 'react'
import Tooltip from '../Tooltip'
import classNames from 'classnames'
import Size, { SizeCategory } from './size'
// @ts-ignore
import { getLocale, isOfficialSite } from '@tita/utils'
import Close from '../Close'

import './index.scss'

const preCls = 'tita-ui--avatar'

export interface IAvatarProps {
  src?: string
  name?: string | React.ReactNode
  color?: string
  className?: string
  isDashed?: boolean
  size?: SizeCategory
  style?: React.CSSProperties
  showNameTips?: boolean
  onClick?: () => void
  onMouseEnter?: () => void
  onMouseLeave?: () => void
  canDelete?: boolean
  onDelete?: () => void
  isDept?: boolean
  showDisableMark?: boolean // 是否显示离职禁用标签，默认 false
  jobState?: 0 | 1 // 人员状态：0-离职|禁用，1-在职
  icon?: string
  iconStyle?: CSSProperties
  contentWidth?: string | number
  customTipContent?: string | React.ReactNode
}

interface Size {
  width: number
  textNum: number
  fontSize: number
}

enum UserJobState {
  Dimission,
  OnTheJob,
}

export const Avatar: FC<IAvatarProps> = React.memo((props) => {
  let {
    size: sizeNumber,
    src,
    style,
    name,
    showNameTips = false,
    color = '#2879ff',
    canDelete,
    onDelete,
    className,
    icon,
    showDisableMark,
    jobState = UserJobState.OnTheJob,
    contentWidth,
    isDashed,
    customTipContent,
    iconStyle,
  } = props
  const size = Size(sizeNumber || 'default')
  // 渲染离职禁用标签
  const renderDisableMark = () => {
    if (jobState !== 0 || !showDisableMark) return null
    return (
      <span
        className={`${preCls}__disable-mark`}
        style={{
          width: size.width,
          height: size.width,
          fontSize: size.fontSize,
          lineHeight: size.width + 'px',
          ...style,
        }}
      >
        {isOfficialSite() ? getLocale('Per_Ma_form_detail_Resigned') || '离职' : '禁用'}
      </span>
    )
  }
  const renderImgAvatar = () => (
    <img
      className={classNames(`${preCls}`, className, {
        [`${preCls}--dashed`]: isDashed,
      })}
      style={{
        width: size.width,
        height: size.width,
        verticalAlign: 'top',
        borderRadius: '50%',
        overflow: 'hidden',
        flexShrink: 0,
        ...style,
      }}
      src={src}
      alt=''
    />
  )
  const renderTextAvatar = () => {
    // TODO: 传递name参数为jsx时的处理出错
    // 限制名称长度
    let cutName: string = ''
    if (typeof name === 'string') {
      let cutNameLen = 0
      for (let i = 0, lent = name?.length || 0; i < lent; i++) {
        let len = 1
        // 对于英文名称进行处理
        name[i].match(/[0-9a-z]/) && (len = 0.6)
        if (cutNameLen + len > size.textNum) break
        cutNameLen += len
        cutName += name[i]
      }
    }
    let renderTextAvatar = null
    if (icon)
      contentWidth = iconStyle?.fontSize || size.fontSize,
      renderTextAvatar = (
        <i
          className={classNames(`tu-icon-${icon}`, `${preCls}-iconAvatar`)}
          style={iconStyle}
        ></i>
      )
    if (cutName && !icon) renderTextAvatar = cutName
    if (!cutName && !icon){
      // 处理截断，高阳需求中文取后N个字，英文取前N个字。目前中英文及数字混用没有规则，暂时先不做
      // if(typeof name === 'string'){
      //   renderTextAvatar = name.slice(-size.textNum)
      // }else {
      //   renderTextAvatar = name?.props?.name?.slice(-size.textNum)
      // }
      renderTextAvatar = name
    } 

    return (
      <div
        className={classNames(
          `${preCls} ${preCls}-avatar--text whitespace-nowrap`,
          className,
          {
            [`${preCls}--dashed`]: isDashed,
          }
        )}
        style={{
          width: size.width,
          height: size.width,
          backgroundColor: color || '#2879ff',
          fontSize: `${size.fontSize}px`,
          flexShrink: 0,
          ...style,
        }}
      >
        <div
          style={{
            width: contentWidth || size.fontSize * size.textNum,
            overflow: 'hidden',
          }}
        >
          {renderTextAvatar && renderTextAvatar}
        </div>
      </div>
    )
  }

  const hasAvatar = useMemo(() => {
    if (!src) return false
    if (typeof src === 'string') return !src.match(/default\w*.jpg$/)
    // @ts-ignore
    if (typeof src === 'object') return src?.hasAvatar
  }, [src])

  let avatarContent = hasAvatar ? renderImgAvatar() : renderTextAvatar()

  if (showNameTips) {
    avatarContent = (
      <Tooltip
        overlay={customTipContent ?? name}
        placement='top'
        align={{ offset: [0, 5] }}
      >
        {avatarContent}
      </Tooltip>
    )
  }

  return (
    <Close onClose={onDelete}>
      {jobState !== 0 || !showDisableMark ? (
        avatarContent
      ) : (
        <div style={{ position: 'relative', flexShrink: 0 }}>
          {avatarContent}
          {/* 离职禁用标签 */}
          {renderDisableMark()}
        </div>
      )}
    </Close>
  )
})

export default Avatar
