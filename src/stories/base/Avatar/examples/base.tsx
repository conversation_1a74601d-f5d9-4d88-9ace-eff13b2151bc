import React, { FC, ReactNode } from 'react'
import Avatar, { IAvatarProps } from '../index'
import './base.scss'

export interface IBaseProps extends IAvatarProps {}
const avatarUrl: string =
  '//file.tita.work/TitaImage/500040/fe08e1d3807747dca4e8bd6503516655_m.jpg'

const Base: FC<IBaseProps> = (props) => {
  return (
    <div className='flex space-x-12px'>
      <Avatar
        icon='search'
        name={'AO'}
        size={30}
        showNameTips={false}
        canDelete={true}
      />
      <Avatar
        src={avatarUrl}
        name='刘二清'
        size={30}
        showNameTips={false}
        canDelete={true}
      />
      <Avatar name='刘二清' size={30} showNameTips canDelete={true} />
      <Avatar name='hcowey7c' size={30} showNameTips={false} canDelete={true} />
      <Avatar
        icon='shenpizhonxin'
        size={48}
        iconStyle={{
          fontSize: 28
        }}
      />
      <Avatar
        name='马斯克'
        size={48}
      />
    </div>
  )
}

export default React.memo(Base)
