import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'

export interface IPageNumProps {
  /**
   * 当前页数
   * @default 1
   */
  current: number
  /**
   * 总页数
   * @default 10
  */
 total: number
 /**
  * icon
   * @default task
   */
  icon?: string
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  iconStyle?: React.CSSProperties
}

const preCls = 'tita-ui-page-num'

export const PageNum: FC<IPageNumProps> = React.memo(({ current, total, icon, className, style, iconStyle }) => {
  return (
    <div className={classNames('flex items-center', preCls, className)} style={style}>
      {icon && <i className={`tu-icon-${icon} ${preCls}__icon`} style={iconStyle} />}
      <span>{current}/{total}</span>
    </div>
  )
})

export default PageNum
