import React, { FC, useEffect, useRef, useState } from 'react'
import classNames from 'classnames'
import { useSize } from 'ahooks'
import './index.scss'

// @ts-ignore
export interface IOverflowObserverProps extends React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  onChange?: (overflow: boolean) => void
  children?: React.ReactNode
}

const preCls = 'tita-ui-overflow-observer'

export const OverflowObserver: FC<IOverflowObserverProps> = React.memo(({ onChange, children, className, ...other }) => {
  const containerRef = useRef() as React.MutableRefObject<HTMLDivElement>
  const containerRefSize = useSize(containerRef)
  const containerWidth = containerRefSize && containerRefSize.width || 0

  const contentRef = useRef() as React.MutableRefObject<HTMLDivElement>
  const contentRefSize = useSize(contentRef)
  const contentWidth = contentRefSize && contentRefSize.width || 0

  useEffect(() => {
    if (onChange) onChange(contentWidth > containerWidth)
  }, [containerWidth, contentWidth])

  return (
    <div ref={containerRef} className={classNames(preCls, className)} {...other}>
      {children}
      <span className='inline-block absolute opacity-0 left-0 pointer-events-none' ref={contentRef}>{children}</span>
    </div>
  )
})

export default OverflowObserver
