import React, { FC } from 'react'
import OverflowObserver from '@/stories/base/OverflowObserver'

export interface IBaseProps {
  /**
   * 子元素宽度
   * @default 100
   */
  width: number
}

const Base: FC<IBaseProps> = ({ width }) => {
  return (
    <OverflowObserver className='w-80 p-4 border rounded-md shadow-md flex flex-col' onChange={overflow => console.log('overflow', overflow)}>
      改变 width 参数，观察 onChange 的返回值
      <div className='bg-slate-500 inline-block' style={{ width, height: 50 }}>
      </div>
    </OverflowObserver>
  )
}

export default React.memo(Base)
