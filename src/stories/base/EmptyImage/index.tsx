import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'

export interface IEmptyImageProps {
  image: string
  /** 上图中的标题 */
  title?: string
  /** 图片尺寸 */
  height: number
  width?: number
  maxHeight?: string | number
  /** 上图中的按钮 */
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-empty-image'

export const EmptyImage: FC<IEmptyImageProps> = React.memo(({ image, title, width, height, maxHeight, children, className, style }) => {
  const hasTitle = title && title.length > 0
  return (
    <div className={classNames(preCls, {
      [`${preCls}--hasTitle`]: hasTitle
    })}>
      <div className={`${preCls}__image`}>
        <img src={image} style={{
          width: width ? `${width}px` : 'auto',
          height: height ? `${height}px` : 'auto',
          maxHeight
        }} alt="" />
      </div>
      <div className={`${preCls}__content`}>
        <p className={`${preCls}__title`}>{title}</p>
        <div className={className} style={style}>
          {children}
        </div>
      </div>
    </div>
  )
})

export default EmptyImage
