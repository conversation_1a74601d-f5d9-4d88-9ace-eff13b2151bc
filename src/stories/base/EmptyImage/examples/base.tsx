import React, { <PERSON> } from 'react'
import EmptyImage from '@/stories/base/EmptyImage'
import emptyImagePng from './images/empty-image.png'
import Button from '../../Button'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({ }) => {
  return (
    <>
      <p>无标题</p>
      <EmptyImage image={emptyImagePng} height={279}>
        <Button type="border" primary>Hello</Button>
      </EmptyImage>
      <p>有标题</p>
      <EmptyImage image={emptyImagePng} height={279} title='这是一个空状态示例'>
        <Button type="border" primary>Hello</Button>
      </EmptyImage>
    </>
  )
}

export default React.memo(Base)
