import { useLocalStorage } from '@/hooks/useLocalStorage'
import SearchCheckbox, {
  ISearchCheckboxProps,
  SearchCheckboxOption,
} from '@/stories/base/SearchCheckbox'
import { numberToChinese } from '@/utils/string'
import { filterTree } from '@/utils/tree'
import { arr2Dic } from '@tita/utils'
import { useUpdateEffect } from 'ahooks'
import classNames from 'classnames'
import React, {
  FC,
  ReactElement,
  useCallback,
  useMemo,
  useRef,
  useState,
} from 'react'
import SortList, { ISortListProps } from '../SortList'
import Tooltip from '../Tooltip/tooltip'
import TitaScroll from '../TitaScroll'
import Toast from '@/stories/base/Toast'
import './index.scss'

const preCls = 'tita-ui-data-sort'

export interface IDataSortProps extends ISearchCheckboxProps {
  onChange?: (value: (string | number)[]) => void
  /** 最少选中的个数 */
  minNum?: number
  /** 最多选中的个数 */
  maxNum?: number
  /** 本地缓存 */
  cacheKey?: string
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const defaultValue: any[] = []

export const DataSort: FC<IDataSortProps> = React.memo(
  ({
    datas,
    cacheKey,
    value: propsValue,
    minNum,
    maxNum,
    onChange,
    render,
    className,
    style,
    ...other
  }) => {
    const [value, setValue] = useLocalStorage(cacheKey, propsValue || defaultValue)
    const [timer, setTimer] = useState('current')
    const valueRef = useRef(value)
    valueRef.current = value

    useUpdateEffect(() => {
      // 如果没有开启本地缓存时,则响应外部的变更
      if (!cacheKey) setValue(propsValue || [])
    }, [propsValue])

    const items = useMemo(() => {
      let items: SearchCheckboxOption<any>[] = []
      filterTree(datas, (item) => {
        if (!item.isGroup) {
          items.push(item)
        }
        return !item.isGroup
      })
      return items
    }, [datas])

    const [datasDic, setDatasDic] = useState(
      arr2Dic(items, 'key') as Record<
        string | number,
        SearchCheckboxOption<any>
      >
    )
    useUpdateEffect(() => {
      setDatasDic(
        arr2Dic(items, 'key') as Record<
          string | number,
          SearchCheckboxOption<any>
        >
      )
    }, [items])
    const boards = useMemo(
      () => value.map((key) => ({ ...datasDic[key] })),
      [value, datasDic]
    )

    const removeItem = useCallback((key: string | number) => {
      const newValue = valueRef.current.filter((item) => item !== key)
      setValue(newValue)
      onChange?.(newValue)
    }, [])

    const renderContainer: ISortListProps['customListContainer'] = useCallback(
      ({ listNode }: { listNode: ReactElement }) => {
        return (
          <div
            style={{
              height: '100%',
            }}
          >
            {listNode}
          </div>
        )
      },
      []
    )

    const renderRow: ISortListProps<SearchCheckboxOption<any>>['render'] =
      useCallback(
        ({ data }) => {
          const showMinTips = minNum && valueRef.current.length <= minNum
          const index = datas?.findIndex((item) => item.key === data.key)
          return (
            <div className='pr-20px' key={data.key}>
              <div
                className={classNames(`${preCls}__sort-item`, 'space-x-10px')}
              >
                <div className='flex items-center space-x-10px w-full'>
                  {!data.disable ? (
                    <i className='tu-icon-shitu cursor-move text-[#6f7886]' />
                  ) : (
                    <i style={{ width: 14 }} />
                  )}
                  {render ? (
                    render(data, index)
                  ) : (
                    <p className=' text-#3f4755 leading-[22px] text-[14px]'>
                      {data.title}
                    </p>
                  )}
                </div>
                {(showMinTips && (
                  <Tooltip
                    overlay={`至少显示${numberToChinese(minNum + '')}个字段`}
                  >
                    <i
                      className='tu-icon-canceled cursor-pointer text-[#C0C8D5]'
                      style={{ lineHeight: '18px' }}
                    />
                  </Tooltip>
                )) ||
                  (!data.disable && (
                    <i
                      className='tu-icon-canceled cursor-pointer text-[#424a58]'
                      style={{ lineHeight: '18px' }}
                      onClick={() => removeItem(data.key)}
                    />
                  ))}
              </div>
            </div>
          )
        },
        [datas]
      )

    const onDragEndHandler = useCallback(
      (value: SearchCheckboxOption<any>[]) => {
        const keys = value.map((item) => item.key)
        setValue(keys)
        onChange?.(keys)
      },
      []
    )

    const onChangeCheckbox = (
      _: any,
      info: { type: 'add' | 'remove'; key: string | number }
    ) => {
      let newValue = []
      if (info.type === 'add') {
        if (maxNum && maxNum <= value.length) {
          Toast.Error(`最多选中${maxNum}条数据`)
          setTimer(new Date().getTime().toString())
          return false
        }
        newValue = [...value, info.key]
      } else {
        newValue = value.filter((item) => item !== info.key)
      }
      setValue(newValue)
      onChange?.(newValue)
    }

    return (
      <div className={classNames(preCls, className)} style={style}>
        <div className='w-full px-20px pt-16px border-r border-[#DFE3EA]'>
          <SearchCheckbox
            key={`SearchCheckbox-${timer}`}
            value={value}
            datas={datas}
            onChange={onChangeCheckbox}
            defaultExpendAll={other.defaultExpendAll}
          />
        </div>
        <div className='flex flex-col w-full pl-20px pt-[23px]'>
          <p className='text-#89919f leading-[22px] text-[14px] mb-12px'>
            已选择 {value.length} 项字段，可上下拖动排序
          </p>
          <TitaScroll
            height='100%'
            innerStyle={{
              width: '100%',
              paddingBottom: 10,
            }}
            className='overflow-hidden'
            colBarInset
          >
            <SortList
              value={boards}
              onChange={onDragEndHandler}
              rowKey='key'
              space={8}
              // customListContainer={renderContainer}
              render={renderRow}
            />
          </TitaScroll>
        </div>
      </div>
    )
  }
)

export default DataSort
