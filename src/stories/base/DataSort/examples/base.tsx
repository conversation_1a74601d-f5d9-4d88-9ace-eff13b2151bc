import React, { FC, useState } from 'react'
import DataSort from '@/stories/base/DataSort'

export interface IBaseProps {}

const datas = new Array(100).fill(0).map((_, i) => ({
  title: `字段 ${i}`,
  key: i,
}))

const Base: FC<IBaseProps> = ({}) => {
  const [value, setValue] = useState<(string | number)[]>(['1', '2'])
  return (
    <div className='h-[423px]'>
      <DataSort
        defaultExpendAll
        datas={[
          {
            title: '基础选项',
            isGroup: true,
            key: 'base',
            children: [
              {
                title: '选项1',
                key: '1',
              },
              {
                title: '选项2',
                key: '2',
              },
              {
                title: '选项3',
                key: '3',
              },
            ],
          },
          {
            title: '高级选项',
            isGroup: true,
            key: 'advanced',
            children: [
              {
                title: '选项1',
                key: '4',
              },
              {
                title: '选项2',
                key: '5',
              },
              {
                title: '选项3',
                key: '6',
              },
            ],
          },
        ]}
        value={value}
        minNum={1}
        onChange={setValue}
      />
    </div>
  )
}

export default React.memo(Base)
