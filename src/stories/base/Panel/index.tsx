import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'

export interface IPanelProps extends Omit<React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, 'title'> {
  /**
   * 内边距
   * @editType string
   * @default 20px
   */
  padding?: string
  /**
   * 外边距
   * @editType string
   */
  margin?: string
  /**
   * 高度
   * @editType string
   */
  height?: string | number
  /**
   * 最小高度
   * @editType string
   */
  minHeight?: string | number
  /** class */
  className?: string
  /** class */
  contentClassName?: string
  /**
   * 自定义样式
   * @editType json
   */
  style?: React.CSSProperties
  /**
   * 子元素
   * @editType string
   * @default ...
   */
  children?: React.ReactNode
  /**
   * 标题
   * @editType string
   * @default 基本信息
   */
  title?: string | React.ReactNode
}

const preCls = 'tita-ui-panel'

export const Panel: FC<IPanelProps> = React.memo((props) => {

  const { contentClassName, className, padding, margin, height, minHeight, style, children, title, ...other } = props

  const renderTitle = (title: string | React.ReactNode | (() => React.ReactNode)) => {
    if (typeof title === 'string') return <div className=' leading-[22px] font-[500] text-[#141C28] mb-16px'>{title}</div>
    if (typeof title === 'function') return title()
    return title
  }

  return (
    <div className={classNames(preCls, 'bg-panel dark:bg-panel-dark', className)} style={{ padding, height, minHeight, margin, ...style }} {...other}>
      {
        title && renderTitle(title)
      }
      <div className={classNames("h-full w-full", contentClassName)}>
        {children}
      </div>
    </div>
  )
})

export default Panel