.tita-ui--flip-card {
  perspective: 1000px; /* 创建3D效果的透视 */

  &.back .tita-ui--flip-card__inner,
  &.back .tita-ui--flip-card__controller {
    transform: rotateY(-180deg) translateZ(1px); /* 鼠标悬停时翻转180度 */
  }

  &__inner {
    width: 100%;
    transition: transform 600ms; /* 设置动画的过渡效果 */
    transform-style: preserve-3d; /* 设置内部元素保持3D效果 */
    position: relative;
  }

  &__front,
  &__back {
    width: 100%;
    height: min-content;
    backface-visibility: hidden; /* 隐藏背面内容 */
    position: absolute;
    z-index: 5;
  }

  &__back {
    transform: rotateY(180deg); /* 默认显示背面内容 */
  }

  &__controller {
    position: absolute;
    z-index: 10;
  }

  &.animating .tita-ui--flip-card__controller {
    opacity: 0;
  }

  &.front .tita-ui--flip-card__controller {
    right: 25px;
  }
  &.back .tita-ui--flip-card__controller {
    left: 25px;
  }
}
