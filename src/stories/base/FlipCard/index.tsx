import React, {
  FC,
  forwardRef,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import classNames from 'classnames'
import './index.scss'

export interface IFlipCardProps {
  children: React.ReactNode
  /** 自定义类名 */
  className?: string
  /** 翻转切换控制器 */
  controller?: React.ReactElement
  /** 翻转动画过渡时长
   * @default 300ms
   */
  duration?: number
  /** 容器高度
   * @default 500
   */
  height?: number
  /**
   * @default true
   */
  isFront: boolean
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 容器宽度
   * @default 500
   */
  width?: number
  /** 切换事件
   * @param {boolean} isFront 切换前状态
   * @returns boolean true-可以翻转 | false-禁止翻转
   * @returns void 不做处理
   */
  onChange?: (isFront: boolean) => boolean | void
  /** 根据当前显示的 正面 || 反面 判断来自定义渲染 controller
   * @param isFront
   */
  renderController?: (isFront: boolean) => React.ReactElement
}

const preCls = 'tita-ui--flip-card'

export const FlipCard = React.memo(
  forwardRef(
    (
      {
        children,
        className,
        controller,
        duration = 300,
        height = 500,
        isFront = true,
        onChange,
        renderController,
        style,
        width = 500,
      }: IFlipCardProps,
      ref
    ) => {
      const [isExecutingAnimation, setIsExecutingAnimation] = useState(false)
      const [isShowFrontContent, setIsShowFrontContent] = useState(isFront)

      const timerRef = useRef<NodeJS.Timeout>()

      const onControllerClick = () => {
        const res = onChange?.(isShowFrontContent)
        if (res !== false) {
          setIsShowFrontContent(!isShowFrontContent)
          setIsExecutingAnimation(true)
          clearTimeout(timerRef.current)
          timerRef.current = setTimeout(() => {
            setIsExecutingAnimation(false)
            clearTimeout(timerRef.current)
          }, duration)
        }
      }

      const controllerContent = useMemo(() => {
        const _controller = renderController?.(isShowFrontContent) || controller
        if (!_controller) return null
        return React.cloneElement(_controller, {
          className: classNames(
            `${preCls}__controller`,
            _controller.props.className
          ),
          onClick: onControllerClick,
        })
      }, [isShowFrontContent])

      useImperativeHandle(ref, () => ({
        flip: onControllerClick,
      }))

      return (
        <section
          className={classNames(preCls, className, {
            animating: isExecutingAnimation,
            back: !isShowFrontContent,
            front: isShowFrontContent,
          })}
          style={{ ...style, height, width }}
        >
          <div className={`${preCls}__inner`}>
            {controllerContent}
            <div className={`${preCls}__front`}>
              {React.Children.toArray(children)[0]}
            </div>
            <div className={`${preCls}__back`}>
              {React.Children.toArray(children)[1]}
            </div>
          </div>
        </section>
      )
    }
  )
)

export default FlipCard
