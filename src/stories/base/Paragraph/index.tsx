import classNames from 'classnames'
import isEmpty from 'lodash/isEmpty'
import isNumber from 'lodash/isNumber'
import isString from 'lodash/isString'
import React, { FC, useEffect, useState } from 'react'
import Ellipsis from '../Ellipsis'
import { IInputProps, Input } from '../Input/index'
import './index.scss'

export interface IParagraphProps extends Omit<IInputProps, 'onSubmit' | 'ref'> {
  children?: string | number | undefined
  className?: string
  style?: React.CSSProperties
  icon?: string
  iconColor?: string
  text?: string | number | undefined
  ellipse?: boolean
  disableEdit?: boolean
  disableIcon?: boolean
  level?: number
  editable?: boolean
  onSubmit?: (value: string | number | undefined) => void
}

const TITLE_ELE_LIST = [1, 2, 3, 4, 5] as const

type TITLE_LIST = typeof TITLE_ELE_LIST[number]

const preCls = 'tita-ui--paragraph'

export const Paragraph: FC<IParagraphProps> = React.memo(
  ({
    className,
    style,
    children,
    icon,
    iconColor = '#1677ff',
    text,
    ellipse,
    level,
    editable,
    onChange,
    onSubmit,
    disableEdit,
    disableIcon,
    ...restProps
  }) => {
    const [editing, setEditing] = useState(false)

    useEffect(() => {
      if (editable !== undefined) setEditing(editable)
    }, [editable])

    const [value, setValue] = useState(() => {
      if (!isEmpty(text)) {
        return text
      } else {
        if (isString(children) || isNumber(children)) {
          return children
        }
      }
      return ''
    })

    useEffect(() => {
      setValue(text)
    }, [text])

    let levelClassName = ''
    if (level && TITLE_ELE_LIST.includes(level as TITLE_LIST)) {
      levelClassName = `h${level}`
    }

    const onEditChange = (value: string) => {
      setValue(value)
      onChange?.(value)
    }

    const onEnter = () => {
      setEditing(false)
      onSubmit?.(value)
    }

    const onBlur = () => {
      setEditing(false)
      onSubmit?.(value)
    }

    const onEdit: React.MouseEventHandler<HTMLElement> = (e) => {
      e.stopPropagation()
      setEditing(true)
    }

    const renderContent = () => {
      if (!editing) {
        return (
          <div className={`${preCls}__edit-wrapper`}>
            <div
              className={classNames(`${preCls}__edit-text`, levelClassName)}
              style={{ maxWidth: '100%', width: 'calc(100% - 20px)' }}
            >
              {ellipse ? <Ellipsis>{value}</Ellipsis> : value}
            </div>
            {!disableEdit && !disableIcon && (
              <div
                className={(classNames(`${preCls}__edit-icon`), levelClassName)}
              >
                <i
                  onClick={onEdit}
                  className={`tu-icon-${icon ?? 'edit'}`}
                  color={iconColor}
                />
              </div>
            )}
          </div>
        )
      } else {
        return (
          <Input
            autoFocus={true}
            className={classNames(`${preCls}__input`, levelClassName)}
            onEnter={onEnter}
            onBlur={onBlur}
            onChange={onEditChange}
            // @ts-ignore
            value={value}
            onClick={(e) => {
              e.stopPropagation()
              if (restProps.onClick) restProps?.onClick(e)
            }}
            {...restProps}
          ></Input>
        )
      }
    }

    return (
      <div className={classNames(preCls, className)} style={style}>
        {renderContent()}
      </div>
    )
  }
)

export default Paragraph
