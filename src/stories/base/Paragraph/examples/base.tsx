import React, { FC, useState } from 'react'
import Paragraph from '@/stories/base/Paragraph'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  const [text, setText] = useState('hahahhaahhahahh')
  const onChange = (value) => {
    setText(text)
  }
  return (
    <div>
      <Paragraph onChange={onChange} level={1} text={text} />
      <Paragraph onChange={onChange} level={2} text={text} />
      <Paragraph onChange={onChange} level={3} text={text} />
      <Paragraph onChange={onChange} level={4} text={text} />
      <Paragraph onChange={onChange} level={5} text={text} />
      <div style={{ width: 100 }}>
        <Paragraph ellipse onChange={onChange} text={text} />
      </div>
    </div>
  )
}

export default React.memo(Base)
