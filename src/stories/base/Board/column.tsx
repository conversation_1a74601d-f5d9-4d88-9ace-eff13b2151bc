import React, { FC, useCallback, useEffect, useRef, useState } from 'react'
import classNames from 'classnames'
import { VariableSizeList as List } from 'react-window'
import { BoardItem, IBoardProps, RenderRows } from '.'
import './index.scss'
import {
  Draggable,
  DraggableProvided,
  DraggableRubric,
  DraggableStateSnapshot,
  Droppable,
  DroppableProvided,
  DroppableStateSnapshot,
} from 'react-beautiful-dnd'
import Row from './row'

export interface IColumnProps<Data = unknown> {
  board: BoardItem<Data>
  renderColumn: IBoardProps['renderColumn']
  renderRow: IBoardProps['renderRow']
  index: number
  columnKey: string
  rowKey: string
  itemHeight?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-column'

export const Column: FC<IColumnProps> = React.memo(
  ({
    board,
    index,
    renderColumn,
    columnKey,
    rowKey,
    renderRow,
    itemHeight,
    className,
    style,
  }) => {
    const [sizes, setSizes] = useState<Record<string, number>>({})

    const getItemSize = useCallback(
      (index: number) => {
        return itemHeight || sizes[index]
      },
      [sizes]
    )
    const listRef = useRef() as React.MutableRefObject<any>

    // 根据索引，设置Item高度
    const setItemSize = useCallback((index: number, size: number) => {
      if (itemHeight) return

      setSizes((prevSize) => {
        return {
          ...prevSize,
          [index]: size,
        }
      })
      listRef.current.resetAfterIndex(index, false)
    }, [])

    const rowRender = useCallback(
      (props: { data: any[]; index: number; style?: React.CSSProperties }) => {
        const { data, index, style } = props
        const rowData: any = data[index]
        if (!rowData || rowData[rowKey] === undefined) return <></>
        return (
          <Draggable
            draggableId={rowData[rowKey]?.toString?.()}
            index={index}
            key={rowData[rowKey]}
            isDragDisabled={rowData.disable}
          >
            {(
              provided: DraggableProvided,
              snapshot: DraggableStateSnapshot
            ) => (
              <Row
                provided={provided}
                board={board}
                data={rowData}
                index={index}
                rowKey={rowKey}
                renderRow={renderRow}
                setItemSize={setItemSize}
                isDragging={snapshot.isDragging}
                style={style}
              />
            )}
          </Draggable>
        )
      },
      [renderRow]
    )

    const renderRows = useCallback(
      ({ droppableProvided }: { droppableProvided: DroppableProvided }) =>
        ((option) => {
          // return (
          //   <div ref={droppableProvided.innerRef}>
          //     {board.rows.map((quote: any, index: number) => (
          //       <Draggable key={quote.id} draggableId={quote.id} index={index}>
          //         {(
          //           provided: DraggableProvided,
          //           snapshot: DraggableStateSnapshot,
          //         ) => (
          //           <Row
          //             provided={provided}
          //             data={quote}
          //             index={index}
          //             rowKey={rowKey}
          //             renderRow={renderRow}
          //             setItemSize={setItemSize}
          //             isDragging={snapshot.isDragging}
          //             style={style}
          //           />
          //         )}
          //       </Draggable>
          //     ))}
          //     {droppableProvided.placeholder}
          //   </div>
          // )
          return (
            <List
              height={option?.height}
              ref={listRef}
              itemCount={board.rows?.length || 0}
              itemSize={getItemSize}
              width={option.width || '100%'}
              outerRef={droppableProvided.innerRef}
              itemData={board.rows}
            >
              {rowRender}
            </List>
          )
        }) as RenderRows,
      [board.rows, sizes, rowRender]
    )

    return (
      <Droppable
        droppableId={board[columnKey].toString()}
        type='card'
        mode='virtual'
        renderClone={(
          provided: DraggableProvided,
          snapshot: DraggableStateSnapshot,
          rubric: DraggableRubric
        ) => (
          <Row
            provided={provided}
            data={board.rows[rubric.source.index]}
            board={board}
            index={index}
            rowKey={rowKey}
            renderRow={renderRow}
            setItemSize={setItemSize}
            isDragging={snapshot.isDragging}
            style={style}
          />
        )}
      >
        {(
          droppableProvided: DroppableProvided,
          snapshot: DroppableStateSnapshot
        ) => {
          return (
            <div
              className={classNames(preCls, className)}
              style={style}
              {...droppableProvided.droppableProps}
            >
              {renderColumn({
                data: board,
                index,
                renderRows: renderRows({ droppableProvided }),
              })}
            </div>
          )
        }}
      </Droppable>
    )
  }
)

export default Column
