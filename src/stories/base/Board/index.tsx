import React, { FC, useCallback, useMemo } from 'react'
import classNames from 'classnames'
import { DragDropContext, Draggable, DraggableLocation, Droppable, DropResult, OnDragEndResponder, ResponderProvided } from 'react-beautiful-dnd'
import './index.scss'
import { Column } from './column'
import Scroll from '../Scroll'
import { move } from '@/utils/array'

export interface BoardItem<Data = any> {
  rows: Data[]
  [key: string]: any
}

export type RenderRows = (option: { height: number, width?: number }) => React.ReactNode
export type RenderColumn<Data = any> = (props: {
  data: BoardItem<Data>
  index: number
  renderRows: RenderRows
}) => React.ReactNode
export type RenderRow<Data = any> = (props: { data: Data; board: BoardItem<Data>, index: number }) => React.ReactNode
export type OnDragEnd<Data = any> = (newBoards: BoardItem<Data>[], result: DropResult & { moveItem: Data, sourceBoard: BoardItem<Data>, destinationBoard: BoardItem<Data> }, provided: ResponderProvided) => void

export interface IBoardProps<Data = any> {
  onDragEnd: OnDragEnd
  renderColumn: RenderColumn<Data>
  renderRow: RenderRow<Data>
  boards: BoardItem<Data>[]
  columnKey: string
  rowKey: string
  itemHeight?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-board'

export const Board: FC<IBoardProps> = React.memo(
  ({
    boards,
    onDragEnd,
    renderColumn,
    renderRow,
    columnKey,
    rowKey,
    itemHeight,
    className,
    style,
  }) => {
    const onDragEndHandler = useCallback((result: DropResult, provided: ResponderProvided) => {
      if (!result.destination) {
        return;
      }
    
      const { source, destination, type } = result
    
      // 如果是列的拖拽
      if (type === 'COLUMN') {
        const reorderedColumns = move(boards, source.index, destination.index)
        // @ts-ignore
        if (onDragEnd) onDragEnd(reorderedColumns, { ...result }, provided)
        return;
      }
    
      // 如果是行的拖拽
      if (
        source.droppableId === destination.droppableId &&
        source.index === destination.index
      ) {
        return;
      }

      const sourceColumnIndex = boards.findIndex(column => column[columnKey] == source.droppableId);
      const destinationColumnIndex = boards.findIndex(column => column[columnKey] == destination.droppableId);

      if (sourceColumnIndex === -1 || destinationColumnIndex === -1) {
        console.error('列匹配失败,请检查 key 是否一致')
        console.error('boards', boards)
        console.error('source.droppableId', source.droppableId)
        console.error('destination.droppableId', destination.droppableId)
        return
      }

      const moveItem = boards[sourceColumnIndex].rows[source.index]
      const sourceBoard = boards[sourceColumnIndex]
      const destinationBoard = boards[destinationColumnIndex]

      // 如果是在同一列移动
      if (sourceColumnIndex === destinationColumnIndex) {
        const column = boards[sourceColumnIndex]
        column.rows = move(column?.rows, source.index, destination.index)
        boards[sourceColumnIndex] = column
      } else {
        const sourceColumn = boards[sourceColumnIndex];
        const destinationColumn = boards[destinationColumnIndex];
  
        destinationColumn?.rows.splice(destination.index, 0, sourceColumn?.rows[source.index]);
        sourceColumn?.rows.splice(source.index, 1);
  
        boards[sourceColumnIndex].rows = [...(sourceColumn?.rows || [])];
        boards[destinationColumnIndex].rows = [...(destinationColumn?.rows || [])];
      }

      const newBoards = [...boards];

      if (onDragEnd) onDragEnd(newBoards, {
        ...result,
        moveItem,
        sourceBoard,
        destinationBoard,
      }, provided)

    }, [boards, onDragEnd])
    return (
      <DragDropContext onDragEnd={onDragEndHandler}>
        <Droppable droppableId='board' type='COLUMN' direction='horizontal'>
          {(provided) => (
            <div
              className={classNames(preCls, className)}
              style={style}
              ref={provided.innerRef}
              {...provided.droppableProps}
            >
              {boards?.map((board, index) => (
                <Draggable
                  key={board[columnKey]}
                  draggableId={String(board[columnKey])}
                  index={index}
                >
                  {(provided) => (
                    <div
                      className='tita-ui-board-item'
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                    >
                      <Column
                        board={board}
                        columnKey={columnKey}
                        rowKey={rowKey}
                        renderColumn={renderColumn}
                        renderRow={renderRow}
                        index={index}
                        itemHeight={itemHeight}
                      />
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    )
  }
)

export default Board
