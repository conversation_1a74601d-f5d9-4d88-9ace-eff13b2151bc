import React, { FC, useCallback, useMemo, useState } from 'react'
import Board, { BoardItem, RenderRows } from '@/stories/base/Board'
import Button from '../../Button'
import { DraggableLocation, DropResult } from 'react-beautiful-dnd'

export interface IBaseProps { }

const arrayMock = (length: number, create: (idx: number) => any) =>
  new Array(length).fill(0).map((_, i) => create(i))

const mockData = arrayMock(20, (boardIdx) => ({
  id: Math.random().toString().slice(9),
  title: `board-${boardIdx}`,
  rows: arrayMock(1000, (rowIdx) => ({
    id: Math.random().toString().slice(9),
    title: `Todo ${boardIdx}-${rowIdx}`,
  })),
}))

const Base: FC<IBaseProps> = ({ }) => {
  const [boards, setBoards] = useState(() => mockData)

  const onDragEndHandler = useCallback((newBoards: any[]) => {
    setBoards(newBoards)
  }, [])

  const renderColumn = useCallback(
    ({
      data,
      renderRows,
    }: {
      data: BoardItem<{
        id: string
        title: string
      }>
      index: number
      renderRows: RenderRows
    }) => {
      return (
        <div className='p-10px shadow-lg rounded-md w-[300px]'>
          <h3>{data.title}</h3>
          <div className='mt-10px'>{renderRows({ height: 400 })}</div>
        </div>
      )
    },
    []
  )

  const renderRow = useCallback(
    ({
      data,
    }: {
      data: {
        id: string
        title: string
      }
      index: number
    }) => {
      return (
        <div className="pb-10px">
          <div className='border rounded-lg p-10px bg-white'>
            <Button>{data.title}</Button>
          </div>
        </div>
      )
    },
    []
  )

  return (
    <Board
      boards={boards}
      onDragEnd={onDragEndHandler}
      className='space-x-10px'
      rowKey='id'
      columnKey='id'
      renderColumn={renderColumn}
      renderRow={renderRow}
    />
  )
}

export default React.memo(Base)
