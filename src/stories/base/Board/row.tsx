import React, { FC, useEffect, useRef } from 'react'
import classNames from 'classnames'
import { BoardItem, IBoardProps } from '.'
import { DraggableProvided } from 'react-beautiful-dnd'
import './index.scss'
import { useSize } from 'ahooks'

export interface IRowProps<Data = any> {
  data: Data
  provided: DraggableProvided
  board: BoardItem<Data>
  renderRow: IBoardProps['renderRow']
  setItemSize: (index: number, size: number) => void
  index: number
  rowKey: string
  isDragging: boolean
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-row'

function getStyle(provided: DraggableProvided, style?: React.CSSProperties) {
  if (!style) {
    return provided.draggableProps.style
  }

  return {
    ...provided.draggableProps.style,
    ...style,
  }
}

export const Row: FC<IRowProps> = React.memo(
  ({
    data,
    index,
    board,
    renderRow,
    provided,
    setItemSize,
    isDragging,
    rowKey,
    className,
    style,
  }) => {
    const contentRef = useRef() as React.MutableRefObject<HTMLDivElement>;

    const { height } = useSize(contentRef) || { height: 0 }
  
    useEffect(() => {
      if (setItemSize) setItemSize(index, height);
    }, [height]);

    return (
      <div
        className={classNames(preCls, className)}
        ref={provided.innerRef}
        {...provided.draggableProps}
        {...provided.dragHandleProps}
        style={getStyle(provided, style)}
        data-is-dragging={isDragging}
        data-testid={data[rowKey]}
        data-index={index}
      >
        <div ref={contentRef}>
          {renderRow({ data, board, index })}
        </div>
      </div>
    )
  }
)

export default Row
