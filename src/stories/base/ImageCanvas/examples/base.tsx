import React, { useRef } from 'react'
import { ImageCanvas, ImageCanvasRef } from '../index'
import { Button, Space } from 'antd'

const BaseExample = () => {
  const imageRef = useRef<ImageCanvasRef>(null)

  // 示例图片 URL
  const imageUrl = 'https://picsum.photos/800/600'

  const handleZoomIn = () => {
    const currentScale = imageRef.current?.getScale?.() || 1
    imageRef.current?.setScale(currentScale + 0.5)
  }

  const handleZoomOut = () => {
    const currentScale = imageRef.current?.getScale?.() || 1
    imageRef.current?.setScale(currentScale - 0.5)
  }

  const handleReset = () => {
    imageRef.current?.reset()
  }

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button onClick={handleZoomIn}>放大</Button>
          <Button onClick={handleZoomOut}>缩小</Button>
          <Button onClick={handleReset}>重置</Button>
        </Space>
      </div>
      
      <div style={{ width: '800px', height: '500px', border: '1px solid #ddd' }}>
        <ImageCanvas
          ref={imageRef}
          src={imageUrl}
          minScale={0.1}
          maxScale={5}
          initialScale={1}
        />
      </div>
      
      <div style={{ marginTop: 16 }}>
        <p>操作说明：</p>
        <ul>
          <li>拖拽：直接用鼠标拖拽图片</li>
          <li>缩放：使用触摸板双指缩放或鼠标滚轮</li>
          <li>移动端：支持双指缩放和拖拽</li>
        </ul>
      </div>
    </div>
  )
}

export default BaseExample
