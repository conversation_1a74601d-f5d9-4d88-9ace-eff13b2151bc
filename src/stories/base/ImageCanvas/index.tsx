import React, { FC, useRef, useImperativeHandle, forwardRef, useState, useEffect } from 'react'
import classNames from 'classnames'
import { useSpring, animated } from '@react-spring/web'
import { useGesture } from '@use-gesture/react'
import './index.scss'

export interface IImageCanvasProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  src?: string
  // 最小缩放比例
  minScale?: number
  // 最大缩放比例
  maxScale?: number
  // 初始缩放比例
  initialScale?: number
}

export interface ImageCanvasRef {
  // 获取当前缩放值
  getScale: () => number
  // 设置缩放
  setScale: (scale: number) => void
  // 设置位置
  setPosition: (x: number, y: number) => void
  // 重置位置和缩放
  reset: () => void
}

const preCls = 'tita-ui-image-canvas'

export const ImageCanvas = forwardRef<ImageCanvasRef, IImageCanvasProps>(({
  className,
  style,
  src,
  minScale = 0.5,
  maxScale = 3,
  initialScale = 1
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const imgRef = useRef<HTMLImageElement>(null)
  const [imageLoaded, setImageLoaded] = useState(false)

  const [{ scale, x, y }, api] = useSpring(() => ({
    scale: initialScale,
    x: 0,
    y: 0,
    config: { tension: 300 }
  }))

  // 初始化图片自适应缩放
  useEffect(() => {
    if (imageLoaded && containerRef.current && imgRef.current) {
      const container = containerRef.current
      const img = imgRef.current
      const containerRatio = container.clientWidth / container.clientHeight
      const imageRatio = img.naturalWidth / img.naturalHeight
      
      let initialScale = 1
      if (containerRatio > imageRatio) {
        initialScale = container.clientHeight / img.naturalHeight
      } else {
        initialScale = container.clientWidth / img.naturalWidth
      }
      
      api.start({ scale: initialScale, x: 0, y: 0 })
    }
  }, [imageLoaded])

  // 修改手势绑定
  const bindGestures = useGesture(
    {
      onDrag: ({ offset: [dx, dy], event }) => {
        event.preventDefault()
        api.start({ x: dx, y: dy })
      },
      onWheel: ({ delta: [, dy], event }) => {
        event.preventDefault()
        const currentScale = scale.get()
        const newScale = Math.min(Math.max(currentScale - dy * 0.01, minScale), maxScale)
        api.start({ scale: newScale })
      },
      onPinch: ({ 
        offset: [d],
        event 
      }) => {
        event.preventDefault()
        const newScale = Math.min(Math.max(d, minScale), maxScale)
        api.start({ scale: newScale })
      }
    },
    {
      drag: {
        from: () => [x.get(), y.get()],
        bounds: { left: -500, right: 500, top: -500, bottom: 500 },
        rubberband: true
      },
      pinch: {
        scaleBounds: { min: minScale, max: maxScale },
        rubberband: true
      },
      wheel: {
        enabled: true
      }
    }
  )

  // 暴露外部方法
  useImperativeHandle(ref, () => ({
    getScale: () => scale.get(),
    setScale: (newScale: number) => {
      api.start({ scale: Math.min(Math.max(newScale, minScale), maxScale) })
    },
    setPosition: (newX: number, newY: number) => {
      api.start({ x: newX, y: newY })
    },
    reset: () => {
      api.start({ scale: initialScale, x: 0, y: 0 })
    }
  }))

  const preventDefault = (e: Event) => {
    e.preventDefault()
  }

  // 添加鼠标进入/离开处理
  const handleMouseEnter = () => {
    document.addEventListener('wheel', preventDefault, { passive: false })
    document.addEventListener('touchmove', preventDefault, { passive: false })
    document.addEventListener('gesturestart', preventDefault)
    document.addEventListener('gesturechange', preventDefault)
  }

  const handleMouseLeave = () => {
    document.removeEventListener('wheel', preventDefault)
    document.removeEventListener('touchmove', preventDefault)
    document.removeEventListener('gesturestart', preventDefault)
    document.removeEventListener('gesturechange', preventDefault)
  }

  return (
    <div 
      ref={containerRef}
      className={classNames(preCls, className)} 
      {...bindGestures()}
      style={style}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <animated.div
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          touchAction: 'none',
          userSelect: 'none'
        }}
      >
        <animated.img
          ref={imgRef}
          src={src}
          style={{
            transform: scale.to(s => `scale(${s})`),
            translateX: x,
            translateY: y,
            maxWidth: '100%',
            maxHeight: '100%',
            touchAction: 'none',
            userSelect: 'none'
          }}
          onLoad={() => setImageLoaded(true)}
          draggable={false}
        />
      </animated.div>
    </div>
  )
})

ImageCanvas.displayName = 'ImageCanvas'

export default ImageCanvas
