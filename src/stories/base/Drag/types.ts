// @flow
import { DraggableId, DraggableLocation } from 'react-beautiful-dnd'

export type Id = string

export type AuthorColors = {
  soft: string
  hard: string
}

export type Author = {
  id: Id
  name: string
  avatarUrl: string
  url: string
  colors: AuthorColors
}

export enum ComplateStatus {
  NotStarted, // 未开始
  InProgress, // 进行中
  Complated, // 已完成
  Deferred, // 已延期
  Paused, // 暂停中
  Unaccepted, // 未接受
  Cancelled, // 已取消
}

export type Quote = {
  id: Id
  content: string
  author: Author
  status: ComplateStatus
}

export type Dragging = {
  id: DraggableId
  location: DraggableLocation
}

export type QuoteMap = {
  [key: string]: Quote[]
}

export type Task = {
  id: Id
  content: string
}
