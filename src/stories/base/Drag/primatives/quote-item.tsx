// @flow
import React from 'react'
import type { Quote, AuthorColors, ComplateStatus } from '../types'
import type { DraggableProvided } from 'react-beautiful-dnd'
import Progress from '../../Progress'

import './quote-item.scss'
import classNames from 'classnames'

type Props = {
  quote: Quote
  isDragging: boolean
  provided: DraggableProvided
  isClone?: boolean
  isGroupedOver?: boolean
  style?: Object
  index?: number
}

const STATUSMAP = {
  0: {
    name: 'NotStarted',
    color: '',
    description: '未开始',
    icon: 'finished',
  },
  1: {
    name: 'InProgress',
    color: 'rgba(40,121,255,0.16)',
    description: '进行中',
    icon: 'running',
  },
  2: {
    name: 'Complated',
    color: 'rgba(0,214,132,0.16)',
    description: '已完成',
    icon: 'finished',
  },
  3: {
    name: 'Deferred',
    color: '',
    description: '已延期',
    icon: 'paused',
  },
  4: {
    name: 'Paused',
    color: 'rgba(240,163,38,0.16)',
    description: '暂停中',
    icon: 'paused',
  },
  5: {
    name: 'Unaccepted',
    color: '',
    description: '未接受',
    icon: 'paused',
  },
  6: {
    name: 'Cancelled',
    color: '',
    description: '已取消',
    icon: 'paused',
  },
}

// 获取各种状态对应的背景色
const getCardStatusColor = (status: ComplateStatus) => {
  return STATUSMAP[status]?.color
}

function getStyle(provided: DraggableProvided, style: any) {
  if (!style) {
    return provided.draggableProps.style
  }

  return {
    ...provided.draggableProps.style,
    ...style,
  }
}

// Previously this extended React.Component
// That was a good thing, because using React.PureComponent can hide
// issues with the selectors. However, moving it over does can considerable
// performance improvements when reordering big lists (400ms => 200ms)
// Need to be super sure we are not relying on PureComponent here for
// things we should be doing in the selector as we do not know if consumers
// will be using PureComponent
const preCls = 'titaui--drag-list-item'

function QuoteItem(props: Props) {
  const { quote, isDragging, isGroupedOver, provided, style, isClone, index } =
    props

  return (
    <div
      className={`${preCls}-container`}
      // href='#'
      // isDragging={isDragging}
      // isGroupedOver={isGroupedOver}
      // isClone={isClone}
      // colors={quote.author.colors}
      ref={provided.innerRef}
      {...provided.draggableProps}
      {...provided.dragHandleProps}
      style={getStyle(provided, style)}
      // data-is-dragging={isDragging}
      // data-testid={quote.id}
      // data-index={index}
      // aria-label={`${quote.author.name} quote ${quote.content}`}
    >
      <div className={`${preCls}-header`}>
        <div
          className={`${preCls}-status`}
          style={{ backgroundColor: getCardStatusColor(quote.status) }}
        >
          <i
            className={classNames(
              `tu-icon-${STATUSMAP[quote.status]?.icon}`,
              `${preCls}-status-icon`
            )}
          />
          <span>{STATUSMAP[quote?.status]?.description}</span>
        </div>
        <img
          className={`${preCls}-avatar`}
          src={quote.author.avatarUrl}
          alt={quote.author.name}
        />
      </div>

      {isClone ? <div className={`${preCls}-clone-badge`}>Clone</div> : null}
      <div className={`${preCls}-content`}>
        <div className={`${preCls}-block-quote`}>{quote.content}</div>
      </div>
      <div className={`${preCls}-footer`}>
        <div className={`${preCls}-task`}>
          <i
            className={classNames(
              'tu-icon-task',
              'titaui--drag-list-item-task-icon'
            )}
          />
          <span>
            {quote.completedTask ?? 0}/{quote.allTask ?? 0}
          </span>
        </div>
        <div className={`${preCls}-date`}>
          <i
            className={classNames('tu-icon-calendar', `${preCls}-date-icon`)}
          />
          <span>过期三天</span>
        </div>
        <div className={`${preCls}-completeness`}>
          <Progress
            width={14}
            strokeWidth={14}
            trailWidth={14}
            type='circle'
            percent={50}
            showInfo={false}
          >
            {' '}
          </Progress>
          <span className={`${preCls}-percent`}>50%</span>
        </div>
      </div>
    </div>
  )
}

export default React.memo<Props>(QuoteItem)
