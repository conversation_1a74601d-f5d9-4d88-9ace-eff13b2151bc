.titaui--drag-list-wrapper {
  display: flex;
  flex-direction: column;
  padding: 8px;
  border: 8px;
  padding-bottom: 0;
  transition: background-color 0.2s ease, opacity 0.1s ease;
  user-select: none;
  // border-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  padding: 12px 16px 14px 16px;
  .titaui--drag-list-create-button {
    width: 272px;
    height: 44px;
    border-radius: 8px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2879ff;
    cursor: pointer;
    font-size: 14px;
    margin-bottom: 10px;
    .titaui--drag-list-add-icon {
      width: 14px;
      height: 14px;
      margin-right: 3px;
    }
  }
  .titaui--drag-list-scroll-container {
    overflow-x: hidden;
    overflow-y: auto;
    // max-height: 250px;
    .titaui--drag-list-inner-list-title {
      padding: 8px;
      transition: background-color ease 0.2s;
      flex-grow: 1;
      user-select: none;
      position: relative;
      &:focus {
        outline: 2px solid #998dd9;
        outline-offset: 2px;
      }
    }
  }
  .titaui--drag-list-drop-zone {
    padding-bottom: 8px;
    min-height: 250px;
  }
}
