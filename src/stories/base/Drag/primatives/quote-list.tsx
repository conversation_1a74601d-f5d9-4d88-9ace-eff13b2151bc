// @ts-nocheck
import React from 'react'
import { Droppable, Draggable } from 'react-beautiful-dnd'
import classNames from 'classnames'
import QuoteItem from './quote-item'
import type { Quote } from '../types'
import type {
  DroppableProvided,
  DroppableStateSnapshot,
  DraggableProvided,
  DraggableStateSnapshot,
} from 'react-beautiful-dnd'

import CreatTask from './CreateTask/index'

import './quote-list.scss'

export const getBackgroundColor = (
  isDraggingOver: boolean,
  isDraggingFrom: boolean
): string => {
  // if (isDraggingOver) {
  //   return '#F0F4FA'
  // }
  // if (isDraggingFrom) {
  //   return '#E6FCFF'
  // }
  return '#F0F4FA'
}

type Props = {
  listId?: string
  listType?: string
  quotes: Quote[]
  title?: string
  internalScroll?: boolean
  scrollContainerStyle?: Object
  isDropDisabled?: boolean
  isCombineEnabled?: boolean
  style?: Object
  // may not be provided - and might be null
  ignoreContainerClipping?: boolean
  onClickCreate?: () => void
  useClone?: boolean
}

type QuoteListProps = {
  quotes: Quote[]
}

const preCls = 'titaui--drag-list'

const InnerQuoteList = React.memo(function InnerQuoteList(
  props: QuoteListProps
) {
  return props.quotes.map((quote: Quote, index: number) => (
    <Draggable key={quote.id} draggableId={quote.id} index={index}>
      {(
        dragProvided: DraggableProvided,
        dragSnapshot: DraggableStateSnapshot
      ) => (
        <QuoteItem
          key={quote.id}
          quote={quote}
          isDragging={dragSnapshot.isDragging}
          isGroupedOver={Boolean(dragSnapshot.combineTargetFor)}
          provided={dragProvided}
        />
      )}
    </Draggable>
  ))
})

type InnerListProps = {
  dropProvided: DroppableProvided
  quotes: Quote[]
  title: string
}

function InnerList(props: InnerListProps) {
  const { quotes, dropProvided } = props
  const title = props.title ? (
    <h4 className={`${preCls}-inner-list-title`}>{props.title}</h4>
  ) : null

  return (
    <div>
      {title}
      <div className={`${preCls}-drop-zone`} ref={dropProvided.innerRef}>
        <InnerQuoteList quotes={quotes} />
        {dropProvided.placeholder}
      </div>
    </div>
  )
}

export default function QuoteList(props: Props) {
  const {
    ignoreContainerClipping,
    internalScroll,
    scrollContainerStyle,
    isDropDisabled,
    isCombineEnabled,
    listId = 'LIST',
    listType,
    style,
    quotes,
    title,
    useClone,
    onClickCreate,
  } = props

  return (
    <Droppable
      droppableId={listId}
      type={listType}
      ignoreContainerClipping={ignoreContainerClipping}
      isDropDisabled={isDropDisabled}
      isCombineEnabled={isCombineEnabled}
      renderClone={
        useClone
          ? (provided, snapshot, descriptor) => (
              <QuoteItem
                quote={quotes[descriptor.source.index]}
                provided={provided}
                isDragging={snapshot.isDragging}
                isClone
              />
            )
          : null
      }
    >
      {(
        dropProvided: DroppableProvided,
        dropSnapshot: DroppableStateSnapshot
      ) => (
        <div
          className={`${preCls}-wrapper`}
          style={{
            ...style,
            backgroundColor: getBackgroundColor(
              dropSnapshot.isDraggingOver,
              Boolean(dropSnapshot.draggingFromThisWith)
            ),
            opacity: isDropDisabled ? 0.5 : 'inherit',
          }}
          // isDraggingOver={dropSnapshot.isDraggingOver}
          // isDropDisabled={isDropDisabled}
          // isDraggingFrom={Boolean(dropSnapshot.draggingFromThisWith)}
          {...dropProvided.droppableProps}
        >
          <div className={`${preCls}-create-button`} onClick={onClickCreate}>
            <i className={classNames('tu-icon-add1', `${preCls}-add-icon`)} />
            <span>创建任务</span>
          </div>
          {internalScroll ? (
            <div
              className={`${preCls}-scroll-container`}
              style={scrollContainerStyle}
            >
              <InnerList
                quotes={quotes}
                title={title}
                dropProvided={dropProvided}
              />
            </div>
          ) : (
            <InnerList
              quotes={quotes}
              title={title}
              dropProvided={dropProvided}
            />
          )}
        </div>
      )}
    </Droppable>
  )
}
