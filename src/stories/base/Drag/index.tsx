// @flow
import React, { Component } from 'react'
import type {
  DropResult,
  DraggableLocation,
  DroppableProvided,
} from 'react-beautiful-dnd'
import type { QuoteMap, Quote } from './types'
import Column from './column'
import reorder, { reorderQuoteMap } from './reorder'
import { DragDropContext, Droppable } from 'react-beautiful-dnd'
import classNames from 'classnames'

// import './index.scss'
interface IOnDrage {
  (data: QuoteMap): QuoteMap
}

type Props = {
  initial: QuoteMap
  withScrollableColumns?: boolean
  isCombineEnabled?: boolean
  containerHeight?: string
  useClone?: boolean

  className?: string
  onDragEnd?: IOnDrage
  onClickCreate?: (index: number) => void
}

type State = {
  columns: QuoteMap
  ordered: string[]
}

const preCls = 'titaui--drag'

export class Drag extends Component<Props, State> {
  /* eslint-disable react/sort-comp */
  static defaultProps = {
    isCombineEnabled: false,
  }

  state: State = {
    columns: this.props.initial,
    ordered: Object.keys(this.props.initial),
  }

  onDragEnd = (result: DropResult) => {
    if (result.combine) {
      if (result.type === 'COLUMN') {
        const shallow: string[] = [...this.state.ordered]
        shallow.splice(result.source.index, 1)
        this.setState({ ordered: shallow })
        return
      }

      const column: Quote[] = this.state.columns[result.source.droppableId]
      const withQuoteRemoved: Quote[] = [...column]
      withQuoteRemoved.splice(result.source.index, 1)
      const columns: QuoteMap = {
        ...this.state.columns,
        [result.source.droppableId]: withQuoteRemoved,
      }
      this.setState({ columns })
      return
    }

    // dropped nowhere
    if (!result.destination) {
      return
    }

    const source: DraggableLocation = result.source
    const destination: DraggableLocation = result.destination

    // did not move anywhere - can bail early
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return
    }

    // reordering column
    if (result.type === 'COLUMN') {
      const ordered: string[] = reorder(
        this.state.ordered,
        source.index,
        destination.index
      )

      this.setState({
        ordered,
      })

      return
    }

    const data = reorderQuoteMap({
      quoteMap: this.state.columns,
      source,
      destination,
    })

    this.setState({
      columns: data.quoteMap,
    })

    this.props.onDragEnd && this.props.onDragEnd(data.quoteMap)
  }

  onClickCreate(index: number) {
    this.props.onClickCreate?.(index)
  }

  render() {
    const columns: QuoteMap = this.state.columns
    const ordered: string[] = this.state.ordered
    const {
      containerHeight,
      useClone,
      isCombineEnabled,
      withScrollableColumns,
    } = this.props

    const board = (
      <Droppable
        droppableId='board'
        type='COLUMN'
        // style={{ backgroundColor: 'red' }}
        direction='horizontal'
        ignoreContainerClipping={Boolean(containerHeight)}
        isCombineEnabled={isCombineEnabled}
      >
        {(provided: DroppableProvided) => (
          <div
            className={`${preCls}-container`}
            ref={provided.innerRef}
            {...provided.droppableProps}
          >
            {ordered.map((key: string, index: number) => (
              <Column
                key={key}
                index={index}
                title={key}
                quotes={columns[key]}
                isScrollable={withScrollableColumns}
                isCombineEnabled={isCombineEnabled}
                useClone={useClone}
                onClickCreate={this.onClickCreate}
              />
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    )

    return null

    return (
      <React.Fragment>
        <div className={classNames(preCls, this.props.className)}>
          <DragDropContext onDragEnd={this.onDragEnd}>
            {containerHeight ? (
              <div
                className={`${preCls}-parent-container`}
                style={{ height: containerHeight }}
              >
                {board}
              </div>
            ) : (
              board
            )}
          </DragDropContext>
        </div>
      </React.Fragment>
    )
  }
}

export default Drag
