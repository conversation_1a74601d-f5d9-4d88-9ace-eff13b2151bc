// @flow
import { Component } from 'react'
import { Draggable } from 'react-beautiful-dnd'
import type {
  DraggableProvided,
  DraggableStateSnapshot,
} from 'react-beautiful-dnd'
import QuoteList from './primatives/quote-list'
import type { Quote } from './types'
import classNames from 'classnames'

import './column.scss'

type Props = {
  title: string
  quotes: Quote[]
  index: number
  isScrollable?: boolean
  isCombineEnabled?: boolean
  useClone?: boolean
  onClickCreate?: (index: number) => void
}

export default class InitQuoteColumn extends Component<Props> {
  onClickCreate(index: number) {
    this.props.onClickCreate?.(index)
  }
  render() {
    const title: string = this.props.title
    const quotes: Quote[] = this.props.quotes
    const index: number = this.props.index
    return (
      <Draggable draggableId={title} index={index}>
        {(provided: DraggableProvided, snapshot: DraggableStateSnapshot) => (
          <div
            className={classNames('tui--drag-column-container')}
            ref={provided.innerRef}
            {...provided.draggableProps}
          >
            {/* <div> 创建任务</div> */}
            <div isDragging={snapshot.isDragging}>
              <div
                isDragging={snapshot.isDragging}
                {...provided.dragHandleProps}
                aria-label={`${title} quote list`}
              >
                {title}
              </div>
            </div>
            <QuoteList
              listId={title}
              listType='QUOTE'
              style={{
                backgroundColor: snapshot.isDragging ? '#E3FCEF' : null,
              }}
              quotes={quotes}
              onClickCreate={() => {
                this.onClickCreate(index)
              }}
              internalScroll={this.props.isScrollable}
              isCombineEnabled={Boolean(this.props.isCombineEnabled)}
              useClone={Boolean(this.props.useClone)}
            />
          </div>
        )}
      </Draggable>
    )
  }
}
