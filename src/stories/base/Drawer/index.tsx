import { getLocale } from '@tita/utils'
import classnames from 'classnames'
import RcDrawer from 'rc-drawer'
import 'rc-drawer/assets/index.css'
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from 'react'
import DrawerManagerContext from './drawer-manager/context'
import HeaderAction, { IHeaderActionProps } from './headerAction'
import './index.scss'
import { hasAncestorElement } from './utils/dom'

type getContainerFunc = () => HTMLElement
interface IDrawerProps {
  destroyOnClose?: boolean
  getContainer?: string | HTMLElement | getContainerFunc
  maskClosable?: boolean
  mask?: boolean
  maskStyle?: React.CSSProperties
  style?: React.CSSProperties
  drawerStyle?: React.CSSProperties
  bodyStyle?: React.CSSProperties
  contentWrapperStyle?: React.CSSProperties
  visible: boolean
  width?: number | string
  height?: number | string
  zIndex?: number
  prefixCls?: string
  placement?: 'top' | 'right' | 'bottom' | 'left'
  onClose: () => void
  afterVisibleChange?: (visible: boolean) => void
  className?: string
  keyboard?: boolean
  autoClosable?: boolean
  children?: React.ReactNode
  /** 使用透明遮罩
   * @default false
   * @description mask 为 true 时生效
   */
  useTransparentMask?: boolean
}

interface IDrawerHeaderProps {
  closable?: boolean
  onClose?: Function
  actions?: IHeaderActionProps[]
  actionMaxCount?: number
  content?: any
  style?: React.CSSProperties
  approvalSetting?: any
  detailInfo?: any
}
interface IDrawer extends React.FC<IDrawerProps> {
  Header: (props: IDrawerHeaderProps) => React.ReactElement | null
}

const DrawerContext = React.createContext<any | null>(null)

export const Drawer: IDrawer = (props) => {
  const rcRef = useRef<any>()
  // const destroy = useRef(false)
  // const forceUpdate = useForceUpdate()
  const {
    visible,
    placement = 'right',
    children,
    mask = false,
    width,
    className,
    contentWrapperStyle,
    onClose,
    style,
    afterVisibleChange,
    destroyOnClose = true,
    bodyStyle,
    zIndex = 1200,
    autoClosable = false,
    useTransparentMask,
    ...rest
  } = props

  const handleAfterVisibleChange = useCallback((opened: boolean) => {
    afterVisibleChange && afterVisibleChange(opened)
    // if (!opened) {
    //   if (destroyOnClose) {
    //     destroy.current = true
    //     forceUpdate()
    //   }
    // }
  }, [])

  function renderContent() {
    if (destroyOnClose && !visible) return null
    // destroy.current = false
    return children
  }

  const autoClose = (e: any) => {
    if (
      hasAncestorElement(
        e.target,
        'document-mouse-event-ignore,rc-tooltip,titaui-popup,rc-dialog-root,common-modal,in-modal,task-hot-add,addClassName,modal,titaui-mblog,action__right'
      )
    ) {
      return
    }
    onClose()
  }

  useEffect(() => {
    if (autoClosable) {
      document.addEventListener('mousedown', autoClose)
      // document.addEventListener('click', autoClose)
    }
    return () => {
      document.removeEventListener('mousedown', autoClose)
      // document.removeEventListener('click', autoClose)
    }
  }, [])

  const _width = useMemo(() => {
    if (width) return width

    if (placement === 'top' || placement === 'bottom') {
      return '100%'
    }

    return 820
  }, [width])

  const renderDrawer = () => {
    return (
      <RcDrawer
        className={classnames(
          'tita-ui--drawer',
          'document-mouse-event-ignore',
          className,
          {
            'transparent-mask': mask && useTransparentMask,
          }
        )}
        style={{ ...style, zIndex }}
        {...rest}
        // @ts-ignore
        title={undefined}
        afterVisibleChange={handleAfterVisibleChange}
        onClose={onClose}
        {...{
          open: visible,
          level: null,
          placement,
          showMask: mask,
          handler: false,
          width: _width,
          ref: rcRef,
          contentWrapperStyle,
        }}
      >
        {renderContent()}
      </RcDrawer>
    )
  }

  return (
    <DrawerContext.Provider
      value={{
        close: () => {
          onClose()
        },
      }}
    >
      {renderDrawer()}
    </DrawerContext.Provider>
  )
}
interface IDrawerHeaderProps {
  closable?: boolean
  noFooterLine?: boolean
  className?: string
  onClose?: Function
  actions?: IHeaderActionProps[]
  actionComponent?: any
  onlyShowContent?: any
  actionMaxCount?: number
  content?: any
  style?: React.CSSProperties
}

function DrawerHeader({
  closable = true,
  actions,
  actionMaxCount = 4,
  content,
  style,
  className,
  onClose,
  actionComponent,
  onlyShowContent,
  approvalSetting,
  detailInfo,
  noFooterLine,
}: IDrawerHeaderProps) {
  const drawer = useContext(DrawerContext)
  const manager = useContext(DrawerManagerContext)
  // @ts-ignore
  const showClose = closable && (!!onClose || !!drawer || !!window.drawerClose)
  if (!showClose && !(actions && actions.length) && !content) return null

  let displayActions = actions || []
  let collapsedActions: any[] = []

  if (actions && actions.length > actionMaxCount) {
    displayActions = actions.slice(0, actionMaxCount - 1)
    collapsedActions = actions.slice(actionMaxCount - 1)
  }

  const showBackBtn = !!manager && manager.getCanBack()
  const hasContent = !!content

  return (
    <div
      className={classnames('tita-ui--drawer-header', className, {
        'tita-ui--drawer-header--noFooterLine': noFooterLine,
      })}
      style={style}
    >
      {showBackBtn && (
        <a
          className='tita-ui--drawer-header-back'
          onClick={manager && manager.goBack}
        >
          <i className='tu-icon-fanhui' />
          {getLocale('Mod_Back')}
        </a>
      )}
      {showBackBtn && hasContent && (
        <div className='tita-ui--drawer-header-divider' />
      )}
      {hasContent && content}
      <div style={{ flex: 1 }} />
      <div>{onlyShowContent}</div>
      <div>{actionComponent}</div>
      {displayActions.map((action) => (
        <HeaderAction {...action} />
      ))}
      {!!collapsedActions.length && (
        <HeaderAction
          approvalSetting={approvalSetting}
          detailInfo={detailInfo}
          title={getLocale('Mod_MoreOperate')}
          icon='tu-icon-10'
          children={collapsedActions}
        />
      )}
      {showClose && (
        <div
          className='tita-ui--drawer-header-close tu-icon-canceled'
          onClick={() => {
            // @ts-ignore
            drawer ? drawer.close() : window.drawerClose()
          }}
        />
      )}
    </div>
  )
}

Drawer.Header = DrawerHeader

export default Drawer
