import classNames from 'classnames'
import {
  forwardRef,
  useCallback,
  useContext,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'
import Drawer from '../index'
import DrawerManagerContext from './context'
import './index.scss'
import { sleep } from './utils'
import useForceUpdate from './utils/useForceUpdate'

export enum DrawerStatus {
  closed,
  opening,
  opened,
  closing,
}
interface IDrawerWrapperProps {
  getContainer?: Function
}
const prefixCls = 'tita-ui--drawer-wrapper'
const transDuration = 100

function DrawerWrapper({ getContainer }: IDrawerWrapperProps, ref: any) {
  const [visible, setVisible] = useState(false)
  const [child, setChild] = useState<any>(null)
  const [drawerProps, setDrawerProps] = useState<any>({})
  const status = useRef<DrawerStatus>(DrawerStatus.closed)
  const manager = useContext(DrawerManagerContext)
  const forceUpdate = useForceUpdate()
  const transitionStatus = useRef('actived')
  const displayInfirst = useRef(true)
  const next = useRef(null)
  const waiting = useRef<any>(null)

  const afterVisibleChange = useCallback(
    (visible: boolean) => {
      status.current = visible ? DrawerStatus.opened : DrawerStatus.closed
      drawerProps.afterVisibleChange && drawerProps.afterVisibleChange(visible)
    },
    [drawerProps]
  )

  const toggleChild = async (newChild: any, newDrawerProps: any) => {
    // 就推屏退场动画
    transitionStatus.current = 'leaving'
    next.current = newChild
    forceUpdate()
    await sleep(transDuration)
    // 新推屏由preload进入展示容器
    transitionStatus.current = 'leaved'
    // 交换预加载和展示区
    displayInfirst.current = !displayInfirst.current
    next.current = null
    setChild(newChild)
    if (drawerProps != newDrawerProps) {
      setDrawerProps({ ...newDrawerProps })
    }
    await sleep(0)
    // 新推屏入场动画
    transitionStatus.current = 'entering'
    forceUpdate()
    await sleep(transDuration)
    transitionStatus.current = 'actived'
    forceUpdate()
    // 如果等待位有推屏 由等待位入场
    if (waiting.current) {
      toggleChild(waiting.current.child, waiting.current.newDrawerProps)
      waiting.current = null
    }
  }
  useImperativeHandle(
    ref,
    () => ({
      open: (comp = null, newDrawerProps: any) => {
        if (status.current == DrawerStatus.closed) {
          status.current = DrawerStatus.opening
        }
        if (transitionStatus.current != 'actived') {
          // 如果动画过程中请求打开其他推屏 进入等待位 基本不会出现这种情况
          waiting.current = { child: comp, drawerProps: newDrawerProps }
        } else if (child && status.current == DrawerStatus.opened) {
          // 推屏切换动画
          toggleChild(comp, newDrawerProps)
        } else {
          setChild(comp)
          if (newDrawerProps != drawerProps) {
            setDrawerProps({ ...newDrawerProps })
          }
        }
        setVisible(true)
      },
      close: () => {
        if (status.current == DrawerStatus.opened) {
          status.current = DrawerStatus.closing
        }
        setVisible(false)
      },
      getStatus: () => status.current,
    }),
    [child]
  )

  const displayCls = classNames(
    `${prefixCls}-display`,
    `${prefixCls}-display__${transitionStatus.current}`,
    {
      [`${prefixCls}-display__actived`]: transitionStatus.current == 'leaving',
      [`${prefixCls}-display__leaved`]: transitionStatus.current == 'entering',
    }
  )
  const preloadCls = `${prefixCls}-preload`
  // const zIndex = useZindex()
  return (
    <Drawer
      {...drawerProps}
      visible={visible}
      onClose={() => manager && manager.close()}
      afterVisibleChange={afterVisibleChange}
      getContainer={getContainer}
    >
      <div className={prefixCls}>
        <div className={displayInfirst.current ? displayCls : preloadCls}>
          {displayInfirst.current ? child : next.current}
        </div>
        <div className={!displayInfirst.current ? displayCls : preloadCls}>
          {!displayInfirst.current ? child : next.current}
        </div>
      </div>
    </Drawer>
  )
}

export default forwardRef(DrawerWrapper)
