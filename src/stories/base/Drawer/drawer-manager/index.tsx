/* eslint-disable no-underscore-dangle */
import ReactDom from 'react-dom'
import DrawerManagerContext from './context'
import DrawerWrapper from './drawerWrapper'
import { hasAncestorElement } from './utils/dom'

class DrawerManager {
  private _drawer: any

  closeCallback: any

  el: any

  timer: any

  history: any[] = []

  uid = 0

  __drawer_map__: any = {}

  open = (type: any, props = {} as any, drawerProps = {}, backParams = {}) => {
    if (!type) throw new Error('need provide drawer type')
    const Component =
      // @ts-ignore
      typeof type === 'string' ? window.__drawer_map__[type] : type
    if (!Component) throw new Error('the type is not regisited')
    props.onClose && (this.closeCallback = props.onClose)
    return new Promise((resolve) => {
      clearTimeout(this.timer)

      this.timer = setTimeout(() => {
        const { uid } = this
        this.uid += 1
        let extendedDrawerProps = drawerProps
        if (this.history.length > 0) {
          extendedDrawerProps = {
            ...this.history[this.history.length - 1].extendedDrawerProps,
            ...extendedDrawerProps,
          }
        }
        if (props.isPush) {
          this.history.push({
            Component,
            props,
            extendedDrawerProps,
            resolve,
            key: uid,
            backParams: backParams,
          })
        } else {
          this.history = [
            {
              Component,
              props,
              extendedDrawerProps,
              resolve,
              key: uid,
              backParams: backParams,
            },
          ]
        }
        this.drawer?.open(
          <Component key={uid} {...props} />,
          extendedDrawerProps
        )
        if (this.history.length === 1) {
          document.addEventListener('mousedown', this.autoClose)
          // document.addEventListener("click", this.autoClose);
        }
      }, 200)
    })
  }

  goBack = (params?: any) => {
    if (this.history.length > 1) {
      const current = this.history.pop()
      current.resolve()
      const { Component, props, extendedDrawerProps, key } =
        this.history[this.history.length - 1]
      this.drawer?.open(
        <Component
          key={key}
          {...props}
          {...params}
          backParams={current.backParams}
        />,
        extendedDrawerProps
      )
    } else {
      // @ts-ignore
      this.close()
    }
  }

  close = (closeParams?: any) => {
    this.history.reverse().forEach(({ resolve }) => resolve())
    this.history = []
    this.drawer.close()
    // document.removeEventListener("click", this.autoClose);
    document.removeEventListener('mousedown', this.autoClose)
    this.closeCallback?.(closeParams)
  }

  private handleAutoClose = () => {
    // @ts-ignore
    window.titaObserver?.emit('DrawerManagerAutoClose')
    // @ts-ignore
    this.close()
  }

  private autoClose = (e: any) => {
    // if (!e.target.clientHeight) return;
    if (this.el && this.el.contains(e.target as any)) {
      if (e.target.classList.contains('tita-ui--drawer-header-close')) {
        this.handleAutoClose()
      }
      return
    }
    if (hasAncestorElement(e.target, 'titaui-nav-top__left-contain')) {
      this.handleAutoClose()
      return
    }
    if (
      hasAncestorElement(
        e.target,
        'document-mouse-event-ignore,rc-tooltip,titaui-popup,tita-ui-popup,rc-trigger-popup,rc-dialog-root,rc-dialog,common-modal,in-modal,task-hot-add,modal,titaui-mblog,action__right,rc-dialog-conten,rc-dialog-body,rc-dialog-footer,ant-select-dropdown,ant-formily-layout,ant-select-clear'
      )
    ) {
      return
    }
    if (
      hasAncestorElement(
        e.target,
        'modal-centered, tita-ui__button, btn-size-m'
      )
    ) {
      return
    }
    if (hasAncestorElement(e.target, 'tita-ui--drawer-header-back')) {
      return
    }
    this.handleAutoClose()
  }

  regist = (type: any, component: any) => {
    // @ts-ignore
    if (!window.__drawer_map__) window.__drawer_map__ = {}
    // if(!window.__drawer_map__[type]){
    // @ts-ignore
    window.__drawer_map__[type] = component
    // }
  }

  private get drawer() {
    if (this._drawer) return this._drawer
    // 总结KR推屏获取不到drawer实例的情况
    // @ts-ignore
    window.drawerClose = this.close

    ReactDom.render(
      <DrawerManagerContext.Provider
        value={{
          getCanBack: () => this.history.length > 1,
          goBack: () => this.goBack(),
          // @ts-ignore
          close: () => this.close(),
          saveProps: (props = {}) => {
            const current = this.history[this.history.length - 1]
            if (current) {
              current.props = { ...current.props, ...props }
            } else {
              current.props = { ...props }
            }
          },
        }}
      >
        <DrawerWrapper
          ref={(r) => (this._drawer = r)}
          getContainer={() => this.el}
        />
      </DrawerManagerContext.Provider>,
      this.getEl()
    )
    return this._drawer
  }

  private getEl() {
    if (!this.el || !this.el.parentNode) {
      this.el = document.createElement('div')

      document.body.appendChild(this.el)
    }
    return this.el
  }
}
const rootManager = new DrawerManager()
export default rootManager
