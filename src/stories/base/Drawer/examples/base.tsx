import { usePopupOpen } from '@/hooks/usePopupOpen'
import { But<PERSON> } from '@/stories'
import Drawer from '../index'
import React, { FC } from 'react'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  const ctl = usePopupOpen(false)

  return (
    <section>
      <Button onClick={ctl.show}>打开推屏</Button>
      <Drawer mask onClose={ctl.hide} visible={ctl.open}>
        <Drawer.Header content={<h1>推屏</h1>} />
        <main>推屏内容</main>
      </Drawer>
    </section>
  )
}

export default React.memo(Base)
