import React, { ReactNode, useMemo, useState } from 'react'
import Popup from '../Popup'
import { IHeaderActionProps } from './headerAction'

interface Props {
  children: ReactNode
  visible: boolean
  onVisibleChange: (visible: boolean) => void
  items: IHeaderActionProps[]
  approvalSetting: any
  detailInfo: any
}
interface IHeaderActionItemProps extends IHeaderActionProps {
  onClose: (visible: boolean) => void
  approvalSetting: any
  detailInfo: any
}
const HeaderDropItem: React.FC<IHeaderActionItemProps> = ({
  icon,
  title,
  hoverColor = '#2879ff',
  color = '#3F4755',
  iconColorInDrop = '#A4ACB9',
  hoverIconColorInDrop,
  colorInDrop,
  hoverColorInDrop,
  onClick,
  onClose,
  approvalSetting,
  detailInfo,
}) => {
  const [hover, setHover] = useState(false)
  const hasIcon = !!icon && typeof icon === 'string'

  return (
    <>
      {icon === 'tu-icon-del' ? (
        <div className='header-drop-item-approval'>
          {hasIcon && (
            <div className='header-drop-item-icon'>
              {typeof icon === 'string' ? (
                <span className={icon as string} />
              ) : (
                icon
              )}
            </div>
          )}
          {title}
        </div>
      ) : (
        <div
          className='header-drop-item'
          style={{
            color: hover
              ? hoverColorInDrop || hoverColor
              : colorInDrop || color,
          }}
          onClick={(e) => {
            onClick && onClick(e)
            onClose(false)
          }}
          onMouseOver={() => setHover(true)}
          onMouseOut={() => setHover(false)}
        >
          {hasIcon && (
            <div
              className='header-drop-item-icon'
              style={{
                color: hover
                  ? hoverIconColorInDrop || hoverColorInDrop || hoverColor
                  : iconColorInDrop || colorInDrop || color,
              }}
            >
              {typeof icon === 'string' ? (
                <span className={icon as string} />
              ) : (
                icon
              )}
            </div>
          )}
          {title}
        </div>
      )}
    </>
  )
}

const HeaderDrop: React.FC<Props> = ({
  visible,
  onVisibleChange,
  items,
  children,
  approvalSetting,
  detailInfo,
}) => {
  const renderDrop = useMemo(
    () => (
      <div className='tita-ui--drawer-header-action-drop-wrapper'>
        {items.map((item) => (
          <HeaderDropItem
            {...item}
            detailInfo={detailInfo}
            approvalSetting={approvalSetting}
            onClose={() => onVisibleChange(false)}
          />
        ))}
      </div>
    ),
    [items]
  )
  return (
    // @ts-ignore
    <Popup
      popupVisible={visible}
      popupPlacement='bottomRight'
      onPopupVisibleChange={onVisibleChange}
      action={['click']}
      popup={renderDrop}
      destroyPopupOnHide
      popupAlign={{
        offset: [12, 2],
      }}
    >
      {children}
    </Popup>
  )
}

export default HeaderDrop
