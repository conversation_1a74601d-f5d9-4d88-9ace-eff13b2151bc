export function hasAncestorElement(node: any, className: string) {
  if (!node) return false
  if (is(node, className)) return true
  return hasAncestorElement(node.parentNode, className)
}

export function is(node: any, className: string) {
  if (!className || !node) return false
  const classNameMap: any = {}
  className.split(',').forEach((clss) => (classNameMap[clss] = true))
  if (!node.className || typeof node.className !== 'string') return false
  for (const clss of node.className?.split(/\s+/)) {
    if (classNameMap[clss]) {
      return true
    }
  }
  return false
}
