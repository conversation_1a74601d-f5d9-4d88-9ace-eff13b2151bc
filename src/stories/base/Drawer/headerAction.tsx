import classnames from "classnames";
import React, {
  MouseEventHand<PERSON>,
  ReactElement,
  useRef,
  useState,
} from "react";
import Tooltip from "../Tooltip";
import HeaderDrop from "./headerDrop";

export interface IHeaderActionProps {
  icon?: string | ((active:boolean) => React.ReactNode) | React.ReactNode;
  title?: string;
  onClick?: MouseEventHandler<HTMLDivElement>;
  color?: string;
  hoverColor?: string;

  iconColorInDrop?:string;
  hoverIconColorInDrop?: string;
  colorInDrop?: string;
  hoverColorInDrop?: string;

  style?: React.CSSProperties;
  className?: string;
  children?: IHeaderActionProps[];
  approvalSetting?: any;
  detailInfo?: any;
}

function HeaderAction({
  icon,
  style,
  color = "#A4ACB9",
  hoverColor = "#2879ff",
  onClick,
  className,
  title,
  children,
  approvalSetting,
  detailInfo,
}: IHeaderActionProps): ReactElement {
  const [hover, setHover] = useState(false);
  const active = useRef(false);
  const [dropVisible, setDropVisible] = useState(false);
  if (typeof icon === "function") {
    icon = icon(hover || active.current);
  } else if (typeof icon === "string") {
    icon = <a className={icon} style={{ color: hover || active.current ? hoverColor : color, ...style }} />;
  }
  let content = (
    <div
      className={classnames("tita-ui--drawer-header-action", className)}

      onClick={(e) => {
        children && children.length > 0 && setDropVisible(true);
        onClick && onClick(e);
      }}
      onMouseOver={() => !active.current && setHover(true)}
      onMouseOut={() => !active.current && setHover(false)}
    >
      {icon}
    </div>
  );

  if (title) {
    content = (
      <Tooltip overlay={title || null} placement="top" zIndex={10000}>
        {content}
      </Tooltip>
    );
  }
  if (children && children.length > 0) {
    content = (
      <HeaderDrop
        visible={dropVisible}
        detailInfo={detailInfo}
        approvalSetting={approvalSetting}
        onVisibleChange={(visible) => {
          active.current = visible;
          !visible && setDropVisible(false);
          !visible && setHover(false);
        }}
        items={children}
      >
        {content}
      </HeaderDrop>
    );
  }

  return content;
}

export default HeaderAction;
