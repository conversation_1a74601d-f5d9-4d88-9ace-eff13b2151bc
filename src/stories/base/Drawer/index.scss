.tita-ui--drawer {
  outline: none;

  &__wrapper {
    height: 100%;
  }
  &-header-close {
    width: 28px;
    height: 28px;
    border-radius: 8px;
    line-height: 28px !important;
    text-align: center;

    &:hover {
      background-color: #F0F4FA;
    }
  }

  &.drawer-open.drawer-right .drawer-content-wrapper {
    box-shadow: 0px 0px 24px 0px rgba(127, 145, 180, 0.2);
  }
  &.drawer-open.drawer-top .drawer-content-wrapper {
    box-shadow: 0px 0px 24px 0px rgba(127, 145, 180, 0.2);
  }
  &.drawer.drawer-open .drawer-mask{
    opacity: 1;
    background:  rgba(20, 28, 40, 0.7);
  }
  
  .drawer-mask {
    background: rgba(20, 28, 40, 0.7) !important;
  }

  &.transparent-mask {
    .drawer-mask {
      background: transparent !important;
    }
  }

  &-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  &-header {
    height: 56px;
    border-bottom: 1px solid #f0f2f5;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    padding: 0 32px;
    color: #141c28;
    font-weight: 600;
    font-size: 16px;
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 1;

    &--noFooterLine{
      border-bottom: none;
    }

    &-back {
      font-size: 12px;
      color: #89919f;

      &:hover {
        color: #2879ff;
      }
    }

    .tu-icon-fanhui {
      margin-right: 5px;
    }

    &-divider {
      margin: 0 8px;
      width: 1px;
      height: 22px;
      background: linear-gradient(
        180deg,
        rgba(191, 199, 213, 0) 0%,
        #bfc7d5 50%,
        rgba(240, 242, 245, 0) 100%
      );
    }

    &-action {
      font-size: 18px;
      color: #a4acb9;
      width: 18px;
      height: 18px;
      cursor: pointer;
      margin: 0 8px;
      background-size: contain;
      background-position: center center;
      background-repeat: no-repeat;

      & > a {
        display: flex;
        align-items: center;
        justify-content: center;
        color: inherit;
        width: 18px;
        height: 18px;
        background-size: contain;
        background-position: center center;
        background-repeat: no-repeat;
      }
    }

    &-close {
      color: #141c28;
      font-size: 20px;
      cursor: pointer;
      margin-left: 18px;
      font-weight: bold;
    }
  }

  &-body {
    height: 0;
    flex: 1;
    overflow: auto;
  }

  &__hide {
    position: absolute;
    display: none;
  }

  &.no-transition {
    transition: none !important;

    .drawer-content-wrapper {
      transition: none !important;
    }
  }
}

.tita-ui--drawer-header-action-drop-wrapper {
  background-color: #fff;
  padding: 16px 0;
  margin-top: 4px;
  box-shadow: 0px 4px 12px 0px rgba(127, 145, 180, 0.2);
  border: 1px solid #f0f2f5;
  position: relative;
  border-radius: 4px;

  &::before,
  &::after {
    content: '';
    display: block;
    position: absolute;
    top: -4px;
    right: 17px;
    width: 0;
    height: 0;
    border-top: none;
    border-left: 4px solid transparent;
    border-bottom: 4px solid #f0f2f5;
    border-right: 4px solid transparent;
  }

  &::after {
    top: -3px;
    border-bottom: 4px solid #fff;
  }

  .header-drop-item {
    height: 32px;
    display: flex;
    align-items: center;
    padding: 0 23px;
    cursor: pointer;

    &:hover {
      background-color: #f7f8fa;
    }

    &-icon {
      font-size: 18px;
      color: inherit;
      width: 18px;
      height: 18px;
      cursor: pointer;
      margin-right: 10px;
      background-size: contain;
      background-position: center center;
      background-repeat: no-repeat;

      & > span {
        display: flex;
        align-items: center;
        justify-content: center;
        color: inherit;
        width: 18px;
        height: 18px;
        background-size: contain;
        background-position: center center;
        background-repeat: no-repeat;
      }
    }
  }

  .header-drop-item-approval {
    height: 32px;
    display: flex;
    align-items: center;
    padding: 0 23px;
    cursor: pointer;
    color: #bfc7d5;

    &-icon {
      font-size: 18px;
      color: inherit;
      width: 18px;
      height: 18px;
      cursor: pointer;
      margin-right: 10px;
      background-size: contain;
      background-position: center center;
      background-repeat: no-repeat;

      & > span {
        display: flex;
        align-items: center;
        justify-content: center;
        color: inherit;
        width: 18px;
        height: 18px;
        background-size: contain;
        background-position: center center;
        background-repeat: no-repeat;
      }
    }
  }
}

.drawer-left .drawer-content-wrapper,
.drawer-right .drawer-content-wrapper,
.drawer-left .drawer-content,
.drawer-right .drawer-content {
  border-radius: 24px 0 0 24px;
}
.drawer-top .drawer-content-wrapper,
.drawer-top .drawer-content {
  border-radius: 0 0 24px 24px;
}
