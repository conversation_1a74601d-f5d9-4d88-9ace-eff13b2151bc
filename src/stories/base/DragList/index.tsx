import React, { FC, useEffect, useState } from 'react'
import classNames from 'classnames'
import './index.scss'
import { DragDropContext, DropResult } from 'react-beautiful-dnd'
import { reorder } from './utils'
import QuoteList from './quote-list'
import { getRandomStr } from '@tita/utils'

export interface IDragListProps {
  initial?: any[],
  data?: any[],
  listId?: string,
  isCombineEnabled?: boolean,
  render: (params: { data: any, isDragging?: boolean, index: number }) => React.ReactNode
  listStyle?: Object,
  customDraggable?: boolean
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-drag-list'

export const DragList: FC<IDragListProps> = React.memo(({ data, render, listStyle, listId: propsListId, customDraggable, isCombineEnabled, className, style }) => {
  const [quotes, setQuotes] = useState(() => data || []);
  useEffect(() => {
    setQuotes(data || [])
  }, [data])
  
  const [listId] = useState(() => propsListId || getRandomStr())

  function onDragStart() {
    // Add a little vibration if the browser supports it.
    // Add's a nice little physical feedback
    if (window.navigator.vibrate) {
      window.navigator.vibrate(100);
    }
  }

  function onDragEnd(result: DropResult) {
    // combining item
    if (result.combine) {
      // super simple: just removing the dragging item
      const newQuotes: any[] = [...quotes];
      newQuotes.splice(result.source.index, 1);
      setQuotes(newQuotes);
      return;
    }

    // dropped outside the list
    if (!result.destination) {
      return;
    }

    if (result.destination.index === result.source.index) {
      return;
    }

    const newQuotes = reorder(
      quotes,
      result.source.index,
      result.destination.index,
    );

    setQuotes(newQuotes);
  }

  return (
    <DragDropContext onDragStart={onDragStart} onDragEnd={onDragEnd}>
      <div className={classNames(preCls, className)} style={style}>
        <QuoteList
          listId={listId}
          render={render}
          style={listStyle}
          quotes={quotes}
          customDraggable={customDraggable}
          isCombineEnabled={isCombineEnabled}
        />
      </div>
    </DragDropContext>
  )
})

export default DragList
