import React, { <PERSON> } from 'react'
import DragList from '@/stories/base/DragList'
import Scroll from '../../Scroll'

export interface IBaseProps { }

const quotes = new Array(1000).fill(0).map((_, i) => ({ key: i + '' }))

const Base: FC<IBaseProps> = ({ }) => {
  return (
    <Scroll height={500}>
      <DragList initial={quotes} render={(data: any) => <div className="h-10 w-20 bg-white shadow-sm rounded-sm border">Hello</div>} />
    </Scroll>
  )
}

export default React.memo(Base)
