// @flow
import React, { CSSProperties } from 'react'
import { DraggableStateSnapshot } from 'react-beautiful-dnd'
import { Droppable } from 'react-beautiful-dnd'
import { DraggableProvided } from 'react-beautiful-dnd'
import { Draggable } from 'react-beautiful-dnd'
import { DroppableProvided, DroppableStateSnapshot } from 'react-beautiful-dnd'
import { IDragListProps } from '.'
import { number } from 'echarts/core'

const scrollContainerHeight: number = 250

type Props = {
  listId?: string
  listType?: string
  render: IDragListProps['render']
  quotes: any[]
  title?: string
  internalScroll?: boolean
  customDraggable?: boolean
  scrollContainerStyle?: Object
  isDropDisabled?: boolean
  isCombineEnabled?: boolean
  style?: Object
  // may not be provided - and might be null
  ignoreContainerClipping?: boolean

  useClone?: boolean
}

type QuoteListProps = {
  quotes: any[]
  customDraggable?: boolean
  render: IDragListProps['render']
}

function getStyle(provided: DraggableProvided): CSSProperties {
  return {
    ...provided.draggableProps.style,
  }
}

const DraggableQuoteItem = React.memo(function ({
  provided,
  quote,
  index,
  isDragging,
  render,
}: {
  quote: any
  index?: number
  isDragging: boolean
  isGroupedOver: boolean
  provided: DraggableProvided
  render: IDragListProps['render']
}) {
  return (
    <div
      ref={provided.innerRef}
      {...provided.draggableProps}
      {...provided.dragHandleProps}
      style={getStyle(provided)}
    >
      {render({ data: quote, isDragging, index: index as number })}
    </div>
  )
})
const QuoteItem = React.memo(function ({
  quote,
  index,
  render,
}: {
  quote: any
  index?: number
  render: IDragListProps['render']
}) {
  return <>{render({ data: quote, index: index as number })}</>
})

// @ts-ignore
const InnerQuoteList = React.memo(function InnerQuoteList(
  props: QuoteListProps
) {
  return props.quotes.map((quote: any, index: number) => {
    if (props.customDraggable) {
      return (
        <QuoteItem
          key={quote.key}
          quote={quote}
          index={index}
          render={props.render}
        />
      )
    }
    return (
      <Draggable key={quote.key} draggableId={quote.key} index={index}>
        {(
          dragProvided: DraggableProvided,
          dragSnapshot: DraggableStateSnapshot
        ) => (
          <DraggableQuoteItem
            key={quote.key}
            quote={quote}
            index={index}
            render={props.render}
            isDragging={dragSnapshot.isDragging}
            isGroupedOver={Boolean(dragSnapshot.combineTargetFor)}
            provided={dragProvided}
          />
        )}
      </Draggable>
    )
  })
})

type InnerListProps = {
  dropProvided: DroppableProvided
  render: IDragListProps['render']
  quotes: any[]
  title?: string
  customDraggable?: boolean
}

function InnerList(props: InnerListProps) {
  const { quotes, dropProvided, customDraggable, render } = props

  return (
    <div>
      <div ref={dropProvided.innerRef}>
        {/* @ts-ignore */}
        <InnerQuoteList quotes={quotes} render={render} customDraggable={customDraggable} />
        {dropProvided.placeholder}
      </div>
    </div>
  )
}

export default function QuoteList(props: Props) {
  const {
    ignoreContainerClipping,
    internalScroll,
    scrollContainerStyle,
    isDropDisabled,
    isCombineEnabled,
    listId = 'LIST',
    listType,
    customDraggable,
    render,
    style,
    quotes,
    title,
    useClone,
  } = props

  return (
    <Droppable
      droppableId={listId}
      type={listType}
      ignoreContainerClipping={ignoreContainerClipping}
      isDropDisabled={isDropDisabled}
      isCombineEnabled={isCombineEnabled}
      // @ts-ignore
      renderClone={
        useClone
          ? (provided, snapshot, descriptor) => (
              <DraggableQuoteItem
                quote={quotes[descriptor.source.index]}
                // @ts-ignore
                provided={provided}
                render={render}
                isDragging={snapshot.isDragging}
                // @ts-ignore
                isClone
              />
            )
          : null
      }
    >
      {(
        dropProvided: DroppableProvided,
        dropSnapshot: DroppableStateSnapshot
      ) => (
        <div
          style={style}
          className='flex flex-col'
          isDraggingOver={dropSnapshot.isDraggingOver}
          isDropDisabled={isDropDisabled}
          isDraggingFrom={Boolean(dropSnapshot.draggingFromThisWith)}
          {...dropProvided.droppableProps}
        >
          {internalScroll ? (
            <div
              className='overflow-y-auto overflow-x-hidden'
              style={scrollContainerStyle}
            >
              <InnerList
                quotes={quotes}
                customDraggable={customDraggable}
                title={title}
                render={render}
                dropProvided={dropProvided}
              />
            </div>
          ) : (
            <InnerList
              quotes={quotes}
              customDraggable={customDraggable}
              title={title}
              render={render}
              dropProvided={dropProvided}
            />
          )}
        </div>
      )}
    </Droppable>
  )
}
