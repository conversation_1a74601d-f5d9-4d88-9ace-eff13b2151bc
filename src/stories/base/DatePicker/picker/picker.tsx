import React, { FC, ReactNode, useMemo, useState } from 'react'
import classNames from 'classnames'
import moment, { Moment } from 'moment'
import Popup, { IPopupProps } from '../../Popup'
import PickerPanels from './picker-panels'
import PickerTrigger, { IPickerTriggerProps } from './components/picker-trigger'
import PickerContext from './context'
import { PickerMode } from './enum'
import {
  PickerDropdownClass,
  PickerDateFormat,
  PickerMaxDate,
  PickerMinDate,
  getPopupPlacement,
  RangeSettingTypes,
} from './utils'
import {
  TPickerMode,
  PickerSettingType,
  PickerValue,
  PickerValues,
  IDateTimeStep,
} from './type'
import './index.scss'
import ConditionRender from './components/condition-render'

export interface IPickerProps
  extends Partial<IPickerTriggerProps>,
    Partial<IPopupProps> {
  /** 自定义 trigger */
  children?: React.ReactElement | any
  /** date-time 模式下「时」「分」间隔，默认值：{ hour: 1, minute: 1 } */
  dateTimeStep?: IDateTimeStep
  /** date-time 模式下默认的时间 */
  defaultTime?: string
  /** popup 对齐方式，默认值：ltr */
  direction?: 'ltr' | 'rtl'
  /** picker显示的方式：layer-popup 弹窗 | tiled-平铺展示 */
  displayMode?: 'layer' | 'tiled'
  /** 日期格式化，默认值：'YYYY/MM/DD' */
  format?: string
  /** 日期最大值 */
  max?: PickerValue
  /** 日期最小值 */
  min?: PickerValue
  /** 内部属性，是否支持范围选择，默认值：false，RangePicker 模式下为 true */
  multiple?: boolean
  /** picker 类型："date" | "dateTime" | "week" | "month" | "year"，默认值：date */
  picker?: TPickerMode
  /** 内部属性，当前日期设置的类型："endDate" | "reset" | "startDate" */
  settingType?: PickerSettingType
  /** 默认选中开始时间还是结束时间 */
  defaultSettingType?: PickerSettingType
  /** 禁用默认的 div 容器 */
  disableDefaultDiv?: boolean
  value: PickerValues
  visible?: boolean
  /** 判断日期是否可点击的方法，参数为具体的日期 Moment，返回 boolean，true-禁用 | false-可用 */
  disabledDate?: (date: Moment) => boolean
  /** 日期修改的回调方法，接受 boolean | Promise<boolean> 作为返回值，判定当前是否修改成功 */
  onChange: (
    date: PickerValues | { isLong: boolean; dateRange: PickerValues }
  ) => boolean | Promise<boolean>
  /** 自定义渲染日期 */
  renderDate?: (date: Moment) => string | ReactNode
}

const Picker: FC<IPickerProps> = (props) => {
  const {
    children,
    direction = 'ltr',
    dateTimeStep = {
      hour: 1,
      minute: 1,
    },
    defaultTime = '00:00',
    disabledDate,
    displayMode = 'layer',
    format = PickerDateFormat,
    max = PickerMaxDate,
    min = PickerMinDate,
    multiple = false,
    picker = PickerMode.Date,
    popupPlacement = 'bottomLeft',
    settingType = 'startDate',
    value: propsValue = [moment()],
    visible = false,
    onChange,
    onPopupVisibleChange,
    renderDate,
    disableDefaultDiv,
    ...restProps
  } = props
  const value = propsValue || [moment()]
  const [popupVisible, setPopupVisible] = useState(visible)

  const onClearHandler = () => onChange([''])
  const onCloseHandler = () => onPopupVisibleChangeHandler(false)

  const onChangeHandler = async (date: PickerValues) => {
    const newDate = date.map((item) =>
      item ? moment(item).format(format) : item,
    )
    const result = await onChange(newDate)
    result !== false && onCloseHandler()
  }

  const onPopupVisibleChangeHandler = (visible: boolean) => {
    setPopupVisible(visible)
    onPopupVisibleChange?.(visible)
  }

  const popContent = useMemo(
    () => (
      <PickerPanels
        onChange={onChangeHandler}
        onClose={onCloseHandler}
        {...restProps}
      />
    ),
    [value],
  )

  return (
    <PickerContext.Provider
      value={{
        dateTimeStep,
        defaultTime,
        disabledDate,
        format,
        max,
        min,
        multiple,
        picker,
        renderDate,
        settingType,
        value,
      }}
    >
      <ConditionRender condition={displayMode === 'layer'}>
        {/* @ts-ignore */}
        <Popup
          destroyPopupOnHide
          clearPadding
          extraClass={classNames(PickerDropdownClass)}
          popup={popContent}
          popupVisible={popupVisible}
          popupPlacement={getPopupPlacement(direction, popupPlacement)}
          onPopupVisibleChange={onPopupVisibleChangeHandler}
          {...restProps}
        >
          <PickerTrigger
            focused={popupVisible}
            viewDate={value?.[0] ? moment(value[0]) : ''}
            onClear={onClearHandler}
            disableDefaultDiv={disableDefaultDiv}
            onClick={() => setPopupVisible(true)}
            {...restProps}
          >
            {children}
          </PickerTrigger>
        </Popup>
      </ConditionRender>
      <ConditionRender condition={displayMode === 'tiled'}>
        {popContent}
      </ConditionRender>
    </PickerContext.Provider>
  )
}

export default Picker
