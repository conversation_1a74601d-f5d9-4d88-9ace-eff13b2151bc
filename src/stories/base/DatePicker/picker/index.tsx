import DateTimeRangePicker from './date-time-range'
import PickerComp, { IPickerProps } from './picker'
import RangePicker from './range-picker'
import Range from './range'
import HourPicker from './hour-picker'

interface IPicker {
  DateTimeRange: typeof Range
  Range: typeof RangePicker
  Hour: typeof HourPicker
}

const Picker: Partial<IPicker & React.FC<IPickerProps>> = PickerComp

Picker.DateTimeRange = Range
Picker.Range = RangePicker
Picker.Hour = HourPicker

export default Picker
