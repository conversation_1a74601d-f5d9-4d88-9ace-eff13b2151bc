import React, { useEffect, useState } from 'react'
import { PickerValues } from './type'

function useInitializeRangeValue(
  initialData: PickerValues,
  defaultData: PickerValues,
  visible: boolean,
): [PickerValues, React.Dispatch<React.SetStateAction<PickerValues>>] {
  const [viewDate, setViewDate] = useState(defaultData)

  useEffect(() => {
    if (visible) {
      let _viewDate = defaultData
      if (initialData instanceof Array) {
        const valuesLen = initialData?.length
        const startDate = initialData?.[0] || ''
        const endDate = initialData?.[valuesLen - 1] || startDate
        _viewDate = [startDate, endDate]
      }
      setViewDate(_viewDate)
    }
  }, [visible])

  return [viewDate, setViewDate]
}

export default useInitializeRangeValue
