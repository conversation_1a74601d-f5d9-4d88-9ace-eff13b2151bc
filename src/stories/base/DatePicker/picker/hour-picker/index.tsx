import React, { FC, useMemo, useState, useCallback } from 'react'
import classNames from 'classnames'
import Popup, { IPopupProps } from '../../../Popup'
import RangePickerTrigger from '../components/range-picker-trigger'
import TimePicker from '../components/panels/date-time-panel/time-picker'
import { getLocale } from '@tita/utils';
import './index.scss'

// @ts-ignore
export interface IHourPickerProps extends IPopupProps {
  children?: React.ReactNode | any
  className?: string
  style?: React.CSSProperties
  /** popupClassName */
  PickerDropdownClass?: string
  onChange?: (value: string) => void
  initialValue?: string
}
const preCls = 'tita-ui-hourPicker'
const HourPicker: FC<IHourPickerProps> = React.memo((props) => {
  const {
    children,
    className,
    style,
    PickerDropdownClass,
    onChange,
    initialValue,
    ...restProps
  } = props

  const onChangeHandler = useCallback((value: string) => {
    onChange?.(value)
  }, [onChange])


  const popContent = useMemo(() => {
    return (
      <TimePicker
        className={`${preCls}__hourPicker`}
        onChange={onChangeHandler}
        value={initialValue || ""}
        timeStep={{
          hour: 1,
          minute: 15,
        }}
        labelOption={
          {
            hour: getLocale('Mod_Hour'),
            minute: getLocale('Tasks_Tab_MyS_Minute'),
          }
        }
      />
    )
  }, [onChangeHandler, initialValue])
  return (
    <div className={classNames(preCls, className)} style={style}>
      {/* @ts-ignore */}
      <Popup
        destroyPopupOnHide
        clearPadding
        extraClass={classNames(PickerDropdownClass)}
        // @ts-ignore
        popup={popContent}
        {...restProps}
      >
        {children}
      </Popup>
    </div>
  )
})
export default HourPicker
