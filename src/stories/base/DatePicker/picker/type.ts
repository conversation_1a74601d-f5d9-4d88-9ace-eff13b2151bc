import { Moment } from 'moment'

type ShortCuts =
  | 'currentYear'
  | 'currentMonth'
  | 'prevMonth'
  | 'currentWeekDay'
  | 'currentWeek'
  | 'prevWeek'
  | 'nextWeek'
  | 'nextMonth'
  | 'today'
  | 'yesterday'
  | 'tomorrow'

export type TPickerMode = 'date' | 'dateTime' | 'week' | 'month' | 'year'
export type PickerSettingType = 'endDate' | 'reset' | 'startDate'

export type PickerPopupPlacement =
  | 'bottomLeft'
  | 'bottomRight'
  | 'topLeft'
  | 'topRight'

export type PickerValue = Moment | string
export type PickerValues = PickerValue[]
export type TOnSelectFn = (date: PickerValue, type: TPickerMode, year?: number) => void;
export interface PickerShortCuts {
  key: ShortCuts
  title: string
}

export interface IDateTimeStep {
  hour?: number
  minute?: number
}
