import React, { ReactNode } from 'react'
import moment, { Moment } from 'moment'
import { PickerMode } from './enum'
import { TPickerMode, PickerSettingType, PickerValue, IDateTimeStep } from './type'
import { PickerDateFormat, PickerMaxDate, PickerMinDate, RangeSettingTypes } from './utils'

interface PickerContextProps {
  dateTimeStep?: IDateTimeStep
  defaultTime?: string
  format?: string
  max?: PickerValue
  min?: PickerValue
  multiple?: boolean
  picker?: TPickerMode
  settingType?: PickerSettingType
  value: (Moment | string)[]
  disabledDate?: (date: Moment) => boolean
  renderDate?: (date: Moment) => string | ReactNode
}

interface RangePickerContextProps extends Partial<PickerContextProps> {
  values?: (Moment | string)[]
}

const DefaultContextData: Partial<PickerContextProps> = {
  dateTimeStep: {
    hour: 1,
    minute: 1
  },
  defaultTime: '00:00',
  format: PickerDateFormat,
  max: PickerMaxDate,
  min: PickerMinDate,
  multiple: false,
  picker: PickerMode.Date,
  settingType: 'startDate',
}

const PickerContext = React.createContext<PickerContextProps>({
  ...DefaultContextData,
  value: [moment()],
})

export const RangePickerContext = React.createContext<RangePickerContextProps>({
  ...DefaultContextData,
  values: [moment(), moment()],
})

export default PickerContext
