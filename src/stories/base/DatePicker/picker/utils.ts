import cloneDeep from 'lodash/cloneDeep'
import moment from 'moment'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import { getLocale } from '@tita/utils'
import { SHORTCUTS } from './enum'
import {
  PickerSettingType,
  PickerShortCuts,
  PickerValue,
  PickerValues,
} from './type'
import { IPopupProps } from '../../Popup'

dayjs.extend(isBetween)

export const PickerClass = 'tita-ui__picker'
export const PickerHeadClass = 'tita-ui__picker-head'
export const PickerBodyClass = 'tita-ui__picker-body'
export const PickerPanelClass = 'tita-ui__picker-panel'
export const PickerCellClass = 'tita-ui__picker-cell'
export const PickerTriggerClass = 'tita-ui__picker-trigger'
export const PickerDropdownClass = 'tita-ui__picker-dropdown'
export const PickerRangesClass = 'tita-ui__picker-ranges'
export const PickerRangeTriggerClass = 'tita-ui__picker-ranges-trigger'

export const MONTHDATAMAP = [
  '一月',
  '二月',
  '三月',
  '四月',
  '五月',
  '六月',
  '七月',
  '八月',
  '九月',
  '十月',
  '十一月',
  '十二月',
]
export const MONTHDATAMAPEN = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
]
export const WEEK_DAYS_COUNT = 7
export const YEAR_DECADE_COUNT = 10

export const PickerDateFormat = 'YYYY/MM/DD'
export const PickerDateTimeFormat = 'YYYY/MM/DD HH:mm'
export const PickerMonthFormat = 'YYYY/MM'
export const PickerTimeFormat = 'HH:mm'
export const PickerYearFormat = 'YYYY'

export const PickerMaxDate = moment(`${moment().year() + 10}/12/31`).format(
  PickerDateFormat
)
export const PickerMinDate = '2000/01/01'

export const getCurrentValues = (symbol: any) => [
  moment().startOf(symbol),
  moment().endOf(symbol),
]

export const getCurrentWeekDay = () => [
  moment().isoWeekday(1).startOf('day'),
  moment().isoWeekday(7).endOf('day'),
]

export const getPreviousValues = (symbol: any) => {
  let previousDate
  switch (symbol) {
    case 'week':
      previousDate = [
        moment().subtract(1, 'weeks').isoWeekday(1).startOf('day'),
        moment().subtract(1, 'weeks').isoWeekday(7).endOf('day'),
      ]
      break
    case 'date':
      previousDate = [
        moment().subtract(1, 'days').startOf('day'),
        moment().subtract(1, 'days').endOf('day'),
      ]
      break
    default:
      previousDate = [
        moment().subtract(1, symbol).startOf(symbol),
        moment().subtract(1, symbol).endOf(symbol),
      ]
      break
  }
  return previousDate
}

export const getNextValues = (symbol: any) => {
  let previousDate
  switch (symbol) {
    case 'week':
      previousDate = [
        moment().add(1, 'weeks').isoWeekday(1).startOf('day'),
        moment().add(1, 'weeks').isoWeekday(7).endOf('day'),
      ]
      break
    case 'date':
      previousDate = [
        moment().add(1, 'days').startOf('day'),
        moment().add(1, 'days').endOf('day'),
      ]
      break
    default:
      previousDate = [
        moment().add(1, symbol).startOf(symbol),
        moment().add(1, symbol).endOf(symbol),
      ]
  }
  return previousDate
}

export enum RangeSettingTypes {
  END = 'endDate',
  RESET = 'reset',
  START = 'startDate',
}

export const RangePickerShortcutsKeysMapping = (
  showPreviousCycle?: boolean,
  showNextCycle?: boolean
): PickerShortCuts[] => {
  if (showPreviousCycle) {
    return [
      {
        key: SHORTCUTS.YESTERDAY,
        title: getLocale('OKR_MyO_Pr_Text_Yesterday') || '昨天',
      },
      {
        key: SHORTCUTS.TODAY,
        title: getLocale('Mod_Today') || '今天',
      },
      {
        key: SHORTCUTS.PREVIOUS_WEEK,
        title: getLocale('Rep_NewS_LastWeek') || '上周',
      },
      {
        key: SHORTCUTS.CURRENT_WEEKDAY,
        title: getLocale('Feed_Tab_Title_Thisweek') || '本周',
      },
      {
        key: SHORTCUTS.PREVIOUS_MONTH,
        title: getLocale('Rep_NewS_LastMonth') || '上月',
      },
      {
        key: SHORTCUTS.CURRENT_MONTH,
        title: getLocale('Feed_Tab_Title_Thismonth') || '本月',
      },
      {
        key: SHORTCUTS.CURRENT_YEAR,
        title: getLocale('Feed_Tab_Title_Thisyear') || '本年',
      },
    ]
  }
  if (showNextCycle) {
    return [
      {
        key: SHORTCUTS.TODAY,
        title: getLocale('Mod_Today') || '今天',
      },
      {
        key: SHORTCUTS.TOMORROW,
        title: getLocale('Pro_page_Plan_Tomorrow') || '明天',
      },
      {
        key: SHORTCUTS.CURRENT_WEEKDAY,
        title: getLocale('Feed_Tab_Title_Thisweek') || '本周',
      },
      {
        key: SHORTCUTS.NEXT_WEEK,
        title: getLocale('Mod_NextWeek') || '下周',
      },
      {
        key: SHORTCUTS.CURRENT_MONTH,
        title: getLocale('Feed_Tab_Title_Thismonth') || '本月',
      },
      {
        key: SHORTCUTS.NEXT_MONTH,
        title: '下月',
      },
      {
        key: SHORTCUTS.CURRENT_YEAR,
        title: getLocale('Feed_Tab_Title_Thisyear') || '本年',
      },
    ]
  }
  return [
    {
      key: SHORTCUTS.TODAY,
      title: getLocale('Mod_Today') || '今天',
    },
    {
      key: SHORTCUTS.CURRENT_WEEKDAY,
      title: getLocale('Feed_Tab_Title_Thisweek') || '本周',
    },
    {
      key: SHORTCUTS.CURRENT_MONTH,
      title: getLocale('Feed_Tab_Title_Thismonth') || '本月',
    },
    {
      key: SHORTCUTS.CURRENT_YEAR,
      title: getLocale('Feed_Tab_Title_Thisyear') || '本年',
    },
  ]
}

export const RangeWeekPickerShortcutsKeysMapping = () => [
  {
    key: SHORTCUTS.PREVIOUS_WEEK,
    title: getLocale('Rep_NewS_LastWeek'),
  },
  {
    key: SHORTCUTS.CURRENT_WEEK,
    title: getLocale('Feed_Tab_Title_Thisweek'),
  },
  {
    key: SHORTCUTS.NEXT_WEEK,
    title: getLocale('Mod_NextWeek'),
  },
]

export const getPickerDateBySettingType = (params: {
  defaultValue?: PickerValue
  format?: string
  settingType?: PickerSettingType
  values: PickerValues
}) => {
  const {
    defaultValue = moment(),
    format = PickerDateFormat,
    settingType,
    values,
  } = params
  if (values instanceof Array && settingType) {
    const valuesLen = values.length
    const startDate = values[0] || moment().format(format)
    const endDate = values[valuesLen - 1] || moment().format(format)
    return settingType === RangeSettingTypes.START ? startDate : endDate
  }
  return defaultValue
}

export const setPickerDateBySettingType = (params: {
  newDate: PickerValue
  settingType?: PickerSettingType
  values: PickerValues
  isLong?: boolean
}) => {
  const { newDate, settingType, values, isLong } = params
  if (values instanceof Array && settingType) {
    const valuesClone = cloneDeep(values)
    const isSettingStartDate = settingType === RangeSettingTypes.START
    const isResetRangeDate = settingType === RangeSettingTypes.RESET
    if (isResetRangeDate) return [moment(newDate)]
    const updateIndex = isSettingStartDate ? 0 : 1
    valuesClone.splice(updateIndex, 1, moment(newDate))
    if (
      isSettingStartDate &&
      moment(newDate, PickerDateFormat).isAfter(
        moment(valuesClone[1]).format(PickerDateFormat),
        'date'
      ) &&
      !isLong
    ) {
      valuesClone[0] = valuesClone[1]
      valuesClone[1] = moment(newDate)
    }
    if (
      !isSettingStartDate &&
      moment(newDate, PickerDateFormat).isBefore(
        moment(valuesClone[0]).format(PickerDateFormat),
        'date'
      ) &&
      !isLong
    ) {
      valuesClone[1] = valuesClone[0]
      valuesClone[0] = moment(newDate)
    }
    return valuesClone.slice(0, 2)
  }
  return [moment(newDate)]
}

export const isCurrentDateInRanges = (params: {
  nowDate: PickerValue
  values: PickerValues
  format?: string
}) => {
  const { format = PickerDateFormat, nowDate, values } = params
  const date = typeof nowDate === 'string' ? nowDate : nowDate.format(format)
  const data = {
    inRanges: false,
    rangeStart: false,
    rangeEnd: false,
  }
  if (values instanceof Array) {
    const valuesLen = values.length
    const startDate = values[0] || ''
    const endDate = values[valuesLen - 1] || ''
    if (!startDate || !endDate || valuesLen < 2) return data
    if (
      dayjs(date).isBetween?.(
        moment(startDate).format(format),
        moment(endDate).format(format),
        null,
        '[]'
      )
    ) {
      return {
        inRanges: true,
        rangeStart: dayjs(date).isSame(moment(startDate).format(format)),
        rangeEnd: dayjs(date).isSame(moment(endDate).format(format)),
      }
    }
    return data
  }
  return data
}

//获取某年某月有多少天
export const getDaysOfMonth = (year: number, month: number) => {
  switch (month) {
    case 1:
    case 3:
    case 5:
    case 7:
    case 8:
    case 10:
    case 12:
      return 31
    case 4:
    case 6:
    case 9:
    case 11:
      return 30
    case 2:
      return (year % 4 == 0 && year % 100 != 0) || year % 400 == 0 ? 29 : 28
    default:
      return 0
  }
}

export function getHours(hourStep: number = 1) {
  const hours: string[] = []
  for (let i = 0; i < 24; i += 1) {
    if (i % hourStep === 0) {
      hours.push(i < 10 ? `0${i}` : `${i}`)
    }
  }
  return hours
}
export function getMinutes(minuteStep: number = 1) {
  const minutes: string[] = []
  for (let i = 0; i < 60; i += 1) {
    if (i % minuteStep === 0) {
      minutes.push(i < 10 ? `0${i}` : `${i}`)
    }
  }
  return minutes
}

export const getPopupPlacement = (
  direction: 'rtl' | 'ltr',
  popupPlacement: IPopupProps['popupPlacement']
): IPopupProps['popupPlacement'] => {
  if (popupPlacement !== undefined) {
    return popupPlacement
  }
  return direction === 'rtl' ? 'bottomRight' : 'bottomLeft'
}

export const getMonth = () => [
  getLocale('Mod_Jan'),
  getLocale('Mod_Feb'),
  getLocale('Mod_Mar'),
  getLocale('Mod_Apr'),
  getLocale('Mod_May'),
  getLocale('Mod_Jun'),
  getLocale('Mod_Jul'),
  getLocale('Mod_Aug'),
  getLocale('Mod_Sept'),
  getLocale('Mod_Oct'),
  getLocale('Mod_Nov'),
  getLocale('Mod_Dec'),
]

export const getWeek = () => [
  getLocale('Mod_Sun'),
  getLocale('Mod_Mon'),
  getLocale('Mod_Tues'),
  getLocale('Mod_Wed'),
  getLocale('Mod_Thur'),
  getLocale('Mod_Fri'),
  getLocale('Mod_Sat'),
]

export const getWeekly = () => [
  getLocale('Tasks_Tab_MyS_Week1s'),
  getLocale('Tasks_Tab_MyS_Week2s'),
  getLocale('Tasks_Tab_MyS_Week3s'),
  getLocale('Tasks_Tab_MyS_Week4s'),
  getLocale('Tasks_Tab_MyS_Week5s'),
]
