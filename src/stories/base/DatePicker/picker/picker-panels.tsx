import React, { FC, useCallback, useContext, useState, useEffect } from 'react'
import classNames from 'classnames'
import DatePanel from './components/panels/date-panel'
import DateTimePanel from './components/panels/date-time-panel'
import MonthPanel from './components/panels/month-panel'
import YearPanel from './components/panels/year-panel'
import WeekPanel from './components/panels/week-panel'
import PickerContext from './context'
import { PickerMode } from './enum'
import {
  TPickerMode,
  PickerSettingType,
  PickerValues,
  PickerValue,
} from './type'
import { getPickerDateBySettingType, PickerClass } from './utils'
import './index.scss'

interface IPickerPanelsProps {
  picker?: TPickerMode
  settingType?: PickerSettingType
  value?: PickerValues
  onChange: (date: PickerValues) => void
  onClose: () => void
}

const PickerPanels: FC<IPickerPanelsProps> = (props) => {
  const { onChange, onClose } = props
  const {
    format,
    picker = PickerMode.Date,
    settingType,
    value,
  } = useContext(PickerContext)
  const [pickerMode, setPickerMode] = useState<TPickerMode>(picker)
  const [viewDate, setViewDate] = useState<PickerValues>(value)
  const [selectWeekDate, setSelectWeekDate] = useState<PickerValues>()

  const onViewDateChange = (date: any) => {
    setViewDate(() => [date])
  }

  const onPanelChange = (viewDate: PickerValue, type: TPickerMode) => {
    onViewDateChange(viewDate)
    setPickerMode(() => type)
  }

  const onDateSelect = (date: PickerValue) => onChange?.([date])

  const onDateTimeChange = (date: PickerValue) => onChange?.([date])

  const onMonthSelect = (date: PickerValue) => {
    onViewDateChange(date)
    if (picker !== PickerMode.Month && picker !== PickerMode.Week) {
      onPanelChange(date, PickerMode.Date)
    } else if (picker === PickerMode.Week) {
      onPanelChange(date, PickerMode.Week)
    } else if (onChange) {
      onChange([date])
    }
  }
  const onYearSelect = (date: PickerValue) => {
    onViewDateChange(date)
    if (picker !== PickerMode.Year) {
      onPanelChange(date, PickerMode.Month)
    } else if (onChange) {
      onChange([date])
    }
  }

  const onWeekSelect = (date: any, year?: any) => {
    setSelectWeekDate(date)
    if (onChange) {
      onChange(date)
    }
  }

  const onSelectChange = (
    date: PickerValue,
    type: TPickerMode,
    year?: number,
  ) => {
    switch (type) {
      case PickerMode.Date:
        onDateSelect(date)
        break
      case PickerMode.Month:
        onMonthSelect(date)
        break
      case PickerMode.Year:
        onYearSelect(date)
        break
      case PickerMode.Week:
        onWeekSelect(date, year)
        break
      default:
        break
    }
  }

  const renderPanels = useCallback(() => {
    let panel
    const date = getPickerDateBySettingType({
      format,
      settingType,
      values: viewDate,
    })
    switch (pickerMode) {
      case PickerMode.Date:
        panel = (
          <DatePanel
            viewDate={date}
            onSelect={onSelectChange}
            onPanelChange={onPanelChange}
          />
        )
        break
      case PickerMode.DateTime:
        panel = (
          <DateTimePanel
            onCancel={onClose}
            onChange={onDateTimeChange}
            viewDate={date}
          />
        )
        break
      case PickerMode.Month:
        panel = (
          <MonthPanel
            viewDate={date}
            onSelect={onSelectChange}
            onViewDateChange={onViewDateChange}
            onPanelChange={onPanelChange}
          />
        )
        break
      case PickerMode.Year:
        panel = (
          <YearPanel
            viewDate={date}
            onSelect={onSelectChange}
            onViewDateChange={onViewDateChange}
          />
        )
        break
      case PickerMode.Week:
        panel = (
          <WeekPanel
            // @ts-ignore
            viewDate={viewDate}
            onSelect={onSelectChange}
            onViewDateChange={onViewDateChange}
            onPanelChange={onPanelChange}
            valueTypeIsRange={true}
            // @ts-ignore
            selectWeekDate={selectWeekDate}
          />
        )
      default:
        break
    }
    return panel
  }, [pickerMode, selectWeekDate, viewDate])

  useEffect(() => {
    setViewDate(value)
    if (typeof value[0] === 'string') {
      setSelectWeekDate(value)
    }
  }, [value])

  return (
    <div
      className={classNames(PickerClass, [
        {
          [`${PickerClass}-${picker}`]: picker,
        },
      ])}
    >
      {renderPanels()}
    </div>
  )
}

export default PickerPanels
