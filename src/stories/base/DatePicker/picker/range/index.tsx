import { useZindex } from '@tita/hooks'
import { getLocale } from '@tita/utils'
import classNames from 'classnames'
import { FC, useEffect, useMemo, useState } from 'react'
import Popup from '../../../Popup'
import ConditionRender from '../components/condition-render'
import RangePickerTrigger from '../components/range-picker-trigger'
import { RangePickerContext } from '../context'
import { PickerMode } from '../enum'
import { IPickerProps } from '../picker'
import { PickerValues } from '../type'
import {
  PickerDateFormat,
  PickerDateTimeFormat,
  PickerDropdownClass,
  PickerMonthFormat,
  PickerYearFormat,
  getPopupPlacement
} from '../utils'
import './index.scss'
import PopupContent from './popupContent'

// @ts-ignore
export interface IRangePickerProps extends IPickerProps {
  /** 是否允许为空提交 */
  canEmpty?: boolean
  /** popup 内容容器 class */
  className?: string
  /** 自定义 trigger 外层容器 class */
  contentClassName?: string
  defaultTimes?: string[]
  /** 结束日期是否是「长期」 */
  isLongTerm?: boolean
  /** 是否展示清除按钮 */
  showClear?: boolean
  /** 是否展示长期按钮 */
  showLongTerm?: boolean
  /** 是否展示上一周期（上周、上月） */
  showPreviousCycle?: boolean
  /** 是否展示下一周期（下周、下月） */
  showNextCycle?: boolean
  /** 是否展示时间 */
  showTime?: boolean
  visible?: boolean
  value?: PickerValues | { isLong: boolean; dateRange: PickerValues }
  onChange: (
    date: PickerValues | { isLong: boolean; dateRange: PickerValues }
  ) => boolean | Promise<boolean>
  onCancel?: () => void
  onOk?: (
    date: PickerValues | { isLong: boolean; dateRange: PickerValues }
  ) => boolean | Promise<boolean>
  /** 是否展示底部操作栏 */
  showFooter?: boolean
}

const RangePicker: FC<IRangePickerProps> = (props) => {
  const {
    canEmpty = true,
    children,
    className,
    displayMode = 'layer',
    direction = 'ltr',
    format: propsFormat,
    isLongTerm = false,
    picker = PickerMode.Date,
    popupPlacement = 'bottomLeft',
    onCancel,
    onChange,
    onOk,
    onPopupVisibleChange,
    showClear,
    showLongTerm = false,
    showPreviousCycle = false,
    showNextCycle,
    showTime,
    value: propsValue,
    // 兼容老代码
    // @ts-ignore
    values: propsValues,
    visible = false,
    showFooter = true,
    defaultSettingType,
    ...restProps
  } = props

  let values = propsValue || propsValues
  if (typeof values === 'object' && !Array.isArray(propsValue)) {
    // @ts-ignore
    values = values.isLong ? [values.dateRange[0]] : values.dateRange
  }

  const format = useMemo(() => {
    if (propsFormat) {
      return propsFormat
    }
    if (picker === PickerMode.Date) {
      if (showTime) return PickerDateTimeFormat
      else return PickerDateFormat
    }
    if (picker === PickerMode.Month) return PickerMonthFormat
    if (picker === PickerMode.Year) return PickerYearFormat
    return PickerDateFormat
  }, [])

  const [popupVisible, setPopupVisible] = useState(visible)

  const onPopupVisibleChangeHandler = (visible: boolean) => {
    setPopupVisible(visible)
    onPopupVisibleChange?.(visible)
  }

  // 取消操作
  const onCancelHandler = () => {
    onCancel?.()
    setPopupVisible(false)
  }

  useEffect(() => {
    setPopupVisible(visible)
  }, [visible])

  const zIndex = useZindex([popupVisible])
  useEffect(() => {
    if (!popupVisible) {
      setTimeout(() => {
        setPopupContentKey((key) => key + 1)
      }, 100)
    }
  }, [popupVisible])
  const [popupContentKey, setPopupContentKey] = useState(0)

  return (
    <RangePickerContext.Provider
      value={{
        // @ts-ignore
        values,
        picker,
        format,
      }}
    >
      <ConditionRender condition={displayMode === 'layer'}>
        {/* @ts-ignore */}
        <Popup
          destroyPopupOnHide
          clearPadding
          extraClass={classNames(PickerDropdownClass)}
          popup={
            <PopupContent
              {...props}
              zIndex={zIndex}
              key={popupContentKey}
              onCancel={onCancelHandler}
            />
          }
          popupVisible={popupVisible}
          popupPlacement={getPopupPlacement(direction, popupPlacement)}
          onPopupVisibleChange={onPopupVisibleChangeHandler}
          zIndex={zIndex}
          {...restProps}
        >
          <RangePickerTrigger
            focused={popupVisible}
            format={format}
            placeholder={getLocale('Rep_NewS_All')}
            // @ts-ignore
            viewDate={values}
            {...restProps}
          >
            {children}
          </RangePickerTrigger>
        </Popup>
      </ConditionRender>
      <ConditionRender condition={displayMode === 'tiled'}>
        <PopupContent
          {...props}
          zIndex={zIndex}
          onCancel={onCancelHandler}
        />
      </ConditionRender>
    </RangePickerContext.Provider>
  )
}

export default RangePicker
