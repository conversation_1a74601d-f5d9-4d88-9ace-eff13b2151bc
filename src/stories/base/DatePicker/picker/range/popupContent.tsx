import { useRefState } from '@tita/hooks'
import classNames from 'classnames'
import cloneDeep from 'lodash/cloneDeep'
import moment from 'moment'
import React, { FC, useEffect, useMemo } from 'react'
import styled from 'styled-components'
import { IRangePickerProps } from '.'
import ConditionRender from '../components/condition-render'
import DateInputGroup from '../components/date-input-group'
import TimePicker from '../components/panels/date-time-panel/time-picker'
import RangePickerOperationBar from '../components/range-picker-operation-bar'
import Shortcuts from '../components/shortcuts'
import { PickerMode } from '../enum'
import Picker from '../picker'
import { PickerValues } from '../type'
import {
  PickerDateFormat,
  PickerDateTimeFormat,
  PickerMonthFormat,
  PickerRangesClass,
  PickerTimeFormat,
  PickerYearFormat,
  RangeSettingTypes,
} from '../utils'
import './index.scss'

const getIndexBySettingType = (settingType: RangeSettingTypes) =>
  settingType === RangeSettingTypes.START ? 0 : 1

export interface IPopupContentProps extends IRangePickerProps {
  zIndex: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

export const PopupContent: FC<IPopupContentProps> = React.memo((props) => {
  const {
    canEmpty = true,
    children,
    className,
    style,
    displayMode = 'layer',
    direction = 'ltr',
    format: propsFormat,
    isLongTerm = false,
    picker = PickerMode.Date,
    popupPlacement = 'bottomLeft',
    onCancel,
    onChange,
    onOk,
    onPopupVisibleChange,
    showClear,
    showLongTerm = false,
    showPreviousCycle = false,
    showNextCycle,
    showTime,
    value: propsValue,
    // 兼容老代码
    // @ts-ignore
    values: propsValues,
    visible = false,
    showFooter = true,
    defaultSettingType,
    zIndex,
    defaultTimes,
    ...restProps
  } = props

  let value = cloneDeep(propsValue || propsValues)
  let values = propsValue || propsValues
  if (typeof values === 'object' && !Array.isArray(propsValue)) {
    // @ts-ignore
    values = values.isLong ? [values.dateRange[0]] : values.dateRange
  }

  const format = useMemo(() => {
    if (propsFormat) {
      return propsFormat
    }
    if (picker === PickerMode.Date) {
      if (showTime) return PickerDateTimeFormat
      else return PickerDateFormat
    }
    if (picker === PickerMode.Month) return PickerMonthFormat
    if (picker === PickerMode.Year) return PickerYearFormat
    return PickerDateFormat
  }, [])

  // @ts-ignore
  const [isLong, isLongRef, setIsLong] = useRefState<boolean>(
    isLongTerm || value?.isLong
  )
  const [settingType, settingTypeRef, setSettingType] =
    useRefState<RangeSettingTypes>(
      // @ts-ignore
      defaultSettingType || RangeSettingTypes.START
    )
  const [date, dateRef, setDate] = useRefState<PickerValues>([
    moment(values?.[0]),
    moment(values?.[1]),
  ])
  const [time, timeRef, setTime] = useRefState<string[]>([
    values?.[0]
      ? moment(values?.[0]).format(PickerTimeFormat)
      : defaultTimes?.[0] || moment().format(PickerTimeFormat),
    values?.[1]
      ? moment(values?.[1]).format(PickerTimeFormat)
      : defaultTimes?.[1] || moment().format(PickerTimeFormat),
  ])
  const viewDate = useMemo(() => {
    return [
      (date[0] &&
        moment(
          `${moment(date[0]).format(PickerDateFormat)} ${
            showTime ? time[0] : ''
          }`
        )) ||
        undefined,
      (!isLong &&
        date[1] &&
        moment(
          `${moment(date[1]).format(PickerDateFormat)} ${
            showTime ? time[1] : ''
          }`
        )) ||
        undefined,
    ]
  }, [date, time, isLong])

  // 日期修改
  const onDateChangeHandler = (values: PickerValues) => {
    const isLong = isLongRef.current
    const date = values[0]

    const index = getIndexBySettingType(settingTypeRef.current)
    let nextDate = [...dateRef.current]
    nextDate[index] = date

    let newSettingType = settingTypeRef.current

    if (!isLong) {
      if (settingTypeRef.current === RangeSettingTypes.START) {
        // 如果没有结束时间，则定位到结束时间
        if (!nextDate[1]) {
          newSettingType = RangeSettingTypes.END
        }
        // 如果新选择的【开始日期】大于【结束日期】
        else if (moment(nextDate[0]).isAfter(moment(nextDate[1]))) {
          // 如果当前【开始日期】不为空，则清空并定位到【结束日期】
          if (dateRef.current[0]) {
            delete nextDate[1]
            newSettingType = RangeSettingTypes.END
          }
          // 如果当前【开始日期】为空，则交换新选择的【开始日期】和【结束日期】
          else {
            const temp = nextDate[0]
            nextDate[0] = nextDate[1]
            nextDate[1] = temp
          }
        }
      } else {
        // 如果没有开始时间，则定位到开始时间
        if (!nextDate[0]) {
          newSettingType = RangeSettingTypes.START
        }
        // 如果新选择的【结束日期】小于【开始日期】
        else if (moment(nextDate[1]).isBefore(moment(nextDate[0]))) {
          // 如果当前【结束日期】不为空，则清空并定位到【开始日期】
          if (dateRef.current[1]) {
            delete nextDate[0]
            newSettingType = RangeSettingTypes.START
          }
          // 如果当前【结束日期】为空，则交换新选择的【开始日期】和【结束日期】
          else {
            const temp = nextDate[0]
            nextDate[0] = nextDate[1]
            nextDate[1] = temp
          }
        }
      }
    }

    // 如果开始日期和结束日期是同一天，需要判断时间是否冲突
    if (moment(nextDate[0]).isSame(nextDate[1], 'day')) {
      const time = timeRef.current
      let [startHourStr, startMinuteStr] = time[0].split(':')
      let [endHourStr, endMinuteStr] = time[1].split(':')
      const [startHour, startMinute] = [
        Number(startHourStr),
        Number(startMinuteStr),
      ]
      const [endHour, endMinute] = [Number(endHourStr), Number(endMinuteStr)]

      if (settingTypeRef.current === RangeSettingTypes.START) {
        if (startHour > endHour) {
          startHourStr = endHourStr
          if (startMinute > endMinute) {
            startMinuteStr = endMinuteStr
          }
        }
        if (startHour === endHour && startMinute > endMinute) {
          startMinuteStr = endMinuteStr
        }
      } else {
        if (startHour > endHour) {
          endHourStr = startHourStr
          if (startMinute > endMinute) {
            endMinuteStr = startMinuteStr
          }
        }
        if (startHour === endHour && startMinute > endMinute) {
          endMinuteStr = startMinuteStr
        }
      }
      setTime([
        `${startHourStr}:${startMinuteStr}`,
        `${endHourStr}:${endMinuteStr}`,
      ])
    }

    if (!showFooter && nextDate.filter(Boolean).length === 2) {
      onConfirmHandler(nextDate)
    }

    setDate(nextDate)
    setTimeout(() => {
      setSettingType(newSettingType)
    }, 100)
    return true
  }

  // 时间修改
  const onTimeChangeHandler = (time: string) => {
    const newtime = [...timeRef.current]
    newtime[settingTypeIndex] = time
    setTime(newtime)
  }

  // 快捷键操作
  const onShortcutsDateChange = (date: PickerValues) => {
    setIsLong(false)
    setDate(date)
    setSettingType(RangeSettingTypes.RESET)
    if (!showFooter) {
      onConfirmHandler(date)
    }
  }

  // 清空操作
  const onClearHandler = () => {
    setDate(['', ''])
    setSettingType(RangeSettingTypes.START)
  }

  // 取消操作
  const onCancelHandler = () => {
    onCancel?.()
  }

  // 确认操作
  const onConfirmHandler = async (newViewDate: PickerValues) => {
    const newDate = (newViewDate || viewDate).map((date) =>
      date ? moment(date).format(format) : date
    )
    const params = isLong
      ? {
          isLong: true,
          dateRange: [
            newDate[0],
            moment(newDate[0])
              .add(4 * 365, 'days')
              .format(format),
          ],
        }
      : newDate
    if (onOk) {
      const okResult = await onOk?.(params)
      okResult !== false && onCancelHandler()
    }
    const result = await onChange(params)
    result !== false && onCancelHandler()
  }

  const getPickerSettingType = () => {
    if (isLong) {
      setSettingType(RangeSettingTypes.START)
      return
    }
    if (defaultSettingType) {
      setSettingType(
        defaultSettingType === 'startDate'
          ? RangeSettingTypes.START
          : RangeSettingTypes.END
      )
      return
    }
    let _settingType = RangeSettingTypes.START
    if (values instanceof Array) {
      const startDate = values[0] || ''
      const endDate = values[1] || ''
      if (!startDate) {
        _settingType = RangeSettingTypes.START
      } else if (!endDate) {
        _settingType = RangeSettingTypes.END
      } else {
        _settingType = RangeSettingTypes.RESET
      }
    }
    setSettingType(_settingType)
  }

  useEffect(() => {
    getPickerSettingType()
  }, [])

  useEffect(() => {
    settingTypeRef.current = settingType
  }, [settingType])

  const settingTypeIndex = useMemo(() => {
    let typeIndex = 0
    if (settingType === RangeSettingTypes.END) {
      typeIndex = 1
    }
    return typeIndex
  }, [settingType])

  let okBtnDisabled = canEmpty
    ? viewDate.length === 1 || viewDate.filter((v) => !v).length === 1
    : viewDate.filter((v) => !!v).length !== 2

  if (isLong) okBtnDisabled = !viewDate[0]
  const dateTime = viewDate[settingTypeIndex]
    ? moment(viewDate[settingTypeIndex])
    : ''

  // 判断开始时间和结束时间是否是同一天
  const isSameDay = moment(viewDate[0]).isSame(viewDate[1], 'day')

  return (
    <PopupContentStyle className={className} style={style}>
      <div className={classNames(PickerRangesClass, className)}>
        <DateInputGroup
          format={format}
          isLong={isLong}
          onChange={setDate}
          // @ts-ignore
          onFocus={setSettingType}
          settingDateType={settingType}
          // @ts-ignore
          viewDate={viewDate}
          zIndex={zIndex + 1}
        />
        <div className={classNames(`${PickerRangesClass}__main`)}>
          {(showPreviousCycle || showNextCycle) && (
            <Shortcuts
              onChange={onShortcutsDateChange}
              showPreviousCycle={showPreviousCycle}
              showNextCycle={showNextCycle}
            />
          )}
          <Picker
            // @ts-ignore
            onChange={onDateChangeHandler}
            picker={picker}
            settingType={settingType}
            {...restProps}
            // @ts-ignore
            value={viewDate}
            displayMode='tiled'
            multiple
          />
          {showTime && (
            <TimePicker
              {...restProps}
              onChange={onTimeChangeHandler}
              value={dateTime ? dateTime.format('HH:mm') : ''}
              disabledOption={
                (isSameDay && {
                  settingType,
                  time,
                }) ||
                undefined
              }
            />
          )}
        </div>
        <ConditionRender condition={showFooter}>
          <RangePickerOperationBar
            showLong={showLongTerm}
            showClear={showClear}
            isLong={isLong}
            onSelectLong={(value) => {
              if (!value) setSettingType(RangeSettingTypes.END)
              else setSettingType(RangeSettingTypes.START)
              setDate([date[0]])
              setIsLong(value)
            }}
            disabled={okBtnDisabled}
            onCancel={onCancelHandler}
            onClear={onClearHandler}
            // @ts-ignore
            onOk={() => onConfirmHandler()}
          />
        </ConditionRender>
      </div>
    </PopupContentStyle>
  )
})

const PopupContentStyle = styled.div``

export default PopupContent
