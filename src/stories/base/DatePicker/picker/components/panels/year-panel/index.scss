.tita-ui__picker-panel__year {
  display: grid;
  grid-template: auto 1fr / 100%;
  gap: 17px;

  .tita-ui__picker-head {

    &__view {
      text-align: center;
    }
  }

  .tita-ui__picker-body {
    height: 178px;
    display: grid;
    grid-template: repeat(4, 1fr) / repeat(3, 1fr);
    gap: 22px 42px;
  }

  .tita-ui__picker-cell {
    width: 52px;
    height: 28px;
    padding: 5px 0;
    border-radius: 8px;
    transition: all .3s ease;
    background-color: transparent;
    border: none;
    box-sizing: border-box;

    &--this-year,
    &:hover {
      background-color: #f0f4fa;
      color: #2879ff;
    }

    &--in-view {
      color: #141C28;
    }

    &--selected {
      background-color: #2879ff;
      color: #ffffff;

      &:hover {
        background-color: #5c8eff;
        color: #ffffff;
      }
    }

    &:disabled {
      display: none;
    }
  }
}
