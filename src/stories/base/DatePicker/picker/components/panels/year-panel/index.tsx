import React, { FC } from 'react'
import classNames from 'classnames'
import moment, { Moment } from 'moment'
import { PickerValue, TOnSelectFn } from '../../../type'
import {
  PickerPanelClass,
  YEAR_DECADE_COUNT,
} from '../../../utils'
import YearPanelBody from './year-body'
import YearPanelHead from './year-head'
import './index.scss'

interface IYearPanelProps {
  viewDate: PickerValue
  onSelect: TOnSelectFn
  onViewDateChange: (date: Moment) => void
}

const YearPanel: FC<IYearPanelProps> = (props) => {
  const { onViewDateChange, viewDate } = props

  const yearNumber = moment(viewDate).year()
  const startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT
  const endYear = startYear + YEAR_DECADE_COUNT - 1

  const onDecadeYearChange = (diff: any) => {
    if (onViewDateChange) {
      onViewDateChange(moment(viewDate).add(diff, 'y'))
    }
  }

  const extendProps = { ...{ ...props, startYear, endYear } }

  return (
    <div className={classNames(`${PickerPanelClass}__year`)}>
      <YearPanelHead
        {...extendProps}
        onPrevDecadeYear={() => onDecadeYearChange(-YEAR_DECADE_COUNT)}
        onNextDecadeYear={() => onDecadeYearChange(YEAR_DECADE_COUNT)}
      />
      <YearPanelBody {...extendProps} />
    </div>
  )
}

export default YearPanel
