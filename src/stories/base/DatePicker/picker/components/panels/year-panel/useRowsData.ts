import { useEffect, useState } from 'react'
import { YEAR_DECADE_COUNT } from '../../../utils'

const YEAR_ROW_NUM = 4
const YEAR_COL_NUM = 3

function useRowsData(startYear: number) {
  const [rowsData, setRowsData] = useState<number[][]>([])

  const getCells = (baseYear: number) => {
    const cols: any[] = []
    for (let col = 0; col < YEAR_COL_NUM; col += 1) {
      const year = baseYear + col
      cols.push(year)
    }
    return cols
  }

  const getRows = () => {
    let baseYear =
      startYear - (YEAR_COL_NUM * YEAR_ROW_NUM - YEAR_DECADE_COUNT) / 2
    const rows: any[] = []
    for (let row = 0; row < YEAR_ROW_NUM; row += 1) {
      rows.push(getCells(baseYear))
      baseYear += YEAR_COL_NUM
    }
    return rows
  }

  useEffect(() => {
    setRowsData(getRows())
  }, [startYear])

  return rowsData
}

export default useRowsData
