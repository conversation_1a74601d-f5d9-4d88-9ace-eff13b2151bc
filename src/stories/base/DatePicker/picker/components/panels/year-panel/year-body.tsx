import React, { FC, useContext } from 'react'
import classNames from 'classnames'
import moment from 'moment'
import Picker<PERSON>ontext from '../../../context'
import { PickerMode } from '../../../enum'
import { PickerValue, TOnSelectFn } from '../../../type'
import {
  PickerBodyClass,
  PickerCellClass,
  getPickerDateBySettingType,
} from '../../../utils'
import useRowsData from './useRowsData'

interface IYearPanelBodyProps {
  viewDate: PickerValue
  startYear: number
  endYear: number
  onSelect: TOnSelectFn
}

const YearPanelBody: FC<IYearPanelBodyProps> = (props) => {
  const { viewDate = moment(), onSelect, startYear, endYear } = props
  const { format, max, min, settingType } = useContext(PickerContext)
  const maxYear = moment(max).year()
  const minYear = moment(min).year()
  const rowsData = useRowsData(startYear)
  const date = getPickerDateBySettingType({
    format,
    settingType,
    values: [viewDate],
  })

  return (
    <div className={classNames(PickerBodyClass)}>
      {rowsData.map((row) =>
        row.map((year) => (
          <button
            className={classNames(PickerCellClass, {
              [`${PickerCellClass}--this-year`]: year === moment().year(),
              [`${PickerCellClass}--in-view`]:
                year >= startYear && year <= endYear,
              [`${PickerCellClass}--selected`]: year === moment(date).year(),
            })}
            disabled={year < minYear || year > maxYear}
            key={year}
            onClick={() => onSelect?.(moment(viewDate).year(year), PickerMode.Year)}
          >
            {year}
          </button>
        )),
      )}
    </div>
  )
}

export default YearPanelBody
