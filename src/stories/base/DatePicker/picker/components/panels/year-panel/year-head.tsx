import React, { FC, useContext } from 'react'
import classNames from 'classnames'
import moment from 'moment'
import Picker<PERSON>ontext from '../../../context'
import { PickerValue } from '../../../type'
import { PickerHeadClass } from '../../../utils'
import PanelHead from '../panel-head'

interface IYearPanelHeadProps {
  viewDate: PickerValue
  startYear: number
  endYear: number
  onPrevDecadeYear: () => void
  onNextDecadeYear: () => void
}

const YearPanelHead: FC<IYearPanelHeadProps> = (props) => {
  const { onPrevDecadeYear, onNextDecadeYear, startYear, endYear } = props
  const { max, min } = useContext(PickerContext)
  const maxYear = moment(max).year()
  const minYear = moment(min).year()

  return (
    <PanelHead
      {...props}
      prevBtnDisabled={startYear <= minYear}
      nextBtnDisabled={endYear >= maxYear}
      onPrev={onPrevDecadeYear}
      onNext={onNextDecadeYear}
    >
      <button
        className={classNames(
          `${PickerHeadClass}-btn__year`,
          `${PickerHeadClass}-btn--readonly`,
        )}
        type="button"
      >
        {`${startYear} - ${endYear}`}
      </button>
    </PanelHead>
  )
}

export default YearPanelHead
