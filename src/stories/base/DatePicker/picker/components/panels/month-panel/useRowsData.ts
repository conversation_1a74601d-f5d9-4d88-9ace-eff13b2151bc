import { useEffect, useState } from 'react'

const MONTH_ROW_NUM = 4
const MONTH_COL_NUM = 3

function useRowsData() {
  const [rowsData, setRowsData] = useState<number[][]>([])

  const getCells = (baseMonth: number) => {
    const cols: any[] = []
    for (let col = 0; col < MONTH_COL_NUM; col += 1) {
      const month = col + baseMonth
      cols.push(month)
    }
    return cols
  }

  const getRows = () => {
    const rows: any[] = []
    let baseMonth = 0
    for (let row = 0; row < MONTH_ROW_NUM; row += 1) {
      rows.push(getCells(baseMonth))
      baseMonth += MONTH_COL_NUM
    }
    return rows
  }

  useEffect(() => {
    setRowsData(getRows())
  }, [])

  return rowsData
}

export default useRowsData
