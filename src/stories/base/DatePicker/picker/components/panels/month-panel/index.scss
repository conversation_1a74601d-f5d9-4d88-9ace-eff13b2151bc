.tita-ui__picker-panel__month {
  display: grid;
  grid-template: auto 1fr / 100%;
  gap: 17px;

  .tita-ui__picker-head {

    &__view {
      text-align: center;
    }
  }

  .tita-ui__picker-body {
    display: grid;
    grid-template: repeat(4, 1fr) / repeat(3, 1fr);
    gap: 22px 42px;
  }

  .tita-ui__picker-cell {
    width: 52px;
    height: 28px;
    padding: 5px 0;
    border-radius: 8px;
    transition: all .3s ease;
    background-color: transparent;
    border: none;
    box-sizing: border-box;
    color: #141c28;

    &--this-month,
    &:hover {
      background-color: #f0f4fa;
      color: #2879ff;
    }

    &--selected {
      background-color: #2879ff;
      color: #ffffff;

      &:hover {
        background-color: #5c8eff;
        color: #ffffff;
      }
    }
  }
}
