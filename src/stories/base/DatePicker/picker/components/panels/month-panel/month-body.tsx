import React, { <PERSON> } from 'react'
import classNames from 'classnames'
import moment from 'moment'
import { PickerMode } from '../../../enum'
import { PickerValue, TOnSelectFn } from '../../../type'
import {
  PickerBodyClass,
  PickerCellClass,
  MONTHDATAMAP,
  MONTHDATAMAPEN,
  getDaysOfMonth,
} from '../../../utils'
import useRowsData from './useRowsData'
import { isEn } from '@tita/utils'
interface IMonthPanelBodyProps {
  viewDate: PickerValue
  onSelect: TOnSelectFn
}

const MonthPanelBody: FC<IMonthPanelBodyProps> = (
  props,
) => {
  const { viewDate, onSelect } = props
  const rowsData = useRowsData()

  return (
    <div className={classNames(PickerBodyClass)}>
      {rowsData.map((row) =>
        row.map((month) => (
          <button
            className={classNames(
              PickerCellClass,
              `${PickerCellClass}--in-view`,
              {
                [`${PickerCellClass}--this-month`]: month === moment().month(),
                [`${PickerCellClass}--selected`]:
                  month === moment(viewDate).month(),
              },
            )}
            key={month}
            onClick={() => onSelect?.(moment(viewDate).month(month).date(getDaysOfMonth(moment(viewDate).year(), month + 1)), PickerMode.Month)}
          >
            {isEn ? MONTHDATAMAPEN[month] : MONTHDATAMAP[month]}
          </button>
        )),
      )}
    </div>
  )
}

export default MonthPanelBody
