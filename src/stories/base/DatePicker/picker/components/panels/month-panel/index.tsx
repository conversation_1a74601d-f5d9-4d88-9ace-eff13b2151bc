import React, { FC, useEffect, useState } from 'react'
import classNames from 'classnames'
import moment, { Moment } from 'moment'
import { PickerMode } from '../../../enum'
import { PickerValue, TOnSelectFn } from '../../../type'
import { PickerPanelClass } from '../../../utils'
import MonthPanelBody from './month-body'
import MonthPanelHead from './month-head'
import './index.scss'

interface IMonthPanelProps {
  viewDate: PickerValue
  onPanelChange: (date: any, type: any) => void
  onViewDateChange: (date: Moment) => void
  onSelect: TOnSelectFn
}

const MonthPanel: FC<IMonthPanelProps> = (props) => {
  const { viewDate, onPanelChange, onViewDateChange, ...restProps } = props

  const [newViewDate, setNewViewDate] = useState<PickerValue>(viewDate)

  const onYearChange = (diff: number) => {
    if (onViewDateChange) {
      onViewDateChange(moment(newViewDate).add(diff, 'y'))
      setNewViewDate(moment(newViewDate).add(diff, 'y'))
    }
  }

  useEffect(() => {
    if (typeof viewDate === 'string') {
      setNewViewDate(viewDate.split('~')[0])
    } else {
      setNewViewDate(viewDate)
    }
  }, [])

  return (
    <div className={classNames(`${PickerPanelClass}__month`)}>
      <MonthPanelHead
        {...restProps}
        viewDate={newViewDate}
        onPrevYear={() => {
          onYearChange(-1)
        }}
        onNextYear={() => {
          onYearChange(1)
        }}
        onYearClick={() => {
          onPanelChange?.(newViewDate, PickerMode.Year)
        }}
      />
      <MonthPanelBody viewDate={viewDate} {...restProps} />
    </div>
  )
}

export default MonthPanel
