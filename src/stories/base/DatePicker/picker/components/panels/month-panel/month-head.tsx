import React, { FC, useContext } from 'react'
import classNames from 'classnames'
import moment from 'moment'
import Picker<PERSON>ontext from '../../../context'
import { PickerValue } from '../../../type'
import { PickerHeadClass } from '../../../utils'
import PanelHead from '../panel-head'

interface MonthPanelHeadProps {
  viewDate: PickerValue
  onPrevYear: () => void
  onNextYear: () => void
  onYearClick: () => void
}

const MonthPanelHead: FC<MonthPanelHeadProps> = (
  props,
) => {
  const {
    viewDate = moment(),
    onPrevYear,
    onNextYear,
    onYearClick,
    ...restProps
  } = props
  const { max, min } = useContext(PickerContext)
  const maxYear = moment(max).year()
  const minYear = moment(min).year()
  const year = moment(viewDate).year()

  return (
    <PanelHead
      {...restProps}
      prevBtnDisabled={year === minYear}
      nextBtnDisabled={year === maxYear}
      onPrev={onPrevYear}
      onNext={onNextYear}
    >
      <button
        className={classNames(`${PickerHeadClass}-btn__year`)}
        onClick={onYearClick}
        type="button"
      >
        {year}
      </button>
    </PanelHead>
  )
}

export default MonthPanelHead
