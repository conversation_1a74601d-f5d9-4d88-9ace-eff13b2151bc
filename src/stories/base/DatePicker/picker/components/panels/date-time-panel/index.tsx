import React, { FC, useContext, useState } from 'react'
import moment from 'moment'
import But<PERSON> from '../../../../../Button'
import Picker from '../../../picker'
import PickerContext from '../../../context'
import { PickerValue } from '../../../type'
import { PickerPanelClass } from '../../../utils'
import TimePicker, { ITimePickerProps } from './time-picker'
import './index.scss'

interface Props extends Partial<ITimePickerProps> {
  viewDate: PickerValue
  onCancel: () => void
  onChange: (viewDate: PickerValue) => void
}

const prefixCls = `${PickerPanelClass}__date-time`
const FORMAT = 'YYYY/MM/DD HH:mm'
const DateTimePanel: FC<Props> = (props) => {
  const { onCancel, onChange, viewDate, ...restProps } = props
  const { dateTimeStep, defaultTime = '00:00', disabledDate, max, min, value } = useContext(PickerContext)

  const getDateTime = (originDateTime: string) => {
    const [date, time] = originDateTime.split(' ')
    return {
      date,
      time: value[0] ? time : defaultTime
    }
  };

  const [dateTime, setDateTime] = useState(getDateTime(moment(viewDate).format(FORMAT)))

  const onDateTimeChange = async(
    key: 'date' | 'time',
    value: PickerValue|string,
  ) => {
    setDateTime((prevData) => ({...prevData, [key]: value}))
    return true
  }

  const onConfirm = () => onChange(`${dateTime.date} ${dateTime.time}`)

  return (
    <section className={prefixCls}>
      <main className={`${prefixCls}__main`}>
        <Picker
          {...{ disabledDate, max, min }}
          displayMode="tiled"
          onChange={(date) => onDateTimeChange('date', date[0])}
          value={[dateTime.date]}
        />
        <TimePicker
          {...restProps}
          onChange={(time) => onDateTimeChange('time', time)}
          timeStep={dateTimeStep}
          value={dateTime.time}
        />
      </main>
      <footer className={`${prefixCls}__footer`}>
        <Button onClick={onCancel} size="small">
          取消
        </Button>
        <Button onClick={onConfirm} size="small" primary>
          确定
        </Button>
      </footer>
    </section>
  )
}

export default DateTimePanel
