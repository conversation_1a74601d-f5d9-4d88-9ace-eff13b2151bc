import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import classNames from 'classnames'
import cloneDeep from 'lodash/cloneDeep'
import { FixedSizeList } from 'react-window'
import { IDateTimeStep } from '../../../type'
import {
  getHours,
  getMinutes,
  PickerPanelClass,
  RangeSettingTypes,
} from '../../../utils'
import './index.scss'
import TitaScroll, { ITitaScrollRef } from '@/stories/base/TitaScroll'

export interface ITimePickerProps {
  timeStep?: IDateTimeStep
  value: string
  onChange: (value: string) => void
  /** labelOption */
  labelOption?: {
    hour: string
    minute: string
  }
  disabledOption?: {
    settingType: RangeSettingTypes
    time: string[]
  }
  className?: string
}

interface IPickerItemProps {
  data: string[]
  label: string
  value: number
  onSelect: (time: string) => void
  disabled?: (time: string) => boolean
}

const prefixCls = `${PickerPanelClass}__time`
const TIME_ITEM_HEIGHT = 28
const TIME_ROW_NUM = 8
const TIME_SECTION_WIDTH = 64

const PickerItem: FC<IPickerItemProps> = (props) => {
  const { data, label, onSelect, value, disabled } = props
  const dataSize = data.length
  const listRef = useRef<ITitaScrollRef>(null)

  const scrollToIndex = useMemo(
    () => data.findIndex((item) => Number(item) == value),
    [data, value]
  )

  const scrollToIndexHandler = () => {
    listRef.current?.scrollToChild(
      `li:contains('${value.toString().padStart(2, '0')}')`,
      TIME_ROW_NUM / 2 * TIME_ITEM_HEIGHT - TIME_ITEM_HEIGHT / 2
    )
  }

  useEffect(() => {
    scrollToIndexHandler()
  }, [scrollToIndex, value])

  return (
    <section className={`${prefixCls}__section`}>
      <label className={`${prefixCls}__label`}>{label}</label>
      <TitaScroll
        ref={listRef}
        height={TIME_ITEM_HEIGHT * TIME_ROW_NUM}
        shadowTheme='white'
        barWidth={4}
        colBarInset
        colBarOffset={{
          x: 4,
        }}
      >
        <ul className={`${prefixCls}__groups`}>
          {data.map((rowData) => {
            const isDisabled = disabled?.(rowData)
            const key = rowData
            return (
              <li
                className={classNames(`${prefixCls}__item`, {
                  [`${prefixCls}__item--activated`]: value == Number(rowData),
                  [`${prefixCls}__item--disabled`]: isDisabled,
                })}
                key={key}
                onClick={() => {
                  if (isDisabled) return
                  onSelect(rowData)
                }}
              >
                {rowData}
              </li>
            )
          })}
        </ul>
      </TitaScroll>
    </section>
  )
}
const TimePicker: FC<ITimePickerProps> = (props) => {
  const {
    timeStep: { hour: hourStep = 1, minute: minuteStep = 1 } = {},
    onChange,
    value,
    labelOption,
    className,
    disabledOption,
  } = props

  const getTime = (originTime: string) => {
    if (!originTime)
      return {
        hour: '',
        minute: '',
      }
    const [hour, minute] = originTime.split(':')
    return {
      hour,
      minute,
    }
  }

  const [time, setTime] = useState(getTime(value))

  const hoursData = useMemo(() => getHours(hourStep), [])
  const minutesData = useMemo(() => getMinutes(minuteStep), [])

  const onSelectHandler = (key: 'hour' | 'minute', newValue: string) => {
    setTime((prevTime) => {
      const nextTime = cloneDeep(prevTime)
      nextTime[key] = newValue
      onChange(
        `${
          nextTime.hour === 'Invalid Date' || !nextTime.hour
            ? '00'
            : nextTime.hour
        }:${nextTime.minute}`
      )
      return nextTime
    })
  }

  useEffect(() => {
    setTime(getTime(value))
  }, [value])

  const getHourDisabled = useMemo(() => {
    if (!disabledOption) return undefined

    const { settingType, time } = disabledOption
    if (settingType === RangeSettingTypes.START) {
      const [endHour] = time[1].split(':')
      return (hour: string) => {
        // 如果大于当前结束时间，则禁用
        return Number(hour) > Number(endHour)
      }
    } else {
      const [startHour] = time[0].split(':')
      return (hour: string) => {
        // 如果小于当前开始时间，则禁用
        return Number(hour) < Number(startHour)
      }
    }
  }, [disabledOption])

  const getMinuteDisabled = useMemo(() => {
    if (!disabledOption) return undefined

    const { settingType, time } = disabledOption
    const [startHour, startMinute] = time[0].split(':')
    const [endHour, endMinute] = time[1].split(':')
    if (settingType === RangeSettingTypes.START) {
      return (minute: string) => {
        // 如果小于当前结束时间，则不禁用
        if (Number(startHour) < Number(endHour)) return false
        // 如果小时数相等，则判断分钟数
        if (Number(startHour) === Number(endHour)) {
          return Number(minute) > Number(endMinute)
        }
        return false
      }
    } else {
      return (minute: string) => {
        // 如果大于当前开始时间，则不禁用
        if (Number(endHour) > Number(startHour)) return false
        // 如果小时数相等，则判断分钟数
        if (Number(endHour) === Number(startHour)) {
          return Number(minute) < Number(startMinute)
        }
        return false
      }
    }
  }, [disabledOption, hoursData])

  return (
    <div className={classNames(prefixCls, className)}>
      <PickerItem
        data={hoursData}
        label={(labelOption && labelOption['hour']) || '时'}
        onSelect={(data) => onSelectHandler('hour', data)}
        value={Number(time.hour)}
        disabled={getHourDisabled}
      />
      <PickerItem
        data={minutesData}
        label={(labelOption && labelOption['minute']) || '分'}
        onSelect={(data) => onSelectHandler('minute', data)}
        value={Number(time.minute)}
        disabled={getMinuteDisabled}
      />
    </div>
  )
}

export default TimePicker
