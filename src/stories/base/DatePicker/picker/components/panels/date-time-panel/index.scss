.tita-ui__picker-panel__date-time {

  &__main {
    display: flex;
  }

  &__footer {
    margin-top: 6px;
    padding: 12px 32px;
    border-top: 1px solid #E9ECF0;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .titaui-rect-btn {
      margin-left: 12px;
    }

    .titaui-rect-btn--default {
      transition: all 0.3s ease;
      border-color: #DFE3EA;
      color: #6F7886;

      &:hover {
        background-color: transparent;
        border-color: #2879ff;
        color: #2879ff;
      }
    }
  }

  //=============reset style================
  .tita-ui__picker-date {
    padding: 15px 18px 0;
    box-shadow: none;
  }

  .tita-ui__picker-panel__time {
    padding-top: 14px;
  }
}

.tita-ui__picker-panel__time {
  display: flex;

  &__section {
    padding: 15px 4px 0 4px !important;
    position: relative;

    &::before {
      content: '';
      width: 1px;
      background: linear-gradient(180deg, rgba(233, 236, 240, 0) 0%, #E9ECF0 48%, rgba(233, 236, 240, 0) 100%);
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
    }
  }

  &__groups {
    position: relative;
    display: flex;
    flex-direction: column;

    &::before,
    &::after {
      content: "";
      height: 10px;
      display: block;
      pointer-events: none;
      position: absolute;
      right: 0;
      left: 0;
      z-index: 1;
    }

    &::before {
      background: linear-gradient(to bottom, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
      top: 0;
    }

    &::after {
      background: linear-gradient(to top, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
      bottom: 0;
    }
  }

  &__label,
  &__item {
    width: 64px;
    height: 28px;
    line-height: 28px;
    border-radius: 8px;
    transition: all 0.3s ease;
    color: #3F4755;
    display: inline-block;
    text-align: center;

    &--activated {
      background-color: rgba(40, 121, 255, 0.1);
      color: #2879ff;
    }
    &--disabled {
      color: #A4ACB9 !important;
      cursor: not-allowed !important;

      &:hover {
        background-color: transparent !important;
        color: #A4ACB9 !important;
      }
    }
  }

  &__label {
    margin-bottom: 8px;
    font-size: 14px;
    cursor: text;
  }

  &__item {
    font-size: 12px;
    cursor: pointer;

    &:not(&--disabled):hover {
      background-color: #f0f4fa;
      color: #2879ff;
    }
  }

  //=============reset style================
  .ReactVirtualized__List {
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
