import React, { <PERSON> } from "react";
import classNames from "classnames";
import moment from "moment";
import { PickerCellClass } from "../../../utils";

interface IPanelBodyProps {
  colNum: number
  rowNum: number
}

const PanelBody: FC<IPanelBodyProps> = (props) => {
  const { rowNum, colNum } = props;
  const rows: any[] = [];
  let baseMonth = 0;
  for (let row = 0; row < rowNum; row += 1) {
    const cols: any[] = [];
    for (let col = 0; col < colNum; col += 1) {
      const month = col + baseMonth;
      cols.push(
        <td
          className={classNames(
            PickerCellClass,
            `${PickerCellClass}--in-view`,
            {
              [`${PickerCellClass}--this-month`]: month === moment().month(),
            },
          )}
          key={baseMonth}
        >
          <div className={classNames(`${PickerCellClass}__inner`)}>{}</div>
        </td>,
      );
    }
    rows.push(<tr>{cols}</tr>);
    baseMonth += 3;
  }

  return <div />;
};

export default PanelBody;
