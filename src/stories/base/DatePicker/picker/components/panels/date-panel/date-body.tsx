import React, { <PERSON> } from 'react'
import classNames from 'classnames'
import { getLocale } from '@tita/utils'
import { PickerBodyClass, PickerCellClass } from '../../../utils'
import { PickerValue, TOnSelectFn } from '../../../type'
import DatePanelRows from './date-rows'
import './index.scss'

interface IDatePanelBodyProps {
  enableScrollHandler: boolean
  mergeDate: PickerValue
  viewDate: PickerValue
  weekStartDay?: 'MONDAY' | 'SUNDAY'
  onSelect: TOnSelectFn
  setEnableScrollHandler: (enableScrollHandler: boolean) => void
  setMergeDate: (mergeDate: PickerValue) => void
}

const getWeekDayData = () => [
  getLocale('Mod_Sun') || '日',
  getLocale('Mod_Mon') || '一',
  getLocale('Mod_Tu<PERSON>') || '二',
  getLocale('Mod_Wed') || '三',
  getLocale('Mod_Thur') || '四',
  getLocale('Mod_Fri') || '五',
  getLocale('Mod_Sat') || '六',
]
const DatePanelBody: FC<IDatePanelBodyProps> = (props) => {
  const { enableScrollHandler, mergeDate, onSelect, setEnableScrollHandler, setMergeDate, weekStartDay = 'SUNDAY' } = props

  const weekStartDaySun = weekStartDay === 'SUNDAY'

  return (
    <div className={classNames(PickerBodyClass)}>
      <div className={classNames(`${PickerBodyClass}__calendar-header`)}>
        {weekStartDaySun && (
          <div className={PickerCellClass}>{getLocale('Mod_Sun') || '日'}</div>
        )}
        {getWeekDayData()
          .slice(1)
          .map((day) => (
            <div className={PickerCellClass} key={day}>
              {day}
            </div>
          ))}
        {!weekStartDaySun && (
          <div className={PickerCellClass}>{getLocale('Mod_Sun') || '日'}</div>
        )}
      </div>
      <div className={classNames(`${PickerBodyClass}__calendar`)}>
        <DatePanelRows
          enableScrollHandler={enableScrollHandler}
          mergeDate={mergeDate}
          onSelect={onSelect}
          setEnableScrollHandler={setEnableScrollHandler}
          setMergeDate={setMergeDate}
        />
      </div>
    </div>
  )
}

export default DatePanelBody
