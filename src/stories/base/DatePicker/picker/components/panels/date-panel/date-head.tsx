import React, { useContext } from 'react'
import classNames from 'classnames'
import moment from 'moment'
import { getLocale, isEn } from '@tita/utils'
import PickerContext from '../../../context'
import { PickerMode } from '../../../enum'
import { PickerValue, TOnSelectFn } from '../../../type'
import { PickerHeadClass, PickerClass, MONTHDATAMAPEN } from '../../../utils'
import './index.scss'

interface Props {
  viewDate: PickerValue
  onPrev: () => void
  onNext: () => void
  onSelect: TOnSelectFn
  onPanelChange: (viewDate: any, type: any) => void
}

function DatePanelHead(props: Props) {
  const { onPrev, onNext, onPanelChange, onSelect, viewDate = moment() } = props
  const { multiple } = useContext(PickerContext)
  const yearText = moment(viewDate).format('YYYY')
  const monthText = moment(viewDate).format('MM')

  const onClickToday = () => onSelect?.(moment(), PickerMode.Date)

  return (
    <div className={classNames(PickerHeadClass)}>
      {!isEn && (
        <div className={classNames(`${PickerHeadClass}__view`)}>
          <button
            className={classNames(`${PickerHeadClass}-btn__year`)}
            onClick={() => onPanelChange?.(viewDate, PickerMode.Year)}
            type="button"
          >
            {yearText}
          </button>
          <span className={classNames(`${PickerClass}-unit`)}>&nbsp;年</span>
          <button
            className={classNames(`${PickerHeadClass}-btn__month`)}
            onClick={() => onPanelChange?.(viewDate, PickerMode.Month)}
            type="button"
          >
            {monthText}
          </button>
          <span className={classNames(`${PickerClass}-unit`)}>月</span>
        </div>
      )}
      {isEn && (
        <div className={classNames(`${PickerHeadClass}__view`)}>
          <button
            className={classNames(`${PickerHeadClass}-btn__month`)}
            onClick={() => onPanelChange?.(viewDate, PickerMode.Month)}
            style={{ marginRight: 4 }}
            type="button"
          >
            {MONTHDATAMAPEN[Number(monthText) - 1]}
          </button>
          <button
            className={classNames(`${PickerHeadClass}-btn__year`)}
            onClick={() => onPanelChange?.(viewDate, PickerMode.Year)}
            type="button"
          >
            {yearText}
          </button>
        </div>
      )}
      <div className={classNames(`${PickerHeadClass}__operation`)}>
        {!multiple && <button className={classNames(`${PickerHeadClass}-btn__today`)} onClick={onClickToday}>{getLocale('Mod_Today')}</button>}
        <button
          className={classNames(
            `${PickerHeadClass}-btn__prev`,
            'tu-icon-arrow-up1',
          )}
          onClick={onPrev}
        />
        <button
          className={classNames(
            `${PickerHeadClass}-btn__next`,
            'tu-icon-arrow-down1',
          )}
          onClick={onNext}
        />
      </div>
    </div>
  )
}

export default DatePanelHead
