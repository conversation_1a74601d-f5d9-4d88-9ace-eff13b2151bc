import { useEffect, useState } from 'react'
import moment, { Moment } from 'moment'
import { PickerValue } from '../../../type'
import { PickerDateFormat, PickerMaxDate, PickerMinDate, WEEK_DAYS_COUNT } from '../../../utils'

interface IUseRowsDataProps {
  format?: string
  max?: PickerValue
  min?: PickerValue
  weekStartDay?: 'MONDAY' | 'SUNDAY'
}

function useRowsData(props: IUseRowsDataProps): Moment[][] {
  const {
    format = PickerDateFormat,
    max = PickerMaxDate,
    min = PickerMinDate,
    weekStartDay = 'SUNDAY',
  } = props
  const [rowsData, setRowsData] = useState([])

  const getDateCells = (sunday) => {
    const dates: any[] = []
    for (let day = 0; day < WEEK_DAYS_COUNT; day += 1) {
      const date = moment(sunday).add(day, 'd')
      dates.push(date)
    }
    return dates
  }
  const getDateRows = () => {
    const currentDate = moment(min)
    const weeksDiff = moment(moment(max).format(format)).diff(
      moment(min).format(format),
      'weeks',
    )
    const monthFirstDay = currentDate.date(1)

    const daysDiff = weekStartDay === 'SUNDAY' ? 0 : 1
    let weekend = monthFirstDay.subtract(monthFirstDay.day() - daysDiff, 'days')
    const rows: any = []
    for (let row = 0; row < weeksDiff; row += 1) {
      rows.push(getDateCells(weekend))
      weekend = weekend.add(1, 'w')
    }
    return rows
  }

  useEffect(() => {
    setRowsData(getDateRows())
  }, [])

  return rowsData
}

export default useRowsData
