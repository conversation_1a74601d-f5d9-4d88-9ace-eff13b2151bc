import React, {
  FC,
  memo,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import classNames from 'classnames'
import moment from 'moment'
import { FixedSizeList } from 'react-window'
import PickerContext from '../../../context'
import { PickerMode } from '../../../enum'
import { PickerValue, TOnSelectFn } from '../../../type'
import {
  isCurrentDateInRanges,
  PickerBodyClass,
  PickerCellClass,
  PickerDateFormat,
} from '../../../utils'
import useRowsData from './useRowsData'
import { useRefState } from '@tita/hooks'

interface Props {
  enableScrollHandler: boolean
  mergeDate: PickerValue
  onSelect: TOnSelectFn
  setEnableScrollHandler: (enableScrollHandler: boolean) => void
  setMergeDate: (mergeDate: PickerValue) => void
}
const DATE_ROW_NUM = 6
const DATE_CELL_HEIGHT = 32
const DATE_PANEL_WIDTH = 240
const ROW_INDEX_DIFF = 3
const DatePanelRows: FC<Props> = (props) => {
  const { enableScrollHandler, mergeDate, onSelect, setEnableScrollHandler, setMergeDate } = props
  const { disabledDate, format, max, min, renderDate, value } = useContext(PickerContext)
  const listRef = useRef<any>(null)
  const rowsData = useRowsData({
    format,
    max,
    min,
  })
  listRef

  // Gets the row that was first rendered in the viewport
  const scrollToIndex = useMemo(() => moment(mergeDate).diff(moment(min), 'weeks'), [])

  const scrollToIndexHandler = () => {
    if (enableScrollHandler) return
    const activatedDateIndex = moment(mergeDate).diff(moment(min), 'weeks')
    requestAnimationFrame(() => {
      listRef.current.scrollTo(activatedDateIndex * DATE_CELL_HEIGHT, 0)
    })
  }

  const rowRenderer = useCallback(
    ({
      key, // Unique key within array of rows
      index, // Index of row within collection
      data,
      isScrolling, // The List is currently being scrolled
      style,
    }: any) => {
      const rowData = data[index]
      if (!isScrolling && hoverRef.current) {
        setEnableScrollHandler(true)
        setMergeDate(rowsData[index - ROW_INDEX_DIFF]?.[0])
      }
      return (
        <div
          className={classNames(`${PickerBodyClass}__calendar-item`)}
          key={key}
          style={style}
        >
          {rowData.map((date: any) => {
            const currentDate = moment(date)
            const { inRanges, rangeStart, rangeEnd } = isCurrentDateInRanges({
              nowDate: date,
              values: value,
            })
            const disabled = disabledDate?.(currentDate) === true
            const classes = classNames(PickerCellClass, {
              [`${PickerCellClass}--disabled`]: disabled,
              [`${PickerCellClass}--today`]: currentDate.isSame(
                moment(),
                'date',
              ),
              [`${PickerCellClass}--in-view`]:
                currentDate.format('YYYY/MM') ==
                  moment(mergeDate).format('YYYY/MM'),
              [`${PickerCellClass}--in-ranges`]: inRanges,
              [`${PickerCellClass}--in-ranges--start`]: rangeStart,
              [`${PickerCellClass}--in-ranges--end`]: rangeEnd,
              [`${PickerCellClass}--selected`]: value?.some((v) => v &&
                currentDate.isSame(moment(v).format(PickerDateFormat), 'date'),
              ),
            })
            return (
              <div className={classes} key={currentDate.format(PickerDateFormat)}>
                <div
                  className={classNames(`${PickerCellClass}__inner`)}
                  title={moment(currentDate).format('YYYY-MM-DD')}
                  onClick={() => !disabled && onSelect?.(currentDate, PickerMode.Date)}
                >
                  <span>{renderDate?.(currentDate) || moment(currentDate).format('DD')}</span>
                </div>
              </div>
            )
          })}
        </div>
      )
    },
    [mergeDate, rowsData, value],
  )

  useEffect(() => {
    scrollToIndexHandler()
  }, [mergeDate])

  const [hover, hoverRef, setHover] = useRefState(false)

  return (
    <span onMouseOver={() => setHover(true)} onMouseOut={() => setHover(false)}>
      <FixedSizeList
        height={DATE_CELL_HEIGHT * DATE_ROW_NUM}
        ref={listRef}
        itemSize={DATE_CELL_HEIGHT}
        itemCount={rowsData?.length || 0}
        width={DATE_PANEL_WIDTH}
        itemData={rowsData}
      >{rowRenderer}</FixedSizeList>
    </span>
  )
}

export default memo(DatePanelRows)
