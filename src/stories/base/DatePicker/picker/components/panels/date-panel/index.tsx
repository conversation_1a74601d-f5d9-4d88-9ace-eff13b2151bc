import React, { FC, useEffect, useState } from 'react'
import classNames from 'classnames'
import moment, { Moment } from 'moment'
import { PickerValue, TOnSelectFn } from '../../../type'
import { getDaysOfMonth, PickerPanelClass } from '../../../utils'
import DatePanelHead from './date-head'
import DatePanelBody from './date-body'
import './index.scss'

interface IDatePanelProps {
  viewDate: PickerValue
  onPanelChange: (date: Moment, type: any) => void
  onSelect: TOnSelectFn
}

  const getDate = (date: any) => moment(date).date(getDaysOfMonth(moment(date).year(), moment(date).month() + 1))
  const DatePanel: FC<IDatePanelProps> = (props) => {
  const { viewDate } = props
  const [enableScrollHandler, setEnableScrollHandler] = useState(false)
  const [mergeDate, setMergeDate] = useState<PickerValue>(getDate(viewDate))

  // const locateToToday = () => {
  //   setEnableScrollHandler(false)
  //   setMergeDate(moment().date(1))
  // }

  const onMonthChange = (method: 'add' | 'subtract') => {
    setEnableScrollHandler(false)
    setMergeDate((prevDate) => moment(moment(prevDate)[method](1, 'M').date(1)))
  }

  const onSelect = (date: any, type: any) => {
    setMergeDate(date)
    props.onSelect(date, type)
  }

  useEffect(() => {
    setMergeDate(moment(viewDate).date(1))
    setEnableScrollHandler(false)
  }, [viewDate])
    
  return (
    <div className={classNames(`${PickerPanelClass}__date`)}>
      <DatePanelHead
        {...props}
        onPrev={() => onMonthChange('subtract')}
        onNext={() => onMonthChange('add')}
        onSelect={onSelect}
        viewDate={mergeDate}
      />
      <DatePanelBody
        {...props}
        enableScrollHandler={enableScrollHandler}
        mergeDate={mergeDate}
        onSelect={onSelect}
        setEnableScrollHandler={setEnableScrollHandler}
        setMergeDate={setMergeDate}
      />
    </div>
  )
}

export default DatePanel
