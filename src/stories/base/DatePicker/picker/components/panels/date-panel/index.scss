.tita-ui__picker-panel__date {
  display: grid;
  grid-template: 26px 1fr / 100%;
  gap: 12px;

  .tita-ui__picker {
    &-head {

      &-btn__prev {
        margin-right: 0 !important;
      }
    }

    &-body {
      display: grid;
      grid-template: 24px / 1fr;
      gap: 4px;

      &__calendar-header {
        display: grid;
        grid-template: 24px / repeat(7, 1fr);
        justify-items: center;

        .tita-ui__picker-cell {
          padding: 0;
          line-height: 24px;
          text-align: center;
        }
      }

      &__calendar {
        height: 192px;
        position: relative;

        &::before,
        &::after {
          content: "";
          width: 100%;
          height: 10px;
          display: block;
          position: absolute;
          z-index: 1;
          pointer-events: none;
        }

        &::before {
          background: linear-gradient(to bottom,#ffffff 0%,rgba(255,255,255,0) 100%);
          top: 0;
        }

        &::after {
          background: linear-gradient(to top,#ffffff 0%,rgba(255,255,255,0) 100%);
          bottom: 0;
        }

        &-item {
          display: flex;
        }

        > span > div::-webkit-scrollbar {
          display: none;
        }

        .ReactVirtualized__List {
          &::-webkit-scrollbar {
            display: none;
          }
        }
      }
    }

    &-content {
      margin-top: 0;
    }
  }
}
