import React, { <PERSON> } from "react";
import classNames from "classnames";
import { PickerHeadClass } from "../../../utils";

interface HeadProps {
  children?: any;
  prevBtnDisabled?: boolean
  nextBtnDisabled?: boolean
  onPrev: () => void
  onNext: () => void
}

const PanelHead: FC<HeadProps> = (props) => {
  const { children, prevBtnDisabled, nextBtnDisabled, onPrev, onNext } = props;
  return (
    <div className={classNames(PickerHeadClass)}>
      {onPrev && (
        // eslint-disable-next-line jsx-a11y/control-has-associated-label
        <button
          className={classNames(`${PickerHeadClass}-btn__prev`, "tu-icon-left")}
          disabled={prevBtnDisabled}
          onClick={onPrev}
          type="button"
        />
      )}
      <div className={classNames(`${PickerHeadClass}__view`)}>{children}</div>
      {onNext && (
        // eslint-disable-next-line jsx-a11y/control-has-associated-label
        <button
          className={classNames(
            `${PickerHeadClass}-btn__next`,
            "tu-icon-APP-xi",
          )}
          disabled={nextBtnDisabled}
          onClick={onNext}
          type="button"
        />
      )}
    </div>
  );
};

export default PanelHead;
