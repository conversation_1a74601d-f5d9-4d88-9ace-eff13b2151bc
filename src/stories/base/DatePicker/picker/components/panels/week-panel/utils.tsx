//获取当前日期在这个月是第几周
export const getWeekNumber = (date: any) => {
  const currentDate = new Date(date).getDate()
  const day = new Date(date).getDay() //今天是星期几 星期天返回0
  const week = Math.ceil((currentDate + 6 - day) / 7)

  return week
}

//获取某年某月有多少天
export const getDaysOfMonth = (year: number, month: number) => {
  switch (month) {
    case 1:
    case 3:
    case 5:
    case 7:
    case 8:
    case 10:
    case 12:
      return 31
    case 4:
    case 6:
    case 9:
    case 11:
      return 30
    case 2:
      return (year % 4 == 0 && year % 100 != 0) || year % 400 == 0 ? 29 : 28
    default:
      return 0
  }
}

export function startTime(time: any) {
  const nowTimeDate = new Date(time)
  return nowTimeDate.setHours(0, 0, 0, 0)
}

export function endTime(time: any) {
  const nowTimeDate = new Date(time)
  return nowTimeDate.setHours(23, 59, 59, 999)
}

export const getWeeksNum = (year: number, month: number) => {
  var d = new Date()
  // 该月第一天
  d.setFullYear(year, month - 1, 1)
  var w1 = d.getDay()
  if (w1 == 0) w1 = 7
  // 该月天数
  d.setFullYear(year, month, 0)
  var dd = d.getDate()
  // 第一个周一
  let d1
  if (w1 != 1) d1 = 7 - w1 + 2
  else d1 = 1
  let week_count = Math.ceil((dd - d1 + 1) / 7)
  return week_count
}

export const getPointYearMonthWeekStartTimeAndEndTime = (
  year: number | string,
  month: number | string,
  week: number,
) => {
  // 获取当月的周期
  const date = new Date(`${year}/${month}/1`)
  let nowDayOfWeek = date.getDay()
  if (nowDayOfWeek === 0) nowDayOfWeek = 7
  const dayNum = 1 * 24 * 60 * 60 * 1000
  const firstDate = new Date(date.valueOf() - (nowDayOfWeek - 1) * dayNum)
  const lastDate = new Date(new Date(firstDate).valueOf() + 6 * dayNum * week)
  const startDate = new Date(
    startTime(new Date(firstDate).valueOf() + 7 * dayNum * (week - 1)),
  )
  const endDate = new Date(endTime(lastDate))
  const startMonth =
    startDate.getMonth() + 1 > 9
      ? startDate.getMonth() + 1
      : '0' + (startDate.getMonth() + 1)

  const startDateValue =
    startDate.getDate() > 9 ? startDate.getDate() : '0' + startDate.getDate()

  const endMonth =
    endDate.getMonth() + 1 > 9
      ? endDate.getMonth() + 1
      : '0' + (endDate.getMonth() + 1)

  const endDateValue =
    endDate.getDate() > 9 ? endDate.getDate() : '0' + endDate.getDate()

  return {
    startTime: `${startMonth}/${startDateValue}`,
    endTime: `${endMonth}/${endDateValue}`,
  }
}

export const getPointYearMonthWeeksRange = (
  year: number | string,
  month: number | string,
) => {
  const startDate = new Date(`${year}/${month}/1`)
  const calDays = new Date(startDate.valueOf())
  calDays.setMonth(calDays.getMonth() + 1)
  calDays.setDate(0)
  const days = calDays.getDate()
  const dayNum = 1 * 24 * 60 * 60 * 1000
  const result: {
    startTime: string
    endTime: string
  }[] = []
  for (let i = 0; i < days; i++) {
    const date = new Date(startDate.valueOf() + i * dayNum)
    if (date.getDay() === 1) {
      const endDate = new Date(date.valueOf() + 6 * dayNum)
      result.push({
        startTime: `${date.getMonth() + 1 >= 10 ? date.getMonth() + 1: '0' + (date.getMonth() + 1)}/${date.getDate() >= 10 ? date.getDate() : '0' + (date.getDate())}`,
        endTime: `${endDate.getMonth() + 1 >= 10 ? endDate.getMonth() + 1: '0' + (endDate.getMonth() + 1)}/${endDate.getDate() >= 10 ? endDate.getDate() : '0' + (endDate.getDate())}`,
      })
      i += 6
    }
  }
	return result;
}

//获取某个日期处于的年月周
export const getWeek = (date: Date, isPreMonth = false) => { 
  let currentMonth = new Date(date).getMonth() + 1;
  let currentYear = new Date(date).getFullYear()
  let currentDate  = new Date(date).getDate()

  let week

  const range = getPointYearMonthWeeksRange(currentYear, currentMonth);

  if(new Date(range[0].startTime).getDate() > currentDate) { //计入上个月
    currentMonth -= 1;
    const range = getPointYearMonthWeeksRange(currentYear, currentMonth);
    week = range.length
  } else {
    range.forEach((item, index) => {
      if((new Date(item.startTime).getDate() < currentDate && new Date(item.endTime).getDate() > currentDate) || new Date(item.startTime).getDate()  === currentDate || new Date(item.endTime).getDate() === currentDate) {
        week = index + 1
      }
    })
  }
  return [week, currentMonth, currentYear]
};
