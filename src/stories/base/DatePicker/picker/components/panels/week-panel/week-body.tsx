import React, { FC } from 'react'
import classNames from 'classnames'
import {
  getPointYearMonthWeeksRange,
} from './utils'
import { PickerMode } from '../../../enum'
import { PickerBodyClass, PickerClass } from '../../../utils'
import { getWeekly } from '../../../utils'
import { getWeek } from './utils'
import './index.scss'

const preCls = 'titaui__picker'

interface IDatePanelBodyProps {
  startDate: string
  weekStartDay?: 'MONDAY' | 'SUNDAY'
  onSelect: (date: any, type: any, year?: any) => void
  valueTypeIsRange?: boolean
  selectWeekDate: string[]
}

const WeekPanelBody: FC<IDatePanelBodyProps> = (props) => {
  const { startDate, onSelect, selectWeekDate } = props
  
  const getCells = (date: any) => {
    
    const currentYear = getWeek(new Date(new Date()))[2]
    const currentMonth = getWeek(new Date(new Date()))[1]
    const currentWeek = getWeek(new Date(new Date()))[0]

    let year = getWeek(new Date(new Date(date)))[2]//获取年
    let month = getWeek(new Date(new Date(date)))[1]

    let selectYear: any;
    let selectMonth: any;
    let selectWeek: any;
    if(selectWeekDate && typeof selectWeekDate[0] === 'string') {
      selectYear = getWeek(new Date(new Date(selectWeekDate[0])))[2]
      selectMonth = getWeek(new Date(new Date(selectWeekDate[0])))[1]
      selectWeek = getWeek(new Date(new Date(selectWeekDate[0])))[0]
    }

    const range = getPointYearMonthWeeksRange(year as number, month as number)
    if (range) {
      return range.map((item, i) => {
        return (
          <tr
            key={i}
            className={classNames(`${preCls}__week`, {
              [`${preCls}__week--current`]:
                currentWeek === i+1 && 
                getWeek(new Date(`${year}/${item.startTime}`))[2] === currentYear && 
                getWeek(new Date(`${year}/${item.startTime}`))[1] === currentMonth
            }, {
              [`${preCls}__week--select`]:
                selectWeek === i+1 && 
                selectYear === getWeek(new Date(`${year}/${item.startTime}`))[2] && 
                selectMonth === getWeek(new Date(`${year}/${item.startTime}`))[1]
            })}
          >
            <div
              onClick={() => {
                if (onSelect) {
                  onSelect([`${year}/${item.startTime}`, `${year}/${item.endTime}`], PickerMode.Week, year)
                }
              }}
              className={`${preCls}__week-box`}
            >
              <span className={`${preCls}__week-index`}>
                {getWeekly()[i]}
              </span>
              {`${item.startTime}~${item.endTime}`}
            </div>
          </tr>
        )
      })
    } else {
			return null;
		}
  }

  return (
    <div className={classNames(PickerBodyClass)}>
      <table className={classNames(`${PickerClass}-content`)}>
        <tbody>{getCells(startDate)}</tbody>
      </table>
    </div>
  )
}

export default WeekPanelBody
