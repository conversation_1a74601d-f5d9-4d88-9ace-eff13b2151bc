import React from 'react'
import classNames from 'classnames'
import { isEn } from '@tita/utils'
import { PickerMode } from '../../../enum'
import { PickerHeadClass, PickerClass, MONTHDATAMAPEN } from '../../../utils'
import { getWeek } from './utils'
import './index.scss'

interface Props {
  startDate: string
  onPrev: () => void
  onNext: () => void
  onPanelChange: (viewDate: any, type: any) => void
}

function DatePanelHead(props: Props) {
  const { startDate, onPrev, onNext, onPanelChange } = props

  const  yearText = getWeek(new Date(startDate))[2]
  const  monthText = getWeek(new Date(startDate))[1]

  return (
    <div className={classNames(PickerHeadClass)}>
      {!isEn && (
        <div className={classNames(`${PickerHeadClass}__view`)}>
          <button
            className={classNames(`${PickerHeadClass}-btn__year`)}
            onClick={() => onPanelChange && onPanelChange(startDate, PickerMode.Year)}
            type="button"
          >
            {yearText}
          </button>
          <span className={classNames(`${PickerClass}-unit`)}>&nbsp;年</span>
          <button
            className={classNames(`${PickerHeadClass}-btn__month`)}
            onClick={() => onPanelChange && onPanelChange(startDate, PickerMode.Month)}
            type="button"
          >
            {monthText}
          </button>
          <span className={classNames(`${PickerClass}-unit`)}>月</span>
        </div>
      )}
      {isEn && (
        <div className={classNames(`${PickerHeadClass}__view`)}>
          <button
            className={classNames(`${PickerHeadClass}-btn__month`)}
            onClick={() => onPanelChange && onPanelChange(startDate, PickerMode.Month)}
            style={{ marginRight: 4 }}
            type="button"
          >
            {MONTHDATAMAPEN[Number(monthText) - 1]}
          </button>
          <button
            className={classNames(`${PickerHeadClass}-btn__year`)}
            onClick={() => onPanelChange && onPanelChange(startDate, PickerMode.Year)}
            type="button"
          >
            {yearText}
          </button>
        </div>
      )}
      <div className={classNames(`${PickerHeadClass}__operation`)}>
        {
          <button
            className={classNames(
              `${PickerHeadClass}-btn__prev`,
              'tu-icon-arrow-up1',
            )}
            onClick={onPrev}
            type="button"
          />
        }
        {
          <button
            className={classNames(
              `${PickerHeadClass}-btn__next`,
              'tu-icon-arrow-down1',
            )}
            onClick={onNext}
            type="button"
          />
        }
      </div>
    </div>
  )
}

export default DatePanelHead
