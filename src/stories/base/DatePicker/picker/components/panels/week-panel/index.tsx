import React, { <PERSON> } from "react";
import classNames from "classnames";
import moment, { Moment } from "moment";
import { PickerPanelClass } from "../../../utils";
import WeekPanelHead from "./week-head";
import WeekPanelBody from "./week-body";
import { getWeek } from './utils'
import "./index.scss";

interface IDatePanelProps {
  viewDate: string[]
  onViewDateChange: (date: any, type?: string) => void
  onPanelChange: (date: Moment, type: any) => void
  onSelect: (date: any, type: any, year?: any) => void
  valueTypeIsRange: boolean
  selectWeekDate: string[]
}

const DatePanel: FC<IDatePanelProps> = (props) => {
  const { viewDate, onViewDateChange, selectWeekDate } = props;

  const startDate = viewDate[0] 

  const onMonthChange = (diff: number) => {
    if (onViewDateChange) {
      if(typeof startDate === 'string') {
        onViewDateChange(moment(`${new Date(startDate).getFullYear()}/${new Date(startDate).getMonth() + 1}/15`).add(diff, "M"));
      } else {
        const startYear = getWeek(new Date(startDate))[2];
        const startMonth = getWeek(new Date(startDate))[1];
        onViewDateChange(moment(`${startYear}/${startMonth}/15`).add(diff, "M")); //日期写死15 是因为这里是控制头部的月的显示，如果日期为归类上一个月的，头部会乱
      }
    }
  };

  return (
    <div className={classNames(`${PickerPanelClass}__week`)}>
      <WeekPanelHead
        {...props}
        startDate={startDate}
        onPrev={() => onMonthChange(-1)}
        onNext={() => onMonthChange(1)}
      />
      <WeekPanelBody {...props} startDate={viewDate[0]} selectWeekDate={selectWeekDate}/>
    </div>
  );
};

export default DatePanel;
