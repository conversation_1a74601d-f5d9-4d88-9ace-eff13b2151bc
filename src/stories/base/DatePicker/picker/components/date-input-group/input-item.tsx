import React, { Ref, forwardRef, useImperativeHandle, useRef } from 'react'
import classNames from 'classnames'
import Tooltip from '../../../../Tooltip'
import { PickerClass } from '../../utils'
import ConditionRender from '../condition-render'

interface Props {
  activated: boolean
  date: string
  disabled?: boolean
  placeholder?: string
  tooltipZIndex?: number
  onClear?: () => void
  onFocus?: () => void
}

const prefixCls = `${PickerClass}__date-input-item`
const InputItem = (props: Props, ref: Ref<IInputItemRefObj>) => {
  const { activated, date, disabled = false, placeholder, onClear, onFocus, tooltipZIndex } = props

  const inputRef = useRef<any>()
  const timerRef = useRef<any>()

  const onClearHandler = (e: React.MouseEvent) => {
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    onFocus?.()
    onClear?.()
  }

  const focus = () => {
    timerRef.current = setTimeout(() => {
      inputRef.current?.focus()
      clearTimeout(timerRef.current)
    }, 0)
  }

  useImperativeHandle(ref, () => ({
    focus,
  }))

  return (
    <div className={classNames(prefixCls, {
      [`${prefixCls}--activated`]: activated
    })}>
      <input
        disabled={disabled}
        placeholder={placeholder}
        onFocus={onFocus}
        ref={inputRef}
        value={date}
      />
      <ConditionRender condition={!disabled}>
        <Tooltip
          defaultVisible={false}
          placement="top"
          overlay={`清空${placeholder}`}
          trigger={['hover']}
          zIndex={tooltipZIndex}
        >
          <span
            className={classNames('tu-icon-cross', `${prefixCls}__cancel`, {
              [`${prefixCls}__cancel--visible`]: date,
            })}
            onClick={onClearHandler}
          />
        </Tooltip>
      </ConditionRender>
    </div>
  )
}

interface IInputItemRefObj {
  focus: () => void
}
export default forwardRef(InputItem)
