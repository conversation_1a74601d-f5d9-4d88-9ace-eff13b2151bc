.tita-ui__picker__date-input-group {
  height: 32px;
  margin: 16px 20px 1px;
  padding: 0 12px;
  border: 1px solid #2879ff;
  border-radius: 8px;
  background: #ffffff;
  display: flex;
  align-items: center;

  &-divide {
    width: 8px;
    height: 1px;
    margin: 0 8px;
    border-radius: 2px;
    background: #3f4755;
  }
}

.tita-ui__picker__date-input-item {
  flex: 1;
  font-size: 13px;
  line-height: 18px;
  color: #3f4755;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;

  &--activated {
    input {
      color: #2879ff;
    }
  }

  input {
    flex: 1;
    width: 0;
    padding: 0;
    border: none;
    caret-color: #2879ff;
    background-color: #fff;
    outline: none;
  }

  &__cancel {
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    color: #bfc7d5;
    visibility: hidden;
    cursor: pointer;

    &--visible {
      opacity: 1;
      transform: scale(1);
      visibility: visible;
    }

    &:hover {
      color: #f05e5e;
    }
  }
}
