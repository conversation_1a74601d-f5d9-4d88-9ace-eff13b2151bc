import React, { FC, useEffect, useRef } from 'react'
import moment from 'moment'
import classNames from 'classnames'
import { getLocale } from '@tita/utils'
import { PickerClass, RangeSettingTypes } from '../../utils'
import { PickerSettingType, PickerValues } from '../../type'
import InputItem from './input-item'
import './index.scss'

interface IDateInputGroupProps {
  format?: string
  isLong?: boolean
  settingDateType: PickerSettingType
  viewDate: PickerValues
  tooltipZIndex: number
  onChange: (viewDate: PickerValues) => void
  onFocus: (settingType: PickerSettingType) => void
}

const prefixCls = `${PickerClass}__date-input-group`
const DateInputGroup: FC<IDateInputGroupProps> = (props) => {
  const { format, isLong, onChange, onFocus, settingDateType, viewDate, tooltipZIndex } = props

  const startDateInputRef = useRef<any>()
  const endDateInputRef = useRef<any>()

  const onChangeHandler = (type: RangeSettingTypes) => {
  if (type === RangeSettingTypes.START) {
      onChange(['', viewDate[1]])
    }
    if (type === RangeSettingTypes.END) {
      onChange([viewDate[0], ''])
    }
  }

  useEffect(() => {
    if (settingDateType === RangeSettingTypes.START) {
      startDateInputRef.current.focus()
    }
    if (settingDateType === RangeSettingTypes.END) {
      endDateInputRef.current.focus()
    }
  }, [settingDateType, viewDate])

  const startDate =
    viewDate && viewDate[0] ? moment(viewDate[0]).format(format) : ''
  const endDate =
    viewDate && viewDate[1] ? moment(viewDate[1]).format(format) : ''

  return (
    <div className={prefixCls}>
      <InputItem
        activated={settingDateType === RangeSettingTypes.START}
        date={startDate}
        placeholder={getLocale('Mod_Startingtime') || '开始时间'}
        onClear={() => onChangeHandler(RangeSettingTypes.START)}
        onFocus={() => onFocus(RangeSettingTypes.START)}
        ref={startDateInputRef}
        tooltipZIndex={tooltipZIndex}
      />
      <div className={classNames(`${prefixCls}-divide`)} />
      <InputItem
        activated={settingDateType === RangeSettingTypes.END}
        date={isLong ? (getLocale('Pro_page_Plan_LongTerm') || '长期') : endDate}
        disabled={isLong}
        placeholder={getLocale('Mod_EndTime') || '结束时间'}
        onClear={() => onChangeHandler(RangeSettingTypes.END)}
        onFocus={() => onFocus(RangeSettingTypes.END)}
        ref={endDateInputRef}
        tooltipZIndex={tooltipZIndex}
      />
    </div>
  )
}

export default DateInputGroup
