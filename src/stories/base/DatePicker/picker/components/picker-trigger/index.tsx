import React, { FC, useContext } from 'react'
import classNames from 'classnames'
import moment from 'moment'
import PickerContext from '../../context'
import { PickerMode } from '../../enum'
import { PickerValue } from '../../type'
import { PickerDateFormat, PickerTriggerClass } from '../../utils'
import './index.scss'

export interface IPickerTriggerProps {
  children?: React.ReactElement | null
  clearAble?: boolean
  focused?: boolean
  viewDate: PickerValue
  placeholder?: string
  disabled?: boolean
  disableDefaultDiv?: boolean
  onClear: () => void
  onClick: () => void
}

const PickerTrigger: FC<IPickerTriggerProps> = (props) => {
  const {
    children,
    clearAble = false,
    disabled = false,
    focused,
    onClear,
    onClick,
    placeholder,
    viewDate,
    disableDefaultDiv,
  } = props
  const { picker, format } = useContext(PickerContext)

  const onClearHandler = (event: React.MouseEvent<HTMLSpanElement>) => {
    event.stopPropagation()
    event.nativeEvent.stopImmediatePropagation()
    onClear?.()
  }

  let currFormat = format
  switch (picker) {
    case PickerMode.Date:
      currFormat = PickerDateFormat
      break
    case PickerMode.Month:
      currFormat = 'YYYY/MM'
      break
    case PickerMode.Year:
      currFormat = 'YYYY'
      break
    default:
      currFormat = format
      break
  }

  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {children ? (
        disableDefaultDiv ? (
          React.cloneElement(children, {
            onClick: () => !disabled && onClick(),
            ...children.props,
          })
        ) : (
          <div onClick={() => !disabled && onClick()}>{children}</div>
        )
      ) : (
        <div
          className={classNames(PickerTriggerClass, {
            [`${PickerTriggerClass}--disabled`]: disabled,
            [`${PickerTriggerClass}--focused`]: focused,
          })}
          onClick={() => !disabled && onClick()}
        >
          <div
            className={classNames(`${PickerTriggerClass}__input`, {
              [`${PickerTriggerClass}__input--placeholder`]:
                !viewDate && placeholder,
            })}
          >
            {viewDate ? moment(viewDate).format(currFormat) : placeholder}
          </div>
          <div className={classNames(`${PickerTriggerClass}__operation`)}>
            {clearAble && viewDate && !disabled && (
              <span
                className={classNames(
                  'tu-icon-cross',
                  `${PickerTriggerClass}__operation-cancel`
                )}
                onClick={onClearHandler}
              />
            )}
            {!disabled && <span className='tu-icon-caret-down' />}
          </div>
        </div>
      )}
    </>
  )
}
PickerTrigger.defaultProps = {
  children: null,
  clearAble: false,
  disabled: false,
  placeholder: '请选择',
}

export default PickerTrigger
