.tita-ui__picker-trigger {
  width: 120px;
  min-width: min-content;
  max-width: 254px;
  height: 32px;
  padding: 7px 12px;
  border: 1px solid #e9ecf0;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover,
  &--focused {
    border-color: #2879ff;
  }

  &:hover {
    .tita-ui__picker-trigger__operation-cancel {
      opacity: 1;
      transform: scale(1);
      visibility: visible;
    }
  }

  &--focused {
    .tita-ui__picker-trigger__operation {
      .tu-icon-caret-down {
        transform: rotate(-180deg) translateZ(0);
      }
    }
  }

  &__input {
    flex: 1;
    margin-right: 12px;
    font-size: 12px;
    line-height: 18px;
    font-weight: normal;
    color: #3f4755;
    white-space: nowrap;

    &--placeholder {
      color: #bfc7d5;
    }
  }

  &__operation {
    display: flex;
    align-items: center;

    .tu-icon-caret-down {
      font-size: 16px;
      transition: all 0.3s ease;
      color: #6F7886;

      &::before {
        transform: scale(0.5);
        display: inline-block;
      }
    }

    &-cancel {
      margin-right: 4px;
      opacity: 0;
      transform: scale(0);
      transition: all 0.3s ease;
      color: #bfc7d5;
      visibility: hidden;
      cursor: pointer;

      &:hover {
        color: #f05e5e;
      }
    }
  }

  &--disabled {
    cursor: not-allowed;

    &:hover {
      border-color: #e9ecf0;
    }

    .tita-ui__picker-trigger {
      &__input {
        cursor: not-allowed;
      }
    }
  }
}
