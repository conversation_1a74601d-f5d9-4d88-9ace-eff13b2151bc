import React, { FC } from 'react'
import classNames from 'classnames'
import moment from 'moment'
import { getLocale } from '@tita/utils'
import { PickerValues } from '../../type'
import { PickerDateFormat, PickerRangeTriggerClass } from '../../utils'
import './index.scss'

interface IRangePickerTriggerProps {
  contentClassName?: string
  children?: React.ReactElement | null
  clearAble?: boolean
  format?: string
  disabled?: boolean
  focused?: boolean
  placeholder?: string
  triggerClassName?: string
  viewDate: PickerValues
  onClick?: () => void
}

const RangePickerTrigger: FC<IRangePickerTriggerProps> = (props) => {
  const {
    children,
    clearAble,
    contentClassName = '',
    disabled,
    focused,
    format = PickerDateFormat,
    onClick,
    placeholder,
    triggerClassName = '',
    viewDate
  } = props

  const onChildrenClick = () => !disabled && onClick?.()

  const correctlyValue = viewDate && viewDate instanceof Array
  const startDate =
    correctlyValue && viewDate[0] ? moment(viewDate[0]).format(format) : ''
  const endDate =
    correctlyValue && viewDate[1] ? moment(viewDate[1]).format(format) : startDate
  const viewDateEnable = startDate && endDate

  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {children ? (
        <div className={contentClassName} onClick={onChildrenClick}>{children}</div>
      ) : (
        <div
          className={classNames(
            PickerRangeTriggerClass,
            {
              [`${PickerRangeTriggerClass}--disabled`]: disabled,
              [`${PickerRangeTriggerClass}--focused`]: focused,
            },
            triggerClassName,
          )}
          onClick={onChildrenClick}
        >
          <div
            className={classNames(`${PickerRangeTriggerClass}__input`, {
              [`${PickerRangeTriggerClass}__input--placeholder`]:
                !viewDateEnable && placeholder,
            })}
          >
            {startDate && (
              <p
                className={classNames(
                  `${PickerRangeTriggerClass}__selected-date`,
                )}
              >
                {startDate}
              </p>
            )}
            {endDate && (
              <div
                className={classNames(
                  `${PickerRangeTriggerClass}__date-connector`,
                )}
              >
                ~
              </div>
            )}
            {endDate && (
              <p
                className={classNames(
                  `${PickerRangeTriggerClass}__selected-date`,
                )}
              >
                {endDate}
              </p>
            )}
            {!viewDateEnable && placeholder}
          </div>
          <div className={classNames(`${PickerRangeTriggerClass}__operation`)}>
            {clearAble && !disabled && (
              <span
                className={classNames(
                  'tu-icon-cross',
                  `${PickerRangeTriggerClass}__selected-date__cancel`,
                  {
                    [`${PickerRangeTriggerClass}__selected-date__cancel--visible`]:
                      viewDateEnable,
                  },
                )}
              />
            )}
            {!disabled && <span className="tu-icon-caret-down" />}
          </div>
        </div>
      )}
    </>
  )
}
RangePickerTrigger.defaultProps = {
  children: null,
  disabled: false,
  clearAble: false,
  placeholder: getLocale('Rep_NewS_All'),
}

export default RangePickerTrigger
