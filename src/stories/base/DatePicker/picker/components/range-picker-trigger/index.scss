.tita-ui__picker-ranges-trigger {
  min-width: 114px;
  max-width: 285px;
  // height: 36px;
  padding: 7px 16px;
  // border: 1px solid #e9ecf0;
  border-radius: 18px;
  transition: all 0.3s ease;
  background-color: #ffffff;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-flex;
  align-items: center;

  &:hover,
  &--focused {
    border-color: #2879ff;
  }

  // &--focused {
  //   .tita-ui__picker-ranges-trigger__operation {
  //     .tu-icon-caret-down::before {
  //       transform: scale(0.5) rotate(-180deg);
  //     }
  //   }
  // }

  &__input {
    // flex: 1;
    font-size: 14px;
    line-height: 22px;
    font-weight: normal;
    color: #3f4755;
    display: flex;
    align-items: center;

    // &--placeholder {
    //   color: #bfc7d5;
    // }
  }

  &__date-connector {
    width: 8px;
    margin: 0 8px;
    border-radius: 2px;
    text-align: center;
  }

  &__selected-date {
    // flex: 1;
    font-size: 13px;
    line-height: 18px;
    color: #3f4755;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:last-child {
      margin-right: 8px;
    }

    &__cancel {
      opacity: 0;
      transform: scale(0);
      transition: all 0.3s ease;
      color: #bfc7d5;
      visibility: hidden;
      cursor: pointer;

      &--visible {
        // margin-left: 8px;
        opacity: 1;
        transform: scale(1);
        visibility: visible;
      }

      &:hover {
        color: #f05e5e;
      }
    }
  }

  &__operation {
    display: flex;
    align-items: center;

    .tu-icon-caret-down {
      font-size: 16px;
      color: #3f4755;

      &::before {
        transform: scale(0.5);
        transition: all 0.5s cubic-bezier(0.32, 0.28, 0.58, 1.12);
        display: inline-block;
      }
    }
  }

  &--disabled {
    cursor: not-allowed;

    &:hover {
      border-color: #e9ecf0;
    }

    .tita-ui__picker-trigger {
      &__input {
        cursor: not-allowed;
      }
    }
  }
}
