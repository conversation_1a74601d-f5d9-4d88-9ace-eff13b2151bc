import React, { FC } from 'react'
import classNames from 'classnames'
import Button from '../../../../Button'
import Checkbox from '../../../../CheckBox'
import { getLocale } from '@tita/utils'
import { PickerRangesClass } from '../../utils'
import ConditionRender from '../condition-render'
import './index.scss'

interface Props {
  disabled: boolean
  showClear?: boolean
  isLong?: boolean
  showLong?: boolean
  onCancel: () => void
  onClear?: () => void
  onSelectLong: (value: boolean) => void
  onOk: () => void
}

const RangePickerOperationBar: FC<Props> = (props) => {
  const {
    showClear,
    isLong,
    onSelectLong,
    showLong,
    disabled,
    onCancel,
    onClear,
    onOk,
  } = props

  return (
    <div className={classNames(`${PickerRangesClass}__operations`)}>
      <div className={classNames(`${PickerRangesClass}__operations-left`)}>
        {showClear && (
          <Button onClick={onClear} type='link' disablePadding primary>
            {getLocale('Mod_Empty') || '清空'}
          </Button>
        )}
        <ConditionRender condition={!!showLong}>
          <div className={`${PickerRangesClass}__long`}>
            <Checkbox onChange={onSelectLong} checked={isLong}></Checkbox>
            <Button
              type='link'
              disablePadding
              className='long-btn'
              onClick={() => onSelectLong(!isLong)}
              style={{
                color: '#6F7886',
              }}
            >
              {getLocale('Pro_page_Plan_LongTerm') || '长期'}
            </Button>
          </div>
        </ConditionRender>
      </div>
      <div
        className={classNames(
          `${PickerRangesClass}__operations-right space-x-10px`
        )}
      >
        <Button onClick={onCancel} type='border' size='small' minWidth={64}>
          {getLocale('Mod_Cancel') || '取消'}
        </Button>
        <Button
          disabled={disabled}
          onClick={onOk}
          size='small'
          minWidth={64}
          primary
        >
          {getLocale('Mod_Determiness') || '确定'}
        </Button>
      </div>
    </div>
  )
}

export default RangePickerOperationBar
