.tita-ui__picker-ranges__operations {
  height: 27px;
  padding: 12px 16px;
  border-top: 1px solid #e9ecf0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: content-box;

  &-left {
    flex: 1 !important;
    > * + * {
      margin-left: 16px !important;
    }
    display: flex;
    justify-content: space-between;
    .long-btn:hover {
      color: #2879ff !important;
    }
  }
  &-left,
  &-right {
    display: flex;
    align-items: center;
  }

  &-right {
    flex: 0 !important;
    justify-content: flex-end;
    margin-left: 10px;
  }

  .tita-button-default,
  .tita-button-primary {
    font-size: 13px;
    border-radius: 14px;
    transition: all 0.3s ease;
  }

  .tita-button-default {
    border-color: #dfe3ea;
    color: #6f7886;

    &:hover {
      background-color: #ffffff;
      border-color: #2879ff;
      color: #2879ff;
    }
  }

  .titaui-text-btn {
    transition: color 0.3s ease;
    color: #2879ff;

    &:hover {
      color: #5c8eff;
    }
  }
}
