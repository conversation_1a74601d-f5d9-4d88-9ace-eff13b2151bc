import React, { FC, useContext, useMemo } from 'react'
import classNames from 'classnames'
import { RangePickerContext } from '../../context'
import { PickerMode, SHORTCUTS } from '../../enum'
import { PickerShortCuts, PickerValues } from '../../type'
import {
  getCurrentValues,
  getCurrentWeekDay,
  getNextValues,
  getPreviousValues,
  PickerRangesClass,
} from '../../utils'
import { getLocale } from '@tita/utils'

interface Props {
  showPreviousCycle?: boolean
  showNextCycle?: boolean
  onChange: (date: PickerValues) => void
}

const Shortcuts: FC<Props> = (props) => {
  const { onChange, showPreviousCycle, showNextCycle } = props
  const { picker = PickerMode.Date } = useContext(RangePickerContext)
  const shortcutsData = useMemo(() => {
    return [
      {
        key: SHORTCUTS.YESTERDAY,
        title: getLocale('OKR_MyO_Pr_Text_Yesterday') || '昨天',
        show:
          ![PickerMode.Week, PickerMode.Month, PickerMode.Year].includes(
            picker
          ) && showPreviousCycle,
      },
      {
        key: SHORTCUTS.TODAY,
        title: getLocale('Mod_Today') || '今天',
        show: ![PickerMode.Week, PickerMode.Month, PickerMode.Year].includes(
          picker
        ),
      },
      {
        key: SHORTCUTS.TOMORROW,
        title: getLocale('Pro_page_Plan_Tomorrow') || '明天',
        show:
          ![PickerMode.Week, PickerMode.Month, PickerMode.Year].includes(
            picker
          ) && showNextCycle,
      },
      {
        key: SHORTCUTS.PREVIOUS_WEEK,
        title: getLocale('Rep_NewS_LastWeek') || '上周',
        show:
          ![PickerMode.Month, PickerMode.Year].includes(picker) &&
          showPreviousCycle,
      },
      {
        key: SHORTCUTS.CURRENT_WEEKDAY,
        title: getLocale('Feed_Tab_Title_Thisweek') || '本周',
        show: ![PickerMode.Month, PickerMode.Year].includes(picker),
      },
      {
        key: SHORTCUTS.NEXT_WEEK,
        title: getLocale('Mod_NextWeek') || '下周',
        show:
          ![PickerMode.Month, PickerMode.Year].includes(picker) &&
          showNextCycle,
      },
      {
        key: SHORTCUTS.PREVIOUS_MONTH,
        title: getLocale('Rep_NewS_LastMonth') || '上月',
        show: ![PickerMode.Year].includes(picker) && showPreviousCycle,
      },
      {
        key: SHORTCUTS.CURRENT_MONTH,
        title: getLocale('Feed_Tab_Title_Thismonth') || '本月',
        show: ![PickerMode.Year].includes(picker),
      },
      {
        key: SHORTCUTS.NEXT_MONTH,
        title: '下月',
        show: ![PickerMode.Year].includes(picker) && showNextCycle,
      },
      {
        key: SHORTCUTS.CURRENT_YEAR,
        title: getLocale('Feed_Tab_Title_Thisyear') || '本年',
        show: ![PickerMode.Week].includes(picker),
      },
    ].filter((item) => item.show)
  }, [showPreviousCycle])

  const onItemClick = (shortCuts: PickerShortCuts) => {
    let date
    switch (shortCuts.key) {
      case SHORTCUTS.CURRENT_YEAR:
        date = getCurrentValues('year')
        break
      case SHORTCUTS.CURRENT_MONTH:
        date = getCurrentValues('month')
        break
      case SHORTCUTS.PREVIOUS_MONTH:
        date = getPreviousValues('month')
        break
      case SHORTCUTS.NEXT_MONTH:
        date = getNextValues('month')
        break
      case SHORTCUTS.CURRENT_WEEKDAY:
        date = getCurrentWeekDay()
        break
      case SHORTCUTS.CURRENT_WEEK:
        date = getCurrentWeekDay()
        break
      case SHORTCUTS.PREVIOUS_WEEK:
        date = getPreviousValues('week')
        break
      case SHORTCUTS.NEXT_WEEK:
        date = getNextValues('week')
        break
      case SHORTCUTS.TODAY:
        date = getCurrentValues('date')
        break
      case SHORTCUTS.YESTERDAY:
        date = getPreviousValues('date')
        break
      case SHORTCUTS.TOMORROW:
        date = getNextValues('date')
        break
      default:
        date = ['', '']
        break
    }
    onChange(date)
  }

  return (
    <div className={classNames(`${PickerRangesClass}__shortcuts`)}>
      {shortcutsData.map((item) => (
        <div
          className={classNames(`${PickerRangesClass}__shortcuts-item`)}
          key={item.key}
          onClick={() => onItemClick(item)}
        >
          {item.title}
        </div>
      ))}
    </div>
  )
}

export default Shortcuts
