import React, {
  FC,
  ReactNode,
  useMemo,
  useEffect,
  useState,
} from 'react'
import classNames from 'classnames'
import cloneDeep from 'lodash/cloneDeep'
import moment, { Moment } from 'moment'
import Popup from '../../../Popup'
import TimePicker from '../components/panels/date-time-panel/time-picker'
import DateInputGroup from '../components/date-input-group'
import RangePickerOperationBar from '../components/range-picker-operation-bar'
import RangePickerTrigger from '../components/range-picker-trigger'
import Picker, { IPickerProps } from '../picker'
import { PickerSettingType, PickerValues } from '../type'
import useInitializeRangeValue from '../useInitializeRangeValue'
import {
  PickerDropdownClass,
  PickerDateFormat,
  PickerRangesClass,
  RangeSettingTypes,
  getPopupPlacement,
  PickerDateTimeFormat,
  PickerTimeFormat,
  setPickerDateBySettingType,
  setPickerDateTimeBySettingType,
} from '../utils'
import './index.scss'
import { getLocale } from '@tita/utils'
import { useRefState } from '@tita/hooks'
import Shortcuts from '../components/shortcuts'

interface IDateTimeRangePickerProps extends IPickerProps {
  /** 是否允许为空提交 */
  canEmpty?: boolean
  /** popup 内容容器 class */
  className?: string
  /** 是否显示「长期」，默认为 false */
  isLongTerm?: boolean
  /** 是否展示长期按钮，默认为 false */
  showLongTerm?: boolean
  /** 是否展示清除按钮，默认为 true */
  showClear?: boolean
  /** 是否展示上一周期（上周、上月） */
  showPreviousCycle?: boolean
  onChange: (
    date: PickerValues | { isLong: boolean; dateRange: PickerValues },
  ) => boolean | Promise<boolean>
  onOk?: (
    date: PickerValues | { isLong: boolean; dateRange: PickerValues },
  ) => boolean | Promise<boolean>
  renderPickerDate?: (date: Moment, params?: {
    disabled: boolean;
    forwardDisabled: boolean;
    backwardDisabled: boolean;
  }) => string | ReactNode
}

const prefixCls = 'tita-ui__picker-ranges__date-time'
const DateTimeRangePicker: FC<IDateTimeRangePickerProps> = (props) => {
  const {
    canEmpty = false,
    children,
    className,
    direction = 'ltr',
    format = PickerDateTimeFormat,
    isLongTerm = false,
    onChange,
    onOk,
    onPopupVisibleChange,
    popupPlacement = 'bottomLeft',
    renderPickerDate,
    showClear = true,
    showLongTerm = false,
    showPreviousCycle,
    // @ts-ignore
    values: propsValues,
    value: propsValue,
    ...restProps
  } = props

  const values = propsValues || propsValue

  const [isLong, isLongRef, setIsLong] = useRefState<boolean>(isLongTerm)
  const [popupVisible, setPopupVisible] = useState(false)
  const [settingType, settingTypeRef, setSettingType] = useRefState<PickerSettingType>(
    RangeSettingTypes.END,
  )
  const [viewDate, setViewDate] = useInitializeRangeValue(values, [moment(), moment()], popupVisible)
  // const oldEndDateRef = useRef<-1 | any>(-1)
  useEffect(() => {
    if (isLong && viewDate.length > 1) {
      setViewDate([viewDate[0]])
      setSettingType(RangeSettingTypes.START)
    }
  }, [isLong, viewDate])

  const settingTypeIndex = useMemo(() => {
    let typeIndex = 0
    if (settingType === RangeSettingTypes.END) {
      typeIndex = 1
    }
    return typeIndex
  }, [settingType])

  const onPopupVisibleChangeHandler = (visible: boolean) => {
    setPopupVisible(visible)
    onPopupVisibleChange?.(visible)
  }

  // 日期修改
  const onDateChangeHandler = (dates: PickerValues) => {
    setViewDate((prevDate) => {
      const nextViewDate = cloneDeep(prevDate)
      const newDate = dates[0]
      let dateTime = nextViewDate[settingTypeIndex]
      if (dateTime) {
        // 如果已设置过时间，则只更新 date，time保持不变
        const time = moment(dateTime).format(PickerTimeFormat)
        dateTime = moment(`${newDate} ${time}`).format(format)
      } else {
        dateTime = moment(newDate).format(format)
      }
      nextViewDate.splice(settingTypeIndex, 1, dateTime)
      if (moment(nextViewDate[0]).isAfter(moment(nextViewDate[1]))) {
        const temp = nextViewDate[0]
        nextViewDate[0] = nextViewDate[1]
        nextViewDate[1] = temp
      }
      return nextViewDate
    })
    return true
  }

  // 时间修改
  const onTimeChangeHandler = (time: string) => {
    setViewDate((prevDate) => {
      const nextViewDate = cloneDeep(prevDate)
      let dateTime = nextViewDate[settingTypeIndex]
      if (dateTime) {
        // 如果已设置过日期，则只更新 time，date保持不变
        const date = moment(dateTime).format(PickerDateFormat)
        dateTime = moment(`${date} ${time}`).format(format)
      } else {
        // 如果未设置，则要判断是否设置过其他的日期
        const otherDateTime = nextViewDate.filter(
          (item, index) => index !== settingTypeIndex,
        )[0]
        if (otherDateTime) {
          // 如果设置了其他日期，则 date 使用已设置的日期
          dateTime = moment(
            `${moment(otherDateTime).format(PickerDateFormat)} ${time}`,
          ).format(format)
        } else {
          // 如果未设置了其他日期，则 date 使用当前的日期
          dateTime = moment(
            `${moment().format(PickerDateFormat)} ${time}`,
          ).format(format)
        }
      }
      nextViewDate.splice(settingTypeIndex, 1, dateTime)
      return nextViewDate
    })
  }

  // 快捷键操作
  const onShortcutsDateChange = (date: PickerValues) => {
    setViewDate(date)
    setSettingType(RangeSettingTypes.RESET)
  }

  // 清空操作
  const onClearHandler = () => {
    setViewDate(['', ''])
    setSettingType(RangeSettingTypes.START)
  }

  // 取消操作
  const onCancelHandler = () => {
    // @ts-ignore
    props.onCancel?.()
    setPopupVisible(false)
  }

  // 确认操作
  const onConfirmHandler = async () => {
    const newDate = viewDate.map((date) =>
      date ? moment(date).format(format) : date,
    )
    let params = cloneDeep(newDate)
    if (isLong) {
      params = {
        // @ts-ignore
        isLong: true,
        dateRange: [
          newDate[0],
          moment(newDate[0])
            .add(4 * 365, 'days')
            .format(format),
        ],
      }
    } else {
      const startDate = params[0]
      const endDate = params[1]
      // 确认前比较开始结束日期，确保范围显示正确
      if (startDate && endDate) {
        if (moment(startDate).isAfter(endDate, 'minute')) {
          params = [params[1], params[0]]
        }
      }
    }
    if (onOk) {
      const okResult = await onOk?.(params)
      okResult !== false && onCancelHandler()
    }
    const result = await onChange(params)
    result !== false && onCancelHandler()
  }

  // 判断日期是否禁用
  const isDisabledDate = (date: Moment) => {
    const startDate = viewDate[0]
    const endDate = viewDate[1]
    // 当前设置「结束日期」时，且有「开始日期」时，开始日期之前的需要禁用
    const forwardDisabled =
      settingType === RangeSettingTypes.END &&
      !!startDate &&
      date.isBefore(moment(startDate).format(PickerDateFormat), 'date')
    // 当前设置「开始日期」时，且有「结束日期」不是「长期」时，结束日期之后的需要禁用
    const backwardDisabled =
      !isLong &&
      settingType === RangeSettingTypes.START &&
      !!endDate &&
      date.isAfter(moment(endDate).format(PickerDateFormat), 'date')
    return {
      disabled: forwardDisabled || backwardDisabled,
      forwardDisabled,
      backwardDisabled
    }
  }

  const renderPickerDateHandler = (date: Moment) => renderPickerDate?.(date, isDisabledDate(date))

  const popContent = useMemo(() => {
    // 确认按钮是否禁用，默认值：false
    let okBtnDisabled = false
    if (canEmpty) {
      // 允许为空提交时，只存在一个值时，按钮禁用
      okBtnDisabled =
        viewDate.length === 1 || viewDate.filter((v) => !v).length === 1
    } else {
      // 不允许为空提交时，只要有一个值为空，按钮禁用
      if (isLong) okBtnDisabled = !(viewDate.length === 1 && viewDate[0])
      else okBtnDisabled = !(viewDate.length === 2 && viewDate[0] && viewDate[1])
    }
    const dateTime = viewDate[settingTypeIndex]
      ? moment(viewDate[settingTypeIndex])
      : ''
    return (
      <div className={classNames(PickerRangesClass, prefixCls, className)}>
        <DateInputGroup
          format={format}
          isLong={isLong}
          onChange={setViewDate}
          onFocus={setSettingType}
          settingDateType={settingType}
          viewDate={viewDate}
        />
        <div
          className={classNames(`${PickerRangesClass}__main`)}
          key={settingType}
        >
          <Shortcuts
            onChange={onShortcutsDateChange}
            showPreviousCycle={showPreviousCycle}
          />
          <Picker
            {...restProps}
            displayMode="tiled"
            onChange={onDateChangeHandler}
            renderDate={renderPickerDateHandler}
            value={viewDate}
          />
          <TimePicker
            {...restProps}
            onChange={onTimeChangeHandler}
            value={dateTime ? dateTime.format('HH:mm') : ''}
          />
        </div>
        <RangePickerOperationBar
          disabled={okBtnDisabled}
          isLong={isLong}
          onCancel={onCancelHandler}
          onClear={onClearHandler}
          onOk={onConfirmHandler}
          onSelectLong={setIsLong}
          showClear={showClear}
          showLong={showLongTerm}
        />
      </div>
    )
  }, [isLong, settingType, settingTypeIndex, viewDate])

  // useEffect(() => {
  //   if (isLong) {
  //     oldEndDateRef.current = viewDate[1]
  //     setViewDate([viewDate[0], moment(viewDate[0]).add(4 * 365, 'days')])
  //     setSettingType(RangeSettingTypes.START)
  //   } else {
  //     if (oldEndDateRef.current !== -1) {
  //       setViewDate([viewDate[0], oldEndDateRef.current])
  //     }
  //   }
  // }, [isLong])

  return (
    // @ts-ignore
    <Popup
      destroyPopupOnHide
      extraClass={classNames(PickerDropdownClass)}
      popup={popContent}
      clearPadding
      popupVisible={popupVisible}
      popupPlacement={getPopupPlacement(direction, popupPlacement)}
      onPopupVisibleChange={onPopupVisibleChangeHandler}
      {...restProps}
    >
      <RangePickerTrigger
        format={format}
        focused={popupVisible}
        placeholder={getLocale('Rep_NewS_All')}
        viewDate={values}
        {...restProps}
      >
        {children}
      </RangePickerTrigger>
    </Popup>
  )
}

export default DateTimeRangePicker
