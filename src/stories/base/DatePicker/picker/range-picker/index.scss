.tita-ui__picker-ranges {
  width: auto;
  // height: 353px;
  border-radius: 16px;
  box-shadow: 0px 8px 24px 0px rgba(127, 145, 180, 0.16);
  background: #ffffff;
  overflow: hidden;

  &__main {
    display: flex;
    justify-content: space-between;
  }

  &__shortcuts {
    flex: 1;
    padding: 15px 9px 15px 12px;
    display: grid;
    grid-template: repeat(8, 28px) / 100%;
    gap: 4px;
    position: relative;

    &::after {
      content: '';
      width: 1px;
      height: 277px;
      background: linear-gradient(
        180deg,
        rgba(233, 236, 240, 0) 0%,
        #e9ecf0 48%,
        rgba(233, 236, 240, 0) 100%
      );
      position: absolute;
      top: 0;
      right: 0;
    }

    &-item {
      width: 100%;
      padding: 3px 8px;
      font-size: 14px;
      line-height: 22px;
      border-radius: 8px;
      transition: all 0.3s ease;
      box-sizing: border-box;
      color: #3f4755;
      cursor: pointer;
      white-space: nowrap;

      &:hover {
        background-color: #f0f4fa;
        color: #2879ff;
      }
    }
  }

  &__long {
    display: flex;
    align-items: center;
    
    .titaui-text-btn {
      color: #6f7886;
    }
    > * + * {
      margin-left: 4px;
    }
  }

  //========S reset picker style=========
  .tita-ui__picker {
    border-radius: 0;
    box-shadow: none;
  }
  //========E reset picker style=========
}
