import React, { FC, useEffect, useMemo, useRef, useState } from 'react'
import classNames from 'classnames'
import moment from 'moment'
import { getLocale } from '@tita/utils'
import Popup from '../../../Popup'
import ConditionRender from '../components/condition-render'
import DateInputGroup from '../components/date-input-group'
import RangePickerOperationBar from '../components/range-picker-operation-bar'
import RangePickerTrigger from '../components/range-picker-trigger'
import { RangePickerContext } from '../context'
import { PickerMode } from '../enum'
import Picker, { IPickerProps } from '../picker'
import { PickerSettingType, PickerValue, PickerValues } from '../type'
import useInitializeRangeValue from '../useInitializeRangeValue'
import {
  PickerDropdownClass,
  PickerDateFormat,
  PickerRangesClass,
  RangeSettingTypes,
  getPopupPlacement,
  setPickerDateBySettingType,
} from '../utils'
import Shortcuts from '../components/shortcuts'
import './index.scss'
import { useRefState } from '@tita/hooks'

// @ts-ignore
export interface IRangePickerProps extends IPickerProps {
  /** 是否允许为空提交 */
  canEmpty?: boolean
  /** popup 内容容器 class */
  className?: string
  /** 自定义 trigger 外层容器 class */
  contentClassName?: string
  defaultTimes?: string[]
  /** 结束日期是否是「长期」 */
  isLongTerm?: boolean
  /** 是否展示清除按钮 */
  showClear?: boolean
  /** 是否展示长期按钮 */
  showLongTerm?: boolean
  /** 是否展示上一周期（上周、上月） */
  showPreviousCycle?: boolean
  /** 是否展示下一周期（下周、下月） */
  showNextCycle?: boolean
  visible?: boolean
  value?: PickerValues | { isLong: boolean; dateRange: PickerValues }
  onChange: (
    date: PickerValues | { isLong: boolean; dateRange: PickerValues }
  ) => boolean | Promise<boolean>
  onCancel?: () => void
  onOk?: (
    date: PickerValues | { isLong: boolean; dateRange: PickerValues }
  ) => boolean | Promise<boolean>
  /** 是否展示底部操作栏 */
  showFooter?: boolean
  /** 是否展示快捷时间 */
  showShortcuts?: boolean
}

const RangePicker: FC<IRangePickerProps> = (props) => {
  const {
    canEmpty = true,
    children,
    className,
    displayMode = 'layer',
    direction = 'ltr',
    format = PickerDateFormat,
    isLongTerm = false,
    picker = PickerMode.Date,
    popupPlacement = 'bottomLeft',
    onCancel,
    onChange,
    onOk,
    onPopupVisibleChange,
    showClear,
    showLongTerm = false,
    showPreviousCycle = false,
    showNextCycle,
    value: propsValue,
    // 兼容老代码
    // @ts-ignore
    values: propsValues,
    visible = false,
    showFooter = true,
    showShortcuts = true,
    ...restProps
  } = props
  let values = propsValue || propsValues
  if (typeof values === 'object' && !Array.isArray(propsValue)) {
    // @ts-ignore
    values = values.isLong ? [values.dateRange[0]] : values.dateRange
  }

  // @ts-ignore
  const [isLong, isLongRef, setIsLong] = useRefState<boolean>(
    isLongTerm || values?.isLong
  )
  const [popupVisible, setPopupVisible] = useState(visible)
  const [settingType, setSettingType] = useState<PickerSettingType>(
    RangeSettingTypes.START
  )
  // @ts-ignore
  const [viewDate, setViewDate] = useInitializeRangeValue(
    values,
    [moment(), moment()],
    popupVisible
  )
  useEffect(() => {
    if (isLong && viewDate.length > 1) {
      setViewDate([viewDate[0]])
    }
  }, [isLong, viewDate])

  // const oldEndDateRef = useRef<-1 | any>(-1)
  const settingTypeRef = useRef<PickerSettingType>(RangeSettingTypes.START)

  const onPopupVisibleChangeHandler = (visible: boolean) => {
    setPopupVisible(visible)
    onPopupVisibleChange?.(visible)
  }

  // 日期修改
  const onDateChangeHandler = (date: PickerValues) => {
    const isLong = isLongRef.current
    const nextDate = setPickerDateBySettingType({
      newDate: date[0],
      settingType: settingTypeRef.current,
      values: viewDate,
      isLong,
    })
    const endDate = nextDate?.[1]
    if (
      [RangeSettingTypes.START, RangeSettingTypes.RESET].includes(
        // @ts-ignore
        settingTypeRef.current
      ) &&
      !endDate &&
      !isLong
    ) {
      setSettingType(RangeSettingTypes.END)
    } else if (
      (settingTypeRef.current === RangeSettingTypes.END || !!endDate) &&
      !isLong
    ) {
      setSettingType(RangeSettingTypes.RESET)
    }
    if (!showFooter && endDate) {
      onConfirmHandler(nextDate)
    }
    setViewDate(nextDate)
    return true
  }

  // 快捷键操作
  const onShortcutsDateChange = (date: PickerValues) => {
    setViewDate(date)
    setSettingType(RangeSettingTypes.RESET)
    if (!showFooter) {
      onConfirmHandler(date)
    }
  }

  // 清空操作
  const onClearHandler = () => {
    setViewDate(['', ''])
    setSettingType(RangeSettingTypes.START)
  }

  // 取消操作
  const onCancelHandler = () => {
    onCancel?.()
    setPopupVisible(false)
  }

  // 确认操作
  const onConfirmHandler = async (newViewDate: PickerValues) => {
    const newDate = (newViewDate || viewDate).map((date) =>
      date ? moment(date).format(format) : date
    )
    const params = isLong
      ? {
          isLong: true,
          dateRange: [
            newDate[0],
            moment(newDate[0])
              .add(4 * 365, 'days')
              .format(format),
          ],
        }
      : newDate
    if (onOk) {
      const okResult = await onOk?.(params)
      okResult !== false && onCancelHandler()
    }
    const result = await onChange(params)
    result !== false && onCancelHandler()
  }

  const getPickerSettingType = () => {
    let _settingType = RangeSettingTypes.START
    if (values instanceof Array) {
      const startDate = values[0] || ''
      const endDate = values[1] || ''
      if (!startDate) {
        _settingType = RangeSettingTypes.START
      } else if (!endDate) {
        _settingType = RangeSettingTypes.END
      } else {
        _settingType = RangeSettingTypes.RESET
      }
    }
    setSettingType(_settingType)
  }

  useEffect(() => {
    getPickerSettingType()
    setPopupVisible(visible)
  }, [visible])

  useEffect(() => {
    settingTypeRef.current = settingType
  }, [settingType])

  // useEffect(() => {
  //   if (isLong) {
  //     oldEndDateRef.current = viewDate[1]
  //     setViewDate([viewDate[0], moment(viewDate[0]).add(4 * 365, 'days')])
  //   } else {
  //     if (oldEndDateRef.current !== -1) {
  //       setViewDate([viewDate[0], oldEndDateRef.current])
  //     }
  //   }
  // }, [isLong])

  const popContent = useMemo(() => {
    let okBtnDisabled = canEmpty
      ? viewDate.length === 1 || viewDate.filter((v) => !v).length === 1
      : viewDate.filter((v) => !!v).length !== 2

    if (isLong) okBtnDisabled = !viewDate[0]
    return (
      <div className={classNames(PickerRangesClass, className)}>
        {/* @ts-ignore */}
        <DateInputGroup
          format={format}
          isLong={isLong}
          onChange={setViewDate}
          onFocus={setSettingType}
          settingDateType={settingType}
          viewDate={viewDate}
        />
        <div className={classNames(`${PickerRangesClass}__main`)}>
          {showShortcuts && (
            <Shortcuts
              onChange={onShortcutsDateChange}
              showPreviousCycle={showPreviousCycle}
            />
          )}

          <Picker
            // @ts-ignore
            onChange={onDateChangeHandler}
            picker={picker}
            settingType={settingType}
            {...restProps}
            value={viewDate}
            displayMode='tiled'
            multiple
          />
        </div>
        <ConditionRender condition={showFooter}>
          <RangePickerOperationBar
            showLong={showLongTerm}
            showClear={showClear}
            isLong={isLong}
            onSelectLong={(value) => {
              if (!value) setSettingType(RangeSettingTypes.END)
              setIsLong(value)
            }}
            disabled={okBtnDisabled}
            onCancel={onCancelHandler}
            onClear={onClearHandler}
            // @ts-ignore
            onOk={() => onConfirmHandler()}
          />
        </ConditionRender>
      </div>
    )
  }, [isLong, canEmpty, settingType, viewDate])

  return (
    <RangePickerContext.Provider
      value={{
        // @ts-ignore
        values,
        picker,
        format,
        settingType,
      }}
    >
      <ConditionRender condition={displayMode === 'layer'}>
        {/* @ts-ignore */}
        <Popup
          destroyPopupOnHide
          clearPadding
          extraClass={classNames(PickerDropdownClass)}
          popup={popContent}
          popupVisible={popupVisible}
          popupPlacement={getPopupPlacement(direction, popupPlacement)}
          onPopupVisibleChange={onPopupVisibleChangeHandler}
          {...restProps}
        >
          <RangePickerTrigger
            focused={popupVisible}
            format={format}
            placeholder={getLocale('Rep_NewS_All')}
            // @ts-ignore
            viewDate={values}
            {...restProps}
          >
            {children}
          </RangePickerTrigger>
        </Popup>
      </ConditionRender>
      <ConditionRender condition={displayMode === 'tiled'}>
        {popContent}
      </ConditionRender>
    </RangePickerContext.Provider>
  )
}

export default RangePicker
