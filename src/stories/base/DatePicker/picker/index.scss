.tita-ui__picker {
  width: 276px;
  max-height: 292px;
  padding: 15px 18px 16px;
  border-radius: 16px;
  box-shadow: 0px 8px 24px 0px rgba(127, 145, 180, 0.16);
  background-color: #ffffff;
  box-sizing: border-box;
  overflow: hidden;

  &-dateTime {
    &.tita-ui__picker {
      width: 428px;
      max-height: 345px;
      padding: 0;
    }
  }

  &-unit {
    font-size: 14px;
    font-weight: 600;
    color: #141c28;
  }
}

.tita-ui__picker-cell {
  width: 24px;
  height: 24px;
  padding: 4px 6px;
  font-size: 12px;
  line-height: 18px;
  color: #6f7886;
  box-sizing: content-box;

  &--in-view {
    .tita-ui__picker-cell__inner {
      color: #141c28;
    }
  }

  &--today,
  &:hover {
    .tita-ui__picker-cell__inner {
      background-color: #f0f4fa;
      color: #2879ff;
    }
  }

  &--in-ranges {
    position: relative;

    &::after {
      content: '';
      background-color: rgba(40, 121, 255, 0.1);
      pointer-events: none;
      position: absolute;
      top: 4px;
      right: 0;
      bottom: 4px;
      left: 0;
    }

    &--start.tita-ui__picker-cell--selected::after {
      left: 10px;
    }

    &--end.tita-ui__picker-cell--selected::after {
      right: 10px;
    }
  }

  &--disabled {
    cursor: not-allowed;

    .tita-ui__picker-cell__inner {
      background-color: transparent;
      color: #6f7886;
      cursor: not-allowed;
      pointer-events: unset !important;
    }

    &:hover {
      .tita-ui__picker-cell__inner {
        background-color: transparent;
        color: #6f7886;
      }
    }
  }

  &--selected {
    .tita-ui__picker-cell__inner {
      background-color: #5c8eff;
      color: #ffffff;
    }

    &.tita-ui__picker-cell--in-view {
      .tita-ui__picker-cell__inner {
        background-color: #2879ff;
      }
    }

    &:hover {
      .tita-ui__picker-cell__inner {
        background-color: #5c8eff;
        color: #ffffff;
      }
    }
  }

  &__inner {
    width: 100%;
    height: 100%;
    font-size: 12px;
    line-height: 18px;
    border-radius: 8px;
    transition: all 0.3s ease;
    color: #a4acb9;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
  }
}

.tita-ui__picker-content {

  thead th,
  tbody tr,
  tbody tr td {
    &:first-child {
      padding-left: 0;
    }

    &:last-child {
      padding-right: 0;

      td {
        padding-bottom: 0;
      }
    }
  }
}

.tita-ui__picker-head {
  background-color: #ffffff;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;

  &__operation {
    display: grid;
    grid-template: auto / repeat(3, auto);
    gap: 8px;
  }

  &__view {
    flex: 1;
  }
}

.tita-ui__picker-head-btn {

  &__prev,
  &__next,
  &__year,
  &__month,
  &__today {
    min-width: 24px;
    height: 24px;
    border-radius: 8px;
    transition: all 0.3s ease;
    background-color: transparent;
    border: none;
    color: #141c28;
    display: inline-block;

    &:hover {
      background-color: #f0f4fa;
      color: #2879ff;
    }
  }

  &__prev,
  &__next {
    padding: 0;
    font-size: 12px;
    line-height: 18px;
    color: #3f4755;

    &:disabled {
      color: #6f7886;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
        color: #6f7886;
      }
    }
  }

  &__year,
  &__month {
    padding: 1px 2px;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
  }

  &__today {
    font-size: 12px;
    color: #3f4755;
  }

  &--readonly {
    cursor: text;

    &:hover {
      background-color: #ffffff;
      color: #141c28;
    }
  }
}
