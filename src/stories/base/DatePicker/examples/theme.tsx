import DatePicker from '@/stories/base/DatePicker'
import React, { FC } from 'react'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  return (
    <div className='flex flex-wrap childs-mb-8px'>
      <div className='w-1/2'>
        <DatePicker placeholder="请选择" />
      </div>
      <div className='w-1/2'>
        <DatePicker placeholder="请选择" mode="button" second />
      </div>
      <div className='w-1/2'>
        <DatePicker placeholder="请选择" mode="tag" />
      </div>
      <div className='w-1/2'>
        <DatePicker placeholder="请选择" mode="text" />
      </div>
      <div className='w-1/2'>
        <DatePicker placeholder="请选择" mode="inline" />
      </div>
    </div>
  )
}

export default React.memo(Base)
