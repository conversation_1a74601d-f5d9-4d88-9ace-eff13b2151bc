import DatePicker, {
  DateRangePicker,
  DateTimeRangePicker,
} from '@/stories/base/DatePicker'
import React, { FC } from 'react'
import Panel from '../../Panel'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  return (
    <div className='flex flex-wrap childs-mb-8px'>
      <Panel className='w-1/2' title='日期'>
        <DatePicker />
      </Panel>
      <Panel className='w-1/2' title='日期加时间'>
        <DatePicker picker='dateTime' format='YYYY/MM/DD HH:mm' />
      </Panel>
      <Panel className='w-1/2' title='周'>
        <DatePicker picker='week' format='YYYY/w' />
      </Panel>
      <Panel className='w-1/2' title='月份'>
        <DatePicker picker='month' format='YYYY/MM' />
      </Panel>
      <Panel className='w-1/2' title='年份'>
        <DatePicker picker='year' format='YYYY' />
      </Panel>
      <Panel className='w-1/2' title='日期范围'>
        <DateRangePicker showPreviousCycle picker='date' showLongTerm />
      </Panel>
      <Panel className='w-1/2' title='月份范围-快捷'>
        <DateRangePicker picker='month' showPreviousCycle format='YYYY/MM' canClear={false} />
      </Panel>
      <Panel className='w-1/2' title='日期时间范围'>
        <DateTimeRangePicker canClear defaultTimes={['00:00', '23:59']} />
      </Panel>
      <Panel className='w-1/2' title='日期时间范围-快捷'>
        <DateTimeRangePicker
          canClear
          showNextCycle
          defaultSettingType='endDate'
        />
      </Panel>
      <Panel className='w-1/2' title='日期时间范围-支持长期'>
        <DateTimeRangePicker canClear showNextCycle showLongTerm showClear />
      </Panel>
      <Panel className='w-1/2' title='日期时间范围-不展示确认'>
        <DateTimeRangePicker canClear showNextCycle showFooter={false} />
      </Panel>
    </div>
  )
}

export default React.memo(Base)
