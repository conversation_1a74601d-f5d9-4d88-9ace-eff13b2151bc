import React, { FC, useCallback, useEffect, useMemo, useState } from 'react'
import classNames from 'classnames'
import './index.scss'
import SelectView, { ISelectViewProps } from '../SelectView'
import { getLocale } from '@tita/utils'
import Picker from './picker'
import { useUpdateEffect } from 'ahooks'
import DateRangeView from '../DateRangeView'
import { IPickerProps } from './picker/picker'
import { IRangePickerProps } from './picker/range-picker'
import { getInitialDateRangeValue } from './utils'

export type DateRangeValue = string[] | { isLong: boolean; dateRange: string[] }

// @ts-ignore
export interface IDatePickerProps extends ISelectViewProps, IPickerProps {
  value?: string
  initialValue?: string[]
  disabled?: boolean
  format?: string
  onChange?: (value: string) => void
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-date-picker'

export const DatePicker: FC<IDatePickerProps> = React.memo(
  ({
    value: propsValue,
    initialValue,
    disabled,
    onChange,
    placeholder,
    className,
    style,
    children,
    ...other
  }) => {
    const [value, setValue] = useState(initialValue || propsValue)
    useUpdateEffect(() => {
      setValue(propsValue)
    }, [propsValue])

    const onChangeHandler = useCallback(
      (value: string[]) => {
        onChange?.(value ? value[0] : value)
        setValue(value ? value[0] : value)
      },
      [onChange]
    )
    if (disabled)
      return (
        <SelectView
          className={classNames(preCls, className)}
          style={style}
          placeholder={placeholder}
          {...other}
        >
          {value}
        </SelectView>
      )
    return (
      // @ts-ignore
      <Picker
        clearAble
        value={value ? [value] : undefined}
        onChange={onChangeHandler}
        placeholder={getLocale('Per_Manage_pro_seltim')}
        {...other}
      >
        {children || (
          <SelectView
            className={classNames(preCls, className)}
            style={style}
            placeholder={placeholder}
            excludeClassName={['tita-ui__picker']}
            {...other}
          >
            {value}
          </SelectView>
        )}
      </Picker>
    )
  }
)

// @ts-ignore
export interface IDateRangePickerProps
  extends Omit<ISelectViewProps, 'onClick' | 'placeholder'>,
    IRangePickerProps {
  value?: DateRangeValue
  initialValue?: DateRangeValue
  disabled?: boolean
  format?: string
  onChange?: (value: DateRangeValue) => void
  placeholder?: React.ReactNode
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

export const DateRangePicker: FC<IDateRangePickerProps> = React.memo(
  ({
    value: propsValue,
    initialValue,
    disabled,
    onChange,
    placeholder,
    children,
    className,
    style,
    ...other
  }) => {
    const [value, setValue] = useState(initialValue || propsValue)
    useUpdateEffect(() => {
      setValue(propsValue)
    }, [propsValue])

    const onChangeHandler = useCallback(
      (value: string[]) => {
        onChange?.(value)
        setValue(value)
      },
      [onChange]
    )

    const [startDate, endDate, isLongTerm = false] = useMemo(() => {
      if (Array.isArray(value)) {
        return [value[0], value[1], false]
      }
      return [value?.dateRange?.[0], value?.dateRange?.[1], value?.isLong]
    }, [value])

    if (disabled)
      return children ? (
        <>{children}</>
      ) : (
        // @ts-ignore
        <DateRangeView
          startDate={startDate}
          endDate={endDate}
          onClear={() => onChangeHandler([])}
          isLongTerm={isLongTerm}
          style={style}
          excludeClassName={['tita-ui__picker-ranges']}
          {...other}
        />
      )

    return (
      // @ts-ignore
      <Picker.Range
        clearAble
        value={value}
        onChange={onChangeHandler}
        canEmpty={other.canClear}
        {...other}
      >
        {children || (
          // @ts-ignore
          <DateRangeView
            startDate={startDate}
            endDate={endDate}
            onClear={() => onChangeHandler([])}
            isLongTerm={isLongTerm}
            style={style}
            excludeClassName={['tita-ui__picker-ranges']}
            {...other}
          />
        )}
      </Picker.Range>
    )
  }
)
// @ts-ignore
export interface IDateTimeRangePickerProps
  extends ISelectViewProps,
    IRangePickerProps {
  value?: DateRangeValue
  initialValue?: DateRangeValue
  disabled?: boolean
  format?: string
  onChange?: (value: DateRangeValue) => void
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

export const DateTimeRangePicker: FC<IDateTimeRangePickerProps> = React.memo(
  ({
    value: propsValue,
    initialValue,
    disabled,
    onChange,
    placeholder,
    children,
    className,
    style,
    ...other
  }) => {
    const [value, setValue] = useState(() =>
      getInitialDateRangeValue(initialValue || propsValue)
    )
    useUpdateEffect(() => {
      setValue(getInitialDateRangeValue(propsValue))
    }, [propsValue])

    const onChangeHandler = useCallback(
      (value: string[]) => {
        onChange?.(value)
        setValue(value)
      },
      [onChange]
    )
    const [startDate, endDate, isLongTerm = false] = useMemo(() => {
      if (Array.isArray(value)) {
        return [value[0], value[1], false]
      }
      return [value?.dateRange?.[0], value?.dateRange?.[1], value?.isLong]
    }, [value])

    if (disabled)
      return children ? (
        <>{children}</>
      ) : (
        // @ts-ignore
        <DateRangeView
          startDate={startDate}
          endDate={endDate}
          onClear={() => onChangeHandler([])}
          isLongTerm={isLongTerm}
          style={style}
          excludeClassName={['tita-ui__picker-ranges']}
          {...other}
        />
      )

    return (
      // @ts-ignore
      <Picker.DateTimeRange
        clearAble
        value={value}
        onChange={onChangeHandler}
        showTime
        canEmpty={other.canClear}
        {...other}
      >
        {children || (
          // @ts-ignore
          <DateRangeView
            startDate={startDate}
            endDate={endDate}
            showTime
            onClear={() => onChangeHandler([])}
            isLongTerm={isLongTerm}
            style={style}
            excludeClassName={['tita-ui__picker-ranges']}
            {...other}
          />
        )}
      </Picker.DateTimeRange>
    )
  }
)

export default DatePicker
