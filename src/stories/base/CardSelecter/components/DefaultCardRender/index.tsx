import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'
import { CardSelecterOption } from '../..'

export interface IDefaultCardRenderProps extends CardSelecterOption {
  selected?: boolean
  onChangeSelected?: (selected?: boolean) => void
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-default-card-render'

export const DefaultCardRender: FC<IDefaultCardRenderProps> = React.memo(({ label, value, cover, selected, onChangeSelected, className, style }) => {
  return (
    <div className={classNames(preCls, className)} style={style}>
     
    </div>
  )
})

export default DefaultCardRender
