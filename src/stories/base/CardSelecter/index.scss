.tita-ui-card-selecter {
  &__card {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 150px;
    border-radius: 12px;
    border: 1px solid #e4ecfa;
    cursor: pointer;
    overflow: hidden;

    &-disabled {
      cursor: not-allowed;
    }

    &-checkbox {
      position: absolute;
      top: 4px;
      right: 4px;
      border-radius: 50%;
      z-index: 4;

      .tita-ui-checkbox {
        border-radius: 50%;
      }
      background-color: #fff;
    }

    &-cover {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;

      &-img {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    &-title {
      height: 44px;
      flex-shrink: 0;
      background: #ffffff;
      border-radius: 12px 0px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      font-size: 14px;
      color: #3f4755;
      line-height: 22px;
    }

    &-wiki {
      font-size: 16px;
      color: #bfc7d5;
      margin-left: 4px;
      display: flex;
      align-items: center;

      &:hover {
        color: #2879ff;
      }
    }
  }
}
