import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'
import Carousel, { ICarouselProps } from '../Carousel'
import { useFormItemValue } from '@/hooks/useFormItemValue'
import styled from 'styled-components'
import CheckBox from '../CheckBox'
import InnerShadow from '../InnerShadow'
import ListContainer from '../ListContainer'
import Tooltip from '../Tooltip'

export interface CardSelecterOption {
  label: React.ReactNode
  value: string
  cover: string | React.ReactNode
  disabled?: boolean
  checkboxStyle?: React.CSSProperties
  tooltip?: {
    icon?: React.ReactNode
    title?: React.ReactNode
    link?: string
  }
  [key: string]: any
}

export interface ICardSelecterProps extends Omit<ICarouselProps, 'children'> {
  options: CardSelecterOption[]
  value?: string
  defaultValue?: string
  onChange?: (value: string) => void
  disabled?: boolean
  required?: boolean
  height?: number | string
  spaceX?: number | string
  /** 自定义渲染 */
  render?: (props: {
    value: string
    index: number
    option: CardSelecterOption
    selected: boolean
    onChangeSelected: () => void
    disabled?: boolean
  }) => React.ReactNode
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-card-selecter'

export const CardSelecter: FC<ICardSelecterProps> = React.memo(
  ({
    options,
    required,
    className,
    style,
    render,
    spaceX = 12,
    unitWidth = 198,
    height = 150,
    ...other
  }) => {
    const formValue = useFormItemValue(other)
    if (!options) return null

    return (
      <ListContainer
        className={classNames(preCls, className)}
        style={{
          height,
          ...style,
        }}
        containerStyle={{
          gap: spaceX,
        }}
        itemSelecter='.tita-ui-card-selecter__card'
        {...other}
      >
        {options.map((option, index) => {
          const selected = formValue.value === option.value

          let tooltip = null
          if (option.tooltip) {
            const tooltipIcon = option.tooltip?.icon || (
              <i className='tu-icon-wiki' />
            )
            tooltip = option.tooltip?.link ? (
              <a
                className={`${preCls}__card-wiki`}
                href={option.tooltip?.link}
                target='_blank'
              >
                {tooltipIcon}
              </a>
            ) : (
              <span className={`${preCls}__card-wiki`}>{tooltipIcon}</span>
            )
            if (option.tooltip?.title) {
              tooltip = (
                <Tooltip overlay={option.tooltip?.title}>{tooltip}</Tooltip>
              )
            }
          }

          const onChangeSelected = () => {
            if (option.disabled || other.disabled) return
            if (required) formValue.set(option.value)
            // @ts-ignore
            else formValue.set(selected ? undefined : option.value)
          }

          if (render) {
            return render({
              value: option.value,
              index,
              option,
              selected,
              onChangeSelected,
              disabled: option.disabled || other.disabled,
            })
          }

          return (
            <div
              key={option.value}
              className={classNames(`${preCls}__card`, {
                [`${preCls}__card-selected`]: selected,
                [`${preCls}__card-disabled`]: other.disabled,
              })}
              style={{
                width: unitWidth,
              }}
              onClick={() => onChangeSelected()}
            >
              {!(other.disabled && !selected) && (
                <CheckBox
                  checked={selected}
                  disabled={option.disabled || other.disabled}
                  size='large'
                  className={`${preCls}__card-checkbox`}
                  style={{
                    ...option.checkboxStyle,
                  }}
                />
              )}
              <div className={`${preCls}__card-cover`}>
                {typeof option.cover === 'string' ? (
                  <img
                    className={`${preCls}__card-cover-img`}
                    src={option.cover}
                  />
                ) : (
                  option.cover
                )}
                <InnerShadow bottom />
              </div>
              <div className={`${preCls}__card-title`}>
                <p>{option.label}</p>
                {tooltip}
              </div>
            </div>
          )
        })}
      </ListContainer>
    )
  }
)

const CardStyle = styled.div``

export default CardSelecter
