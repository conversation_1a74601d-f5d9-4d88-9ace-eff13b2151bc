import React, { FC } from 'react'
import CardSelecter, { CardSelecterOption } from '@/stories/base/CardSelecter'

export interface IBaseProps {}

const options: CardSelecterOption[] = [
  {
    label: '状态模式',
    value: '1',
    cover:
      'https://xfile6.tita.com/ux/tita-home-page/public/target-evaluate-4.png',
    checkboxStyle: {
      boxShadow: '0 0 0 4px #D4E4FF',
    },
    tooltip: {
      link: 'https://www.tita.com',
    },
  },
  {
    label: '节点流程模式',
    value: '2',
    cover:
      'https://xfile6.tita.com/ux/tita-home-page/public/target-evaluate-4.png',
    checkboxStyle: {
      boxShadow: '0 0 0 4px #FFEAE6',
    },
  },
]

const Base: FC<IBaseProps> = ({}) => {
  return <CardSelecter value='1' disabled options={options} />
}

export default React.memo(Base)
