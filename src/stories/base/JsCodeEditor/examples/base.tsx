import React, { <PERSON> } from 'react'
import JsCodeEditor from '@/stories/base/JsCodeEditor'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {

  const onEditorChange = (value: string) => {
    console.log('🚀 ~ file: base.tsx:21 ~ onEditorChange ~ value:', value)
  }

  return (
    <section>
      <JsCodeEditor
        name='example-editor-1'
        onChange={onEditorChange}
        placeholder='请输入'
        readOnly
      />
    </section>
  )
}

export default React.memo(Base)
