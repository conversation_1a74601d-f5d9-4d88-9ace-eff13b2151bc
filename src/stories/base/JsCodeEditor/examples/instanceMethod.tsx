import React, { FC, useRef } from 'react'
import JsCodeEditor, { IJsCodeEditorRef } from '@/stories/base/JsCodeEditor'
import <PERSON><PERSON> from '../../Button'
import './index.scss'

export interface IInstanceMethodProps {}

const InstanceMethod: FC<IInstanceMethodProps> = ({}) => {
  const jsCodeEditorRef = useRef<IJsCodeEditorRef>({} as IJsCodeEditorRef)

  const onEditorChange = (value: string) => {
    console.log(
      '🚀 ~ file: InstanceMethod.tsx:21 ~ onEditorChange ~ value:',
      value
    )
  }

  const onInsertContent = () => {
    jsCodeEditorRef.current.insert(Math.floor(Math.random() * 11).toString())
  }

  const onRemoveContent = () => jsCodeEditorRef.current.remove()

  const onUndo = () => jsCodeEditorRef.current.undo()

  const onClear = () => jsCodeEditorRef.current.clear()

  const onFocus = () => jsCodeEditorRef.current.focus()

  const getTokenize = async () => {
    const [error, data] = await jsCodeEditorRef.current.getTokenize()
    console.log("🚀 ~ file: instanceMethod.tsx:32 ~ getTokenize ~ tokenize:", error, data)
  }

  return (
    <section className='jsCodeEditor-example__instance-method'>
      <JsCodeEditor
        defaultValue={`
function onLoad(editor) {
  console.log("i've loaded");
}
        `}
        name='example-editor-2'
        placeholder='请输入'
        onChange={onEditorChange}
        ref={jsCodeEditorRef}
      />
      <div className='button-group'>
        <Button primary onClick={onFocus}>
          Activation editor
        </Button>
        <Button second onClick={onInsertContent}>
          Insert a random number 0-10
        </Button>
        <Button gray onClick={onRemoveContent}>
          Backspace
        </Button>
        <Button orange onClick={onUndo}>
          Undo
        </Button>
        <Button danger onClick={onClear}>
          Clear
        </Button>
        <Button danger onClick={getTokenize}>
          get tokenize
        </Button>
      </div>
    </section>
  )
}

export default React.memo(InstanceMethod)
