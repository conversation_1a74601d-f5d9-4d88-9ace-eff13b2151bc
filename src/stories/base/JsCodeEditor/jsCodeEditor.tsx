import React, { Ref, forwardRef, useImperativeHandle } from 'react'
import * as AceBuilds from 'ace-builds'
import ace, { Ace } from 'ace-builds'
import { useDebounceFn } from 'ahooks'
import classNames from 'classnames'
import { tokenize } from 'esprima'
import AceEditor, { IAceEditorProps } from 'react-ace'
import { IAceOptions, IEditorProps } from './interface'
import './index.scss'

import 'ace-builds/src-noconflict/mode-javascript'
import 'ace-builds/src-noconflict/theme-xcode'
import 'ace-builds/src-noconflict/ext-language_tools'

const preCls = 'tita-ui--js-code-editor'

export interface IJsCodeEditorProps extends IAceEditorProps {
  /** 用于编辑器的唯一 ID
   * @default tita-js-code-editor
   */
  name?: string
  /** 自定义类名 */
  className?: string
  /** 编辑器默认值 */
  defaultValue?: string
  /** 编辑器中填充的值 */
  value?: string
  /** 字体大小
   * @default 16
   */
  fontSize?: number | string
  /** 是否高亮当前选中行
   * @default false
   */
  highlightActiveLine?: boolean
  /** 最小行数 */
  minLines?: number
  /** 最大行数 */
  maxLines?: number
  /** 为空提示文案 */
  placeholder?: string
  /** 是否开启只读模式
   * @default false
   */
  readOnly?: boolean
  /** 是否显示侧边栏，与 setOptions 中 showLineNumbers 等属性配合使用
   * @default true
   */
  showGutter?: boolean
  /** 编辑器高度
   * @default 500
   */
  height?: string
  /** 编辑器宽度
   * @default 500
   */
  width?: string
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 直接应用于 Ace 编辑器实例的选项 */
  setOptions?: IAceOptions
  /** 代码缩进
   * @default 2
   */
  tabSize?: number
  /** 是否自动换行
   * @default true
   */
  wrapEnabled?: boolean
  /** 是否启用基础代码自动提示补全
   * @default false
   */
  enableBasicAutocompletion?: boolean | string[]
  /** 是否启用实时代码自动提示补全
   * @default false
   */
  enableLiveAutocompletion?: boolean | string[]
  /** For available modes see https://github.com/thlorenz/brace/tree/master/mode
   * @default javascript
   */
  mode?: string | object
  /** For available themes see https://github.com/thlorenz/brace/tree/master/theme
   * @default xcode
   */
  theme?: string
  onBeforeLoad?: (ace: typeof AceBuilds) => void
  onBlur?: (event: any, editor?: Ace.Editor) => void
  onChange?: (value: string, event?: any) => void
  onCopy?: (value: string) => void
  onCursorChange?: (value: any, event?: any) => void
  onFocus?: (event: any, editor?: Ace.Editor) => void
  onInput?: (event?: any) => void
  onLoad?: (editor: Ace.Editor) => void
  onPaste?: (value: string) => void
  onScroll?: (editor: IEditorProps) => void
  onSelection?: (selectedText: string, event?: any) => void
  onSelectionChange?: (value: any, event?: any) => void
  onValidate?: (annotations: Ace.Annotation[]) => void
}

export type IJsCodeEditorGetTokenizeResult = [
  error: any,
  data: { type: string; value: string }[]
]
export interface IJsCodeEditorRef {
  clear: () => void
  focus: () => void
  getValue: () => string
  getTokenize: () => IJsCodeEditorGetTokenizeResult
  insert: (content: string) => void
  remove: () => void
  undo: () => void
}

const default_editor_config: IAceEditorProps = {
  fontSize: 16,
  highlightActiveLine: false,
  mode: 'javascript',
  setOptions: {
    /** 是否显示缩进指示符 */
    displayIndentGuides: false,
    /** 是否高亮侧边栏 */
    highlightGutterLine: false,
    /** 是否显示代码折叠工具 */
    showFoldWidgets: false,
    /** 是否显示行号 */
    showLineNumbers: true,
  },
  showGutter: true,
  tabSize: 2,
  theme: 'xcode',
  height: '500px',
  width: '500px',
  wrapEnabled: true,
}

export const JsCodeEditor = React.memo(
  forwardRef(
    (
      {
        name = 'tita-js-code-editor',
        onChange,
        ...restProps
      }: IJsCodeEditorProps,
      ref: Ref<IJsCodeEditorRef>
    ) => {
      const { run: debounceChange } = useDebounceFn(
        (value: string, event?: any) => onChange?.(value, event),
        {
          wait: 500,
          leading: true,
          trailing: true,
        }
      )

      let editorInstance: Ace.Editor = (null as any)
      /** 获取编辑器实例 */
      const getEditorInstance = () => {
        if (!editorInstance) {
          editorInstance = ace.edit(name)
        }
        return editorInstance
      }

      /** 激活编辑器 */
      const handleFocusEditor = () => {
        const editor = getEditorInstance()
        editor.focus()
      }

      /** 插入内容
       * @param {string} content
       */
      const handleInsert = (content: string) => {
        const editor = getEditorInstance()
        const position = editor.getCursorPosition()
        const row = position.row
        const column = position.column
        editor.session.insert({ row, column }, content)
        editor.focus()
      }

      /** 删除内容
       * 1. 退格删除
       * 2. 删除选中内容
       */
      const handleRemove = () => {
        const editor = getEditorInstance()
        const selection = editor.getSelectionRange()
        const position = editor.getCursorPosition()
        const row = position.row
        const column = position.column

        if (selection.isEmpty()) {
          if (column > 0) {
            const range = new ace.Range(row, column - 1, row, column)
            editor.session.remove(range)
          } else if (row > 0) {
            const prevLineLength = editor.session.getLine(row - 1).length
            const range = new ace.Range(row - 1, prevLineLength, row, 0)
            editor.session.remove(range)
          }
        } else {
          editor.session.remove(selection)
        }
        editor.focus()
      }

      /** 撤销 */
      const handleUndo = () => {
        const editor = getEditorInstance()
        editor.session.getUndoManager().undo(editor.session)
        editor.focus()
      }

      /** 清空 */
      const handleClear = () => {
        const editor = getEditorInstance()
        editor.setValue('')
        editor.focus()
      }

      /** 获取 tokenize */
      const handleGetTokenize = () => {
        const editor = getEditorInstance()
        const value = editor.getValue()
        let tokenizeData: { type: string; value: string }[] = []
        try {
          tokenizeData = tokenize(value)
          return [null, tokenizeData] as IJsCodeEditorGetTokenizeResult
        } catch (error) {
          return [error, []] as IJsCodeEditorGetTokenizeResult
        }
      }

      /** 获取编辑器内容 */
      const handleGetValue = () => {
        const editor = getEditorInstance()
        const value = editor.getValue()
        return value
      }

      useImperativeHandle(ref, () => ({
        clear: handleClear,
        focus: handleFocusEditor,
        getValue: handleGetValue,
        getTokenize: handleGetTokenize,
        insert: handleInsert,
        remove: handleRemove,
        undo: handleUndo,
      }))

      const props: IAceEditorProps = {
        ...default_editor_config,
        ...restProps,
        setOptions: {
          ...default_editor_config.setOptions,
          ...restProps.setOptions,
        },
      }
      return (
        <div
          className={classNames(preCls, {
            readOnly: restProps.readOnly
          })}
          style={{ width: props.width, height: props.height }}
        >
          <AceEditor {...props} name={name} onChange={debounceChange} />
        </div>
      )
    }
  )
)

export default JsCodeEditor
