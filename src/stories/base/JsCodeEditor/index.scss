.tita-ui--js-code-editor {

  * {
    font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', 'Consolas',
    'source-code-pro', monospace;
  }

  &.readOnly {

    .ace_cursor {
      display: none !important;
    }
  }

  //=============START 编辑器样式重置============
  .ace_editor {
    line-height: 24px;

    .ace_gutter-cell {
      font-family: OPPOSans, Arial, Verdana, Helvetica Neue, Helvetica,
        PingFang SC, Microsoft YaHei, sans-serif;
    }
  }
  /** xcode 主题样式 */
  .ace-xcode {
    .ace_cursor {
      color: #2879ff;
    }

    .ace_gutter {
      background-color: transparent;
    }

    .ace_gutter-cell {
      padding: 0 6px;
      color: #bfc7d5;
      text-align: center;

      &.ace_error,
      &.ace_info,
      &.ace_warning {
        background-image: none;
      }
    }

    .ace_placeholder {
      margin: 0;
      color: #bfc7d5;
    }

    .ace_string {
      color: #2879ff;
    }

    // 代码注释字体颜色
    .ace_comment {
      color: #bfc7d5;
    }

    // 括号等符号标识
    .ace_marker-layer .ace_bracket {
      margin: 0;
    }
  }
  //=============END 编辑器样式重置============
}
