import React from 'react'
import './index.scss'

export interface IErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

interface ErrorBoundaryState {
  hasError: boolean
}
export class ErrorBoundary extends React.Component<
  IErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: IErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError() {
    // 更新 state 以触发下一次渲染时显示降级 UI
    return { hasError: true }
  }

  componentDidCatch(error: any, errorInfo: any) {
    // 你也可以将错误日志上报给服务器
    console.error('Error caught by ErrorBoundary: ', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      // 你可以自定义降级 UI
      return <h1>Something went wrong.</h1>
    }

    return this.props.children
  }
}

export default ErrorBoundary
