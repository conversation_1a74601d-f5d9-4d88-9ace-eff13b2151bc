import React, { FC, useState } from 'react'
import Overflow from '@/stories/base/Overflow'
import Button from '../../Button'
import Resize from '../../Resize'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  const [datas, setDatas] = useState<{ label: number; value: number }[]>(
    new Array(10).fill(0).map((_, i) => ({
      label: i,
      value: i,
    }))
  )
  return (
    <div className='flex'>
      <div>
        <Button
          onClick={() => {
            setDatas(datas.slice(0, -1))
          }}
        >
          -
        </Button>
        <Button
          onClick={() => {
            setDatas([...datas, { label: datas.length, value: datas.length }])
          }}
        >
          +
        </Button>
      </div>
      <Resize width={200} className='border border-solid border-red-500'>
        <Overflow
          datas={datas}
          renderItem={(item) => (
            <div
              key={item.value}
              className='w-[30px] border border-solid border-green-500 shrink-0'
            >
              {item.label}
            </div>
          )}
          renderRest={(items) => <div>{items.length}</div>}
          rowKey='value'
        />
      </Resize>
    </div>
  )
}

export default React.memo(Base)
