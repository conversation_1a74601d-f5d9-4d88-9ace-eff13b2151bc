import { useDebounceEffect, useSize, useUpdateEffect } from 'ahooks'
import classNames from 'classnames'
import React, { FC, useContext, useEffect, useRef, useState } from 'react'
import { OverflowContext } from './context'
import './index.scss'

export interface IOverflowProps<Record = any> {
  datas: Record[]
  renderItem: (data: Record) => React.ReactNode
  renderRest: (datas: Record[]) => React.ReactNode
  onVisibleChange?: (event: {
    visibleIndex: number
    visibleDatas: Record[]
    restDatas: Record[]
  }) => void
  onRestLengthChange?: (event: {
    visibleIndex: number
    visibleDatas: Record[]
    restDatas: Record[]
  }) => void
  rowKey: keyof Record
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-overflow'

export const OverflowItem: FC<{
  children: React.ReactNode
  index: number
  visible: boolean
}> = React.memo(({ children, index }) => {
  const { setItemsWidth } = useContext(OverflowContext)
  const itemRef = useRef<HTMLDivElement>(null)
  const { width } = useSize(itemRef) || { width: 0 }
  useUpdateEffect(() => {
    setItemsWidth((prev) => {
      prev[index] = width
      return [...prev]
    })
  }, [index, width])

  return (
    <div ref={itemRef} className={classNames(`${preCls}__item`)}>
      {children}
    </div>
  )
})

export const Overflow: FC<IOverflowProps> = React.memo(
  ({
    datas,
    renderItem,
    renderRest,
    onVisibleChange,
    onRestLengthChange,
    rowKey,
    className,
    style,
  }) => {
    const [itemsWidth, setItemsWidth] = useState<number[]>([])

    const containerRef = useRef<HTMLDivElement>(null)
    const { width: containerWidth } = useSize(containerRef) || { width: 0 }

    const resetRef = useRef<HTMLDivElement>(null)
    const { width: resetWidth } = useSize(resetRef) || { width: 0 }

    const [restItems, setRestItems] = useState<any[]>([])
    const restItemsLengthRef = useRef(0)
    const [visibleIndex, setVisibleIndex] = useState<number>(0)
    const visibleIndexRef = useRef(0)

    const datasRef = useRef(datas)
    datasRef.current = datas

    const [finish, setFinish] = useState<boolean>(false)
    useEffect(() => {
      if (
        containerWidth &&
        itemsWidth.length === datas?.length &&
        itemsWidth.every(Boolean)
      ) {
        setFinish(true)
      }
    }, [datas, containerWidth, itemsWidth])

    useDebounceEffect(
      () => {
        if (finish) {
          let width = resetWidth
          let visibleIndex = itemsWidth.length - 1
          itemsWidth.find((item, index) => {
            if (width + item > containerWidth) {
              visibleIndex = index - 1
              return true
            }
            width += item
            return false
          })
          const restItems = datas.slice(visibleIndex + 1)

          if (visibleIndexRef.current !== visibleIndex) {
            onVisibleChange?.({
              visibleIndex,
              visibleDatas: datas.slice(0, visibleIndex + 1),
              restDatas: restItems,
            })
          }

          if (restItemsLengthRef.current !== restItems.length) {
            onRestLengthChange?.({
              visibleIndex,
              visibleDatas: datas.slice(0, visibleIndex + 1),
              restDatas: restItems,
            })
          }

          setVisibleIndex(visibleIndex)
          visibleIndexRef.current = visibleIndex
          restItemsLengthRef.current = restItems.length
          setRestItems(restItems)
        }
      },
      [datas, finish, itemsWidth, containerWidth, resetWidth],
      {
        wait: 100,
      }
    )

    if (!datas?.length) return null
    if (!rowKey) return <>请指定 rowKey</>

    return (
      <OverflowContext.Provider value={{ itemsWidth, setItemsWidth }}>
        <>
          <div className={classNames(preCls, className)} style={style}>
            {datas.slice(0, visibleIndex + 1).map((item) => renderItem(item))}
            <div className={`${preCls}__reset`}>{renderRest(restItems)}</div>
          </div>
          <div
            ref={containerRef}
            className={classNames(
              preCls,
              className,
              'absolute opacity-0 pointer-events-none'
            )}
            style={style}
          >
            {datas.map((item, index) => (
              <OverflowItem key={item[rowKey]} index={index} visible>
                {renderItem(item)}
              </OverflowItem>
            ))}
            <div className={`${preCls}__reset`} ref={resetRef}>
              {renderRest(restItems)}
            </div>
          </div>
        </>
      </OverflowContext.Provider>
    )
  }
)

export default Overflow
