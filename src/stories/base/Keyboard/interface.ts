import { IButtonProps } from '../Button'

export interface IKeyboardLayout {
  /** 键盘列数 */
  columns?: number
  /** 键盘行数 */
  rows?: number
  /** 按键高度 */
  height?: number
  /** 按键宽度 */
  width?: number
}

export type IKeyboardColumn = IKeyboardKeyObj[]

export type IKeyboardKeyObj = {
  /** 按键组子元素 */
  children?: IKeyboardKeyObj[]
  /** 按键所占列数
   * @default 1
   */
  columns?: number
  /** 按键按钮 Button 组件属性 */
  containerProps?: IButtonProps
  /** 按键唯一标识 */
  key: string
  /** 按键显示名称 */
  label: string
  /** 按键所占行数
   * @default 1
   */
  rows?: number
  /** 按键类型 */
  type: IKeyboardKeyType
  /** 按键值 */
  value: string
  /** 自定义按键渲染内容 */
  render?: (data: IKeyboardKeyObj) => React.ReactElement
}

export type IKeyboardKeyType =
  | "Identifier" // 目前用于变量：目标值 | 完成值 等
  | 'Keyword' // 关键字：if | else 等
  | 'Numeric' // 数字
  | 'Punctuator' // 标点符号：{} | () | > | >= 等
  | 'Function' // 功能键：删除 | 清空 | 确认等
  // | 'Variable' // 变量

export interface IKeyItemComponentProps {
  data: IKeyboardKeyObj
  disabled?: boolean
  style?: React.CSSProperties
  onSelect: (data: IKeyboardKeyObj) => void
}
