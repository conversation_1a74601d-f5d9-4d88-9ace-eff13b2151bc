import { IKeyboardColumn, I<PERSON>eyboardLayout } from './interface'

/** 键盘默认按键 */
const Keyboard_Default_Keys: IKeyboardColumn[] = [
  [
    {
      key: '1',
      label: '1',
      value: '1',
      type: 'Numeric',
    },
    {
      key: '2',
      label: '2',
      value: '2',
      type: 'Numeric',
    },
    {
      key: '3',
      label: '3',
      value: '3',
      type: 'Numeric',
    },
    {
      key: '4',
      label: '4',
      value: '4',
      type: 'Numeric',
    },
    {
      key: '5',
      label: '5',
      value: '5',
      type: 'Numeric',
    },
    {
      key: '6',
      label: '6',
      value: '6',
      type: 'Numeric',
    },
    {
      key: '7',
      label: '7',
      value: '7',
      type: 'Numeric',
    },
    {
      key: '8',
      label: '8',
      value: '8',
      type: 'Numeric',
    },
    {
      key: '9',
      label: '9',
      value: '9',
      type: 'Numeric',
    },
    {
      key: '0',
      label: '0',
      value: '0',
      type: 'Numeric',
    },
    {
      key: '.',
      label: '.',
      value: '.',
      type: 'Punctuator',
    },
    {
      key: '()',
      label: '()',
      value: '()',
      type: 'Punctuator',
      children: [
        {
          key: '(',
          label: '(',
          value: '(',
          type: 'Punctuator',
        },
        {
          key: ')',
          label: ')',
          value: ')',
          type: 'Punctuator',
        },
      ],
    },
  ],
  [
    {
      key: '+',
      label: '+',
      value: '+',
      type: 'Punctuator',
    },
    {
      key: '-',
      label: '-',
      value: '-',
      type: 'Punctuator',
    },
    {
      key: '*',
      label: '*',
      value: '*',
      type: 'Punctuator',
    },
    {
      key: '/',
      label: '/',
      value: '/',
      type: 'Punctuator',
    },
  ],
  [
    {
      key: 'delete',
      label: 'delete',
      value: 'delete',
      type: 'Function',
      containerProps: {
        orange: true,
      },
      render: () => (
        <span className='tu-icon-cleanup-s' style={{ fontSize: 24 }} />
      ),
    },
    {
      key: 'clear',
      label: '清空',
      value: 'clear',
      type: 'Function',
      containerProps: {
        orange: true,
      },
    },
    {
      key: 'confirm',
      label: '确认',
      value: 'confirm',
      type: 'Function',
      rows: 2,
      containerProps: {
        primary: true,
      },
    },
  ],
]

/** 键盘按键默认布局 */
const Keyboard_Default__Layout: IKeyboardLayout = {
  columns: 1,
  rows: 1,
  width: 52,
  height: 42,
}

/** 键盘按键间隔 */
const KEY_GAP = 8

export { Keyboard_Default_Keys, Keyboard_Default__Layout, KEY_GAP }
