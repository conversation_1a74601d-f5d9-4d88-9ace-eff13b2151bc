.tita-ui--keyboard {
  display: inline-grid;
  gap: 12px;

  .keyboard-column {
    display: grid;
    gap: 8px;
  }

  .keyboard-key-group {
    display: flex;

    .keyboard-key {
      flex: 1;
      width: 0;
      border-radius: 0;

      &:first-child {
        border-top-left-radius: 12px;
        border-bottom-left-radius: 12px;
      }

      &:last-child {
        border-top-right-radius: 12px;
        border-bottom-right-radius: 12px;
      }
    }
  }

  .keyboard-key {
    height: 100%;
    font-size: 14px;
    line-height: 22px;
    border-radius: 12px;
    box-shadow: 1px 1px 2px 0px #e0e1e8, inset 1px 1px 1px 0px #ffffff,
      inset 0px 0px 1px 0px #9ea2bc;
    background-color: #dedfea;
    color: #4a5f78;
    transition: background 0.3s, border 0.3s, color 0.3s, padding 0.3s,
      box-shadow 0.3s;

    &:active {
      box-shadow: 1px 1px 2px 0px #e0e1e8, inset 1px 1px 1px 0px #9ea2bc,
        inset -1px 0px 1px 0px #ffffff;
      background-color: #c1c3d5;
      color: #7d8299;
    }

    &.Numeric {
      box-shadow: 1px 1px 2px 0px #e0e1e8, inset 1px 1px 1px 0px #ffffff,
        inset 0px 0px 1px 0px #9ea2bc;
      background-color: #ffffff;

      &:active {
        box-shadow: 1px 1px 2px 0px #e0e1e8, inset 1px 1px 1px 0px #9ea2bc,
          inset -1px -1px 1px 0px #ffffff;
        background-color: #c1c3d5;
      }
    }

    &.tita-ui-button--default--orange {
      box-shadow: 1px 1px 2px 0px #e0e1e8, inset 1px 1px 1px 0px #ffd081,
        inset -1px -1px 1px 0px #e0971e;
      background-color: #f4b64f !important;

      &:active {
        box-shadow: 1px 1px 2px 0px #e0e1e8, inset 1px 1px 1px 0px #e0971e,
          inset -1px -1px 1px 0px #ffffff;
        background-color: #e0971e !important;
      }
    }

    &.tita-ui-button--default--primary {
      box-shadow: 1px 1px 2px 0px #e0e1e8, inset 1px 1px 1px 0px #6ba3ff,
        inset -1px -1px 1px 0px #0f5dde;
      background-color: #2879ff;
      color: #ffffff;

      &:active {
        box-shadow: 1px 1px 2px 0px #e0e1e8, inset 1px 1px 1px 0px #0f5dde,
          inset -1px -1px 1px 0px #ffffff;
        background-color: #0f5dde;
      }
    }
  }
}
