import React, { <PERSON> } from 'react'
import Keyboard from '@/stories/base/Keyboard'
import { IKeyboardKeyObj } from '../interface'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  const handleKeySelect = (selectedKey: IKeyboardKeyObj) => {
    alert(`clicked: ${selectedKey.label}
value: ${selectedKey.value}
    `)
  }

  return <Keyboard onSelect={handleKeySelect} />
}

export default React.memo(Base)
