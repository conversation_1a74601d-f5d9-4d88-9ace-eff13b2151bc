import React, { FC } from 'react'
import { IKeyItemComponentProps, IKeyboardKeyObj } from '../../interface'
import KeyItem from '../key-item'

interface Props extends IKeyItemComponentProps {}

const preCls = 'keyboard-key-group'

const KeyGroup: FC<Props> = React.memo(
  ({ data: { children }, disabled, onSelect, style }) => {
    return (
      <section className={preCls} style={style}>
        {(children as IKeyboardKeyObj[]).map((child) => {
          return (
            <KeyItem
              data={child}
              disabled={disabled}
              key={child.key}
              onSelect={onSelect}
            />
          )
        })}
      </section>
    )
  }
)

export default KeyGroup
