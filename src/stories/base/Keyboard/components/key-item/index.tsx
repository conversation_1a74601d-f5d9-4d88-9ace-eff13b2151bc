import React, { FC } from 'react'
import classNames from 'classnames'
import Button from '@/stories/base/Button'
import { IKeyItemComponentProps } from '../../interface'

interface Props extends IKeyItemComponentProps {}

const preCls = 'keyboard-key'

const KeyItem: FC<Props> = React.memo(
  ({
    data,
    data: { containerProps, label, render, type, value },
    disabled,
    onSelect,
    style,
  }) => {
    return (
      <Button
        className={classNames(preCls, type)}
        disabled={disabled}
        disableDebounce
        onClick={() => onSelect?.(data)}
        shape='square'
        {...{
          ...containerProps,
          style: {
            ...containerProps?.style,
            ...style,
          },
        }}
      >
        {render?.(data) || label}
      </Button>
    )
  }
)

export default KeyItem
