import React, { FC } from 'react'
import {
  IKeyboardColumn,
  IKeyboardKeyObj,
  IKeyboardLayout,
} from '../../interface'
import KeysGroup from '../keys-group'
import KeyItem from '../key-item'

interface Props {
  data: IKeyboardColumn
  disabled?: boolean
  layout: IKeyboardLayout
  onSelect: (selectedKey: IKeyboardKeyObj) => void
}

const preCls = 'keyboard-column'

const KeyboardColumn: FC<Props> = React.memo(
  ({
    data,
    disabled,
    layout: { columns: layoutColumns, height, rows: layoutRows, width },
    onSelect,
  }) => {
    /** 将一级数组转换成二级数组 */
    const convertToMultiArray = (
      array: any[],
      rows: number,
      columns: number
    ) => {
      const multiArray = []

      for (let i = 0; i < rows; i++) {
        const row = array.slice(i * columns, (i + 1) * columns)
        multiArray.push(row)
      }

      return multiArray
    }

    /** 计算按键所在位置 */
    const calculateKeyPosition = (columns: number, targetIndex: number) => {
      const row = Math.floor(targetIndex / columns) + 1
      const column = (targetIndex % columns) + 1
      return { row, column }
    }

    /** 获取按键栅格布局样式 */
    const getKeyItemGridStyle = (
      columns: number,
      rows: number,
      position: {
        column: number
        row: number
      }
    ) => {
      const style: React.CSSProperties = {}
      if (columns === 1 && rows === 1) return style

      const multiKeyboardColumnDataArray = convertToMultiArray(
        data,
        layoutRows as number,
        layoutColumns as number
      )
      const currentRowData = multiKeyboardColumnDataArray[position.row - 1]

      const { column: prevColumns, row: prevRows } = currentRowData
        .slice(0, position.column - 1)
        .reduce(
          (result, curr) => ({
            column: result.column + (curr.columns || 0),
            row: result.row + (curr.rows || 0),
          }),
          { column: 0, row: 0 }
        )
      const actualColumns = prevColumns + position.column
      const actualRows = prevRows + position.row

      if (columns !== 1) {
        style['gridColumn'] = `${actualColumns} / ${
          actualColumns + (columns as number)
        }`
        style['width'] = 'unset'
      }
      if (rows !== 1) {
        style['gridRow'] = `${actualRows} / ${actualRows + (rows as number)}`
        style['height'] = 'unset'
      }
      return style
    }

    return (
      <div
        className={preCls}
        style={{
          gridTemplate: `repeat(${layoutRows}, ${height}px) / repeat(${layoutColumns}, ${width}px)`,
        }}
      >
        {data.map((item, keyIndex) => {
          const {
            children,
            columns: keyColumns = 1,
            rows: keyRows = 1,
            value,
          } = item
          const position = calculateKeyPosition(
            layoutColumns as number,
            keyIndex
          )
          const itemProps = {
            data: item,
            disabled,
            onSelect,
            style: getKeyItemGridStyle(keyColumns, keyRows, position),
          }
          let Component = KeyItem
          if (children && children.length) {
            Component = KeysGroup
          }
          return <Component key={value} {...itemProps} />
        })}
      </div>
    )
  }
)

export default KeyboardColumn
