import React, { FC, useMemo } from 'react'
import classNames from 'classnames'
import KeyboardColumn from './components/keyboard-column'
import { Keyboard_Default_Keys, Keyboard_Default__Layout } from './constant'
import { IKeyboardColumn, IKeyboardKeyObj, IKeyboardLayout } from './interface'
import './index.scss'

export interface IKeyboardProps {
  /** 自定义类名 */
  className?: string
  /** 键盘排列方式
   * @default horizontal
   */
  direction?: 'horizontal' | 'vertical'
  /** 禁用
   * @default false
   */
  disabled?: boolean
  /** 按键 IKeyboardColumn 布局 */
  layout?: IKeyboardLayout[]
  /** 按键组 */
  keys?: IKeyboardColumn[]
  /** 按键样式 */
  keyStyle?: IKeyboardLayout
  /** 按键点击事件 */
  onSelect: (selectedKey: IKeyboardKeyObj) => void
}

const preCls = 'tita-ui--keyboard'

export const Keyboard: FC<IKeyboardProps> = React.memo(
  ({
    className,
    direction = 'horizontal',
    disabled = false,
    keys,
    keyStyle,
    layout,
    onSelect,
  }) => {
    /** 获取键盘列数据 */
    const keyboardKeysColumns = useMemo(() => {
      if (keys) return keys
      return Keyboard_Default_Keys
    }, [keys])

    /** 获取键盘列布局 */
    const keyboardLayout = useMemo(() => {
      if (layout) return layout
      const keysColumnsSize = keyboardKeysColumns.length
      let layoutArray: IKeyboardLayout[] = new Array(keysColumnsSize)
      const defaultLayout = { ...Keyboard_Default__Layout, ...keyStyle }
      if (keys) {
        layoutArray.fill(defaultLayout)
      } else {
        layoutArray = [
          {
            ...defaultLayout,
            columns: 3,
            rows: 4,
          },
          {
            ...defaultLayout,
            columns: 1,
            rows: 4,
          },
          {
            ...defaultLayout,
            columns: 1,
            rows: 4,
          },
        ]
      }
      return layoutArray
    }, [keyboardKeysColumns, keyStyle, layout])

    /** 获取键盘栅格布局样式 */
    const gridTemplateStyle = useMemo(() => {
      const columnsCount = keyboardKeysColumns.length
      if (direction === 'horizontal')
        return {
          gridTemplateRows: '100%',
          gridTemplateColumns: `repeat(${columnsCount}, auto)`,
        }
      return {
        gridTemplateRows: `repeat(${columnsCount}, auto)`,
        gridTemplateColumns: '100%',
      }
    }, [direction, keyboardKeysColumns])

    return (
      <section
        className={classNames(preCls, className)}
        style={{ ...gridTemplateStyle }}
      >
        {keyboardKeysColumns.map((column, columnIndex) => {
          return (
            <KeyboardColumn
              data={column}
              disabled={disabled}
              key={columnIndex}
              layout={{
                ...Keyboard_Default__Layout,
                ...keyStyle,
                ...keyboardLayout[columnIndex],
              }}
              onSelect={onSelect}
            />
          )
        })}
      </section>
    )
  }
)

export default Keyboard
