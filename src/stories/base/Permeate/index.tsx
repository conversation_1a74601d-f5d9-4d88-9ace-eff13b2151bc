import React, { FC } from 'react'

export interface IPermeateProps {
  children?: React.ReactNode | React.ReactNode[]
  /**
   * 需要透传的子元素索引
   */
  idxs?: number[]
  className?: string
  style?: React.CSSProperties
}

export const Permeate: FC<IPermeateProps> = React.memo(({ children: propsChildren, idxs, ...props }) => {
  const children = (Array.isArray(propsChildren) ? propsChildren : [propsChildren]).filter(Boolean)
  const renderChilds = () => {
    const childs = React.Children.map((children as React.DetailedReactHTMLElement<any, HTMLElement>[]), (child: React.DetailedReactHTMLElement<any, HTMLElement>, i: number) => {
      if (!child) return null

      if (idxs) {
        if (!idxs.includes(i)) {
          return React.cloneElement(child, {
            ...child.props,
            ...props
          })
        }
        return child
      }

      return React.cloneElement(child, {
        ...child.props,
        ...props
      })
    })

    return childs
  }
  return <>{renderChilds()}</>
})

export default Permeate
