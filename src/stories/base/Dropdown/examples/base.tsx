import React, { FC } from 'react'
import Dropdown from '@/stories/base/Dropdown'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  return (
    <div className="w-[300px]">
      <Dropdown
        multiple
        placeholder="默认样式"
        enableSearch
        items={new Array(100)
          .fill(0)
          .map((_, i) => ({
            label: `Item ${i}`,
            value: i,
            icon: 'tu-icon-upload',
            disabled: i == 5,
          }))}
      />
      <Dropdown
        multiple
        placeholder="对勾模式"
        checkableMode="tick"
        maxCount={1}
        items={new Array(100)
          .fill(0)
          .map((_, i) => ({
            label: `Item ${i}`,
            value: i,
            icon: 'tu-icon-upload',
            disabled: i == 5,
          }))}
      />
      <Dropdown
        multiple
        placeholder="tags 模式"
        checkableMode="tick"
        previewMode="tag"
        items={new Array(100)
          .fill(0)
          .map((_, i) => ({
            label: `Item ${i}`,
            value: i,
            icon: 'tu-icon-upload',
            disabled: i == 5,
          }))}
      />
    </div>
  )
}

export default React.memo(Base)
