import React, { <PERSON> } from 'react'
import Dropdown from '@/stories/base/Dropdown'
import Button from '../../Button'

export interface IFooterProps {}

const Footer: FC<IFooterProps> = ({}) => {
  return (
    <div className='w-[300px]'>
      <Dropdown
        placeholder='默认样式'
        items={new Array(100).fill(0).map((_, i) => ({
          label: `Item ${i}`,
          value: i,
          disabled: i == 5,
        }))}
        contentStyle={{
          paddingBottom: 0,
        }}
        footer={
          <div
            className='flex items-center justify-center'
            style={{
              height: 46,
            }}
          >
            <Button icon='add1' type='link' primary className='w-full'>
              添加状态
            </Button>
          </div>
        }
      />
    </div>
  )
}

export default React.memo(Footer)
