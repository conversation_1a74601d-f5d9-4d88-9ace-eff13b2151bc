import React, { FC } from 'react'
import Dropdown from '@/stories/base/Dropdown'
import styled from 'styled-components'

export interface ICustomLabelProps {}

export const Icons = [
  'interview-m',
  'UV-m',
  'manageevaluate-m',
  'total',
  'pinglun-m',
  'OKR-m',
  'deferred-m',
  'finish-m',
  'redenvelopes',
  'kaohe-m',
  'project-m',
  'renwu-m',
  'warninglight-m',
  'dianzan',
  'PV-m',
  'staff-m',
  'pc-organization-d',
  'yanqi-s',
  'dashedevaluate-m',
  'dashedreply-m',
]

const IconItemStyle = styled.i`
  width: 16px;
  height: 16px;
  font-size: 16px;
`
const IconsSelectOptions = Icons.map((name) => ({
  label: <IconItemStyle className={`tu-icon-${name}`} key={name} />,
  value: name,
}))

const CustomLabel: FC<ICustomLabelProps> = ({}) => {
  return (
    <div className='w-[300px]'>
      <Dropdown
        multiple
        placeholder='默认样式'
        popupPlacement='bottomLeft'
        stretch={undefined}
        customRenderLabel={({ item, onSelect, active }) => {
          return (
            <LabelStyle active={active} onClick={onSelect}>
              {item.label}
            </LabelStyle>
          )
        }}
        popupInnerStyle={{
          display: 'flex',
          flexWrap: 'wrap',
          padding: '0 6px 0 10px',
          width: 208,
        }}
        items={IconsSelectOptions}
      />
    </div>
  )
}

const LabelStyle = styled.div<{ active: boolean }>`
  margin: 6px 0;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  margin-right: 4px;
  cursor: pointer;
  font-size: 16px;
  box-sizing: border-box;
  color: #3f4755;

  &:hover {
    background-color: #f0f4fa;
  }
  ${({ active }) => active && `background-color: #f0f4fa;`};
`

export default React.memo(CustomLabel)
