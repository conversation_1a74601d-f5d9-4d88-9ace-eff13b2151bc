import React, {
  FC,
  useMemo,
  useState,
  useEffect,
  useRef,
  MutableRefObject,
  forwardRef,
  useImperativeHandle,
} from 'react'
import classNames from 'classnames'
import './index.scss'
import Popup, { IPopupProps, IPopupRef } from '../Popup'
import SelectView, { ISelectViewProps } from '../SelectView'
import styled from 'styled-components'
import TitaScroll from '../TitaScroll'
import Search from '../Search'
import { handleSearch } from '@/utils/string'
import { useUpdateEffect } from 'ahooks'
import CheckBox from '../CheckBox'
import TagsView from '../TagsView'
import Ellipsis from '../Ellipsis'
import { findDefaultValue } from '@/utils/common'

export type DropdownItem = {
  label?: string | React.ReactNode
  value?: string | number
  icon?: string | React.ReactNode
  disabled?: boolean
  hidden?: boolean
  type?: 'divider'
  children?: DropdownItem[]
}

export type DropdownValue = string | number | (string | number)[]

type DropdownItemValue = DropdownItem | DropdownItem[]

export interface IDropdownProps
  extends Omit<IPopupProps, 'popup'>,
    Omit<ISelectViewProps, 'children' | 'onChange' | 'minWidth' | 'value'> {
  value?: DropdownValue
  initialValue?: DropdownValue
  onChange?: (value: DropdownValue, item: DropdownItemValue) => void
  onChangeBefore?: (
    value: DropdownValue,
    event: {
      key: string | number
      checked: boolean
    },
    item: DropdownItemValue
  ) => DropdownValue
  enableActiveStyle?: boolean
  items?: DropdownItem[]
  deps?: any[]
  multiple?: boolean
  /**
   * 定义选中项的样式，多选模式生效
   */
  checkableMode?: 'checkbox' | 'tick'
  /**
   * 定义选中项的预览样式，多选模式生效
   */
  previewMode?: 'text' | 'tag'
  children?:
    | React.ReactNode
    | ((info: {
        value?: DropdownValue
        item?: DropdownItem
        previewLabel?: string
      }) => React.ReactNode)
  footer?: React.ReactNode
  customRenderLabel?: (info: {
    item: DropdownItem
    active: boolean
    labelNode: React.ReactNode
    onSelect: () => void
    searchValue?: string
  }) => React.ReactNode
  selectViewRender?: (info: {
    value?: DropdownValue
    item?: DropdownItem
    previewLabel?: string
  }) => React.ReactNode
  itemClassName?: string
  width?: number | string
  maxWidth?: number | string
  minWidth?: number | string
  height?: number | string
  maxHeight?: number | string
  enableSearch?: boolean
  customSplit?: React.ReactNode
  contentStyle?: React.CSSProperties
  /** 最大可选数量 */
  maxCount?: number
  /**
   * 禁用自动关闭
   */
  disableAutoHide?: boolean
  className?: string
  popupStyle?: React.CSSProperties
  popupInnerStyle?: React.CSSProperties
  style?: React.CSSProperties
  // 子层级弹层位置
  subPopupPlacement?: IPopupProps['popupPlacement']
  subPopupAlign?: IPopupProps['popupAlign']
  emptyContent?: string
}

const preCls = 'tita-ui-dropdown'

export const Dropdown = React.memo(
  forwardRef<IPopupRef, IDropdownProps>(
    (
      {
        initialValue,
        value: propsValue,
        onChange: propOnChange,
        onChangeBefore,
        items,
        multiple,
        checkableMode = 'checkbox',
        previewMode = 'text',
        customRenderLabel,
        selectViewRender,
        itemClassName,
        className,
        popupStyle,
        popupInnerStyle,
        disableAutoHide,
        enableSearch,
        children,
        width,
        maxWidth,
        minWidth = 118,
        height,
        maxHeight = 272,
        customSplit = '、',
        popupPlacement = 'bottom',
        subPopupPlacement = 'left',
        enableActiveStyle = true,
        footer,
        contentStyle,
        maxCount,
        emptyContent,
        ...other
      },
      ref
    ) => {
      const [searchValue, setSearchValue] = useState<string>()
      const [value, setValue] = useState<DropdownValue | undefined>(
        findDefaultValue([propsValue, initialValue, multiple ? [] : undefined])
      )
      const [options, setOptions] = useState<DropdownItemValue>()

      const setSelectValue = (
        value: DropdownValue,
        item: DropdownItemValue
      ) => {
        setValue(value)
        setOptions(item)
      }

      const onChange = (
        value: DropdownValue,
        event: {
          key: string | number
          checked: boolean
        },
        item: DropdownItemValue
      ) => {
        const newValue = onChangeBefore?.(value, event, item) ?? value
        setSelectValue(newValue, item)
        propOnChange?.(newValue, item)
      }

      useUpdateEffect(() => {
        setValue(propsValue)
      }, [propsValue])

      const popupRef = useRef<IPopupRef>() as MutableRefObject<
        IPopupRef | undefined
      >

      useImperativeHandle(ref, () => ({
        open: () => {
          popupRef.current?.open()
        },
        close: () => {
          popupRef.current?.close()
        },
        getVisible: () => {
          if (!popupRef.current) return false
          return popupRef.current.getVisible()
        },
      }))

      const onChangeChecked = (
        checked: boolean,
        key: string | number,
        disabled: boolean,
        item: DropdownItemValue
      ) => {
        if (disabled) {
          return
        }
        let newValue = []
        let newOptions = []
        if (!multiple) {
          newValue.push(key)
          newOptions.push(item)
        } else {
          if (checked) {
            // @ts-ignore
            newValue = [...value, key]
            newOptions = [...((options as DropdownItemValue[]) ?? []), item]
          } else {
            // @ts-ignore
            newValue = value.filter((item) => item !== key)
            newOptions = ((options as DropdownItem[]) ?? []).filter(
              (item: DropdownItem) => item?.value !== key
            )
          }
          if (maxCount) {
            newValue = newValue.slice(-maxCount)
            newOptions = newOptions.slice(-maxCount)
          }
        }
        onChange?.(newValue, { key, checked }, newOptions as DropdownItemValue)
      }

      const handleSubChange = (
        value: DropdownValue,
        item: DropdownItemValue
      ) => {
        propOnChange?.(value, item)
        //选中某个值关闭整个弹层
        setTimeout(() => {
          popupRef.current?.close()
        }, 0)
      }

      const popup = useMemo(() => {
        return (
          <div className={`${preCls}__content`} style={contentStyle}>
            {enableSearch && (
              <div className='px-12px box-border mb-10px' style={{ maxWidth }}>
                <Search disableSearchResult onSearch={setSearchValue} />
              </div>
            )}
            {items?.length === 0 ||
            items?.filter((item) => !item.hidden)?.length === 0 ? (
              <div className='flex flex-col items-center justify-center h-full'>
                <div
                  className='text-gray-400'
                  style={{
                    width,
                    maxWidth,
                    padding: '0 20px',
                    textAlign: 'center',
                    boxSizing: 'border-box',
                  }}
                >
                  {emptyContent || '暂无数据'}
                </div>
              </div>
            ) : (
              <TitaScroll
                height={height}
                maxHeight={maxHeight}
                innerStyle={{
                  width: '100%',
                  padding: '0 13px',
                  ...popupInnerStyle,
                }}
                colBarInset
                barWidth={5}
                shadowTheme='white'
                style={{
                  minWidth,
                  boxSizing: 'border-box',
                  width,
                  maxWidth,
                  overflow: 'hidden',
                }}
              >
                {items
                  ?.filter((item) => {
                    if (item.hidden) return false
                    if (searchValue && typeof item.label === 'string') {
                      return item.label.includes(searchValue)
                    }
                    return true
                  })
                  ?.map((item, index) => {
                    const active = multiple
                      ? // @ts-ignore
                        value?.includes(item.value)
                      : value === item.value
                    const onSelect = () => {
                      if (item.disabled) {
                        return
                      }
                      if (multiple) {
                        onChangeChecked(
                          !active,
                          item.value as string | number,
                          !!item.disabled,
                          item
                        )
                        return
                      }
                      if (propsValue === undefined) {
                        setSelectValue(item.value as DropdownValue, item)
                      }
                      onChange?.(
                        item.value as string | number,
                        {
                          key: item.value as string | number,
                          checked: true,
                        },
                        item
                      )
                      if (!disableAutoHide && !multiple)
                        popupRef.current?.close()
                    }
                    const iconNode =
                      item.icon &&
                      (typeof item.icon === 'string' ? (
                        <IconStyle className={item.icon} />
                      ) : (
                        <div className='mr-6px'>{item.icon}</div>
                      ))
                    const labelNode =
                      item.type === 'divider' ? (
                        <DrapdownDivider key={index} />
                      ) : item.children && item.children.length > 0 ? (
                        <Dropdown
                          popupPlacement={subPopupPlacement}
                          key={item.value}
                          items={item.children}
                          onChange={handleSubChange}
                          action={['hover']}
                          enableActiveStyle={enableActiveStyle}
                          popupAlign={{
                            offset: [
                              -18,
                              16 +
                                item.children.filter((item) => !item.type)
                                  .length *
                                  17 +
                                item.children.filter(
                                  (item) => item.type === 'divider'
                                ).length *
                                  3 -
                                17,
                            ],
                          }}
                        >
                          <DrapdownItemStyle
                            active={active}
                            className={classNames(`${preCls}__item`, {
                              active,
                            })}
                            enableActiveStyle={enableActiveStyle}
                            key={item.value}
                            disabled={item.disabled}
                          >
                            <div
                              className={classNames(
                                'flex items-center',
                                itemClassName
                              )}
                            >
                              {multiple && checkableMode === 'checkbox' && (
                                <CheckBox
                                  checked={active}
                                  disabled={item.disabled}
                                  className='mr-10px'
                                />
                              )}
                              {iconNode}
                              {searchValue && typeof item.label === 'string' ? (
                                <p
                                  dangerouslySetInnerHTML={{
                                    __html: handleSearch(
                                      searchValue,
                                      item.label,
                                      'color: #2879ff;'
                                    ),
                                  }}
                                />
                              ) : (
                                item.label
                              )}
                            </div>

                            {multiple && active && checkableMode === 'tick' && (
                              <i className='tu-icon-H5-finished-s ml-10px text-primary text-xs' />
                            )}
                          </DrapdownItemStyle>
                        </Dropdown>
                      ) : (
                        <DrapdownItemStyle
                          onClick={onSelect}
                          active={active}
                          className={classNames(`${preCls}__item`, { active })}
                          key={item.value}
                          enableActiveStyle={enableActiveStyle}
                          disabled={item.disabled}
                        >
                          <div
                            className={classNames(
                              'flex items-center',
                              itemClassName
                            )}
                          >
                            {multiple && checkableMode === 'checkbox' && (
                              <CheckBox
                                checked={active}
                                disabled={item.disabled}
                                className='mr-10px'
                              />
                            )}
                            {iconNode}
                            {searchValue && typeof item.label === 'string' ? (
                              <p
                                dangerouslySetInnerHTML={{
                                  __html: handleSearch(
                                    searchValue,
                                    item.label,
                                    'color: #2879ff;'
                                  ),
                                }}
                              />
                            ) : (
                              item.label
                            )}
                          </div>

                          {multiple && active && checkableMode === 'tick' && (
                            <i className='tu-icon-H5-finished-s ml-10px text-primary text-xs' />
                          )}
                        </DrapdownItemStyle>
                      )
                    return customRenderLabel
                      ? customRenderLabel({
                          item,
                          active,
                          labelNode,
                          searchValue,
                          onSelect,
                        })
                      : labelNode
                  })}
              </TitaScroll>
            )}
            {footer && (
              <div className={`${preCls}__footer flex-shrink-0`}>{footer}</div>
            )}
          </div>
        )
      }, [items, value, propsValue, disableAutoHide, onChange, searchValue])

      const itemsDic = useMemo(() => {
        return (
          items?.reduce((acc, item) => {
            acc[item.value as string | number] = item
            return acc
          }, {} as Record<string, DropdownItem>) || {}
        )
      }, [items])

      const valueItem = useMemo(() => {
        return multiple
          ? value
              // @ts-ignore
              ?.map((item) => itemsDic[item])
              // @ts-ignore
              .filter((item) => item !== undefined)
          : // @ts-ignore
            itemsDic[value]
      }, [value, itemsDic])

      const previewLabel = useMemo(() => {
        if (!valueItem) return null
        if (Array.isArray(valueItem) && !valueItem.length) return null

        const label = multiple
          ? // @ts-ignore
            valueItem?.map((item, i) => (
              <span key={item.value}>
                {!!i && customSplit}
                {item.label}
              </span>
            ))
          : valueItem?.label

        return valueItem && <Ellipsis>{label}</Ellipsis>
      }, [valueItem])

      const onDelete = (deleteValue: string | number) => {
        if (multiple) {
          // @ts-ignore
          onChange(
            (value as (string | number)[])?.filter(
              (item) => item !== deleteValue
            ) || [],
            {
              key: deleteValue,
              checked: false,
            },
            ((options as DropdownItem[]) ?? [])?.filter(
              (item) => item.value !== deleteValue
            ) || []
          )
        }
      }

      return (
        <Popup
          // @ts-ignore
          ref={popupRef}
          className={classNames(preCls, className)}
          clearPadding
          stretch='minWidth'
          popupPlacement={popupPlacement}
          {...other}
          style={popupStyle}
          popup={popup}
        >
          {typeof children === 'function'
            ? children({ value, item: valueItem, previewLabel })
            : children ||
              (multiple && previewMode === 'tag' ? (
                <TagsView {...other} tags={valueItem} onDelete={onDelete} />
              ) : (
                <SelectView {...other}>
                  {selectViewRender
                    ? selectViewRender({ value, item: valueItem, previewLabel })
                    : previewLabel}
                </SelectView>
              ))}
        </Popup>
      )
    }
  )
)

const DrapdownItemStyle = styled.div<{
  active?: boolean
  disabled?: boolean
  enableActiveStyle: boolean
}>`
  padding: 6px;
  font-size: 14px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  color: #3f4755;
  transition: background-color 0.3s, color 0.3s;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  ${({ disabled }) =>
    disabled &&
    `
    opacity: 0.5;
    cursor: not-allowed;
  `}

  ${({ active, enableActiveStyle }) =>
    active &&
    enableActiveStyle &&
    `
    color: #2879ff;
  `}

  &:hover {
    background: #f0f2f5;
  }
`

const IconStyle = styled.i`
  width: 16px;
  height: 16px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
`

const DrapdownDivider = styled.div`
  width: 100%;
  height: 1px;
  border-top: 1px solid #e9ecf0;
  margin: 6px 0;
`

export default Dropdown
