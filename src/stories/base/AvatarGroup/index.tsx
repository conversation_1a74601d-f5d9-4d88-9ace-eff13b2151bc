import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'
import styled from 'styled-components'

export interface IAvatarGroupProps {
  offset?: number
  max?: number
  renderMore?: (more: number) => React.ReactNode
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-avatar-group'

export const AvatarGroup: FC<IAvatarGroupProps> = React.memo(
  ({ children, max, renderMore, offset = 10, className, style }) => {
    const childrens = React.Children.toArray(children)
    let more = 0
    if (max && childrens.length > max) {
      more = childrens.length - max
      children = childrens.slice(0, max)
    }
    return (
      <AvatarGroupStyle
        offset={offset}
        className={classNames(preCls, className)}
        style={style}
      >
        {children}
        {more > 0 && renderMore?.(more) || null}
      </AvatarGroupStyle>
    )
  }
)

const AvatarGroupStyle = styled.div<{ offset: number }>`
  > * + * {
    margin-left: ${(props) => -props.offset}px !important;
  }
`

export default AvatarGroup
