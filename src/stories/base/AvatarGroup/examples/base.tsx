import React, { <PERSON> } from 'react'
import AvatarGroup from '@/stories/base/AvatarGroup'
import Avatar from '../../Avatar'

export interface IBaseProps {}

const avatarUrl: string =
  '//file.tita.work/TitaImage/500040/fe08e1d3807747dca4e8bd6503516655_m.jpg'

const Base: FC<IBaseProps> = ({}) => {
  return (
    <AvatarGroup max={3} renderMore={(more) => <Avatar name={more} />}>
      <Avatar src={avatarUrl} />
      <Avatar src={avatarUrl} />
      <Avatar src={avatarUrl} />
      <Avatar src={avatarUrl} />
      <Avatar src={avatarUrl} />
      <Avatar src={avatarUrl} />
    </AvatarGroup>
  )
}

export default React.memo(Base)
