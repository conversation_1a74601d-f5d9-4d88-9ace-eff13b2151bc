import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'
import DefaultRender, { IDefaultRenderProps } from './renders/DefaultRender'
import CoverRender, { ICoverRenderProps } from './renders/CoverRender'
import MiniRender, { IMiniRenderProps } from './renders/MiniRender'
import { Attachment } from '../../business/CommentList/types'
import { UploadFile } from '../../business/Upload/types'
import AIRender, { IAIRenderProps } from './renders/AIRender'

export interface IFileCardProps {
  /**
   * 文件名
   * @default demo.png
   */
  name: string
  /**
   * 封面
   * @default https://xfile6.tita.com/ux/tita-home-page/public/file-type-mind.svg
   */
  cover: React.ReactNode
  /**
   * 主题
   * @default default
   */
  theme?: 'cover' | 'default' | 'mini' | 'ai'
  /**
   * 文件大小
   * @default 100KB
   */
  size?: string
  /**
   * 上传进度
   */
  progress?: number
  /**
   * 是否正在上传
   */
  uploading?: boolean
  /**
   * 是否异常
   */
  error?: boolean
  /**
   * 异常提示
   */
  errorMessage?: string
  /**
   * 是否显示取消按钮
   */
  showCancel?: boolean
  /**
   * 是否显示删除按钮
   */
  showDelete?: boolean
  /**
   * 是否显示重新上传按钮
   */
  showReupload?: boolean
  /**
   * 是否显示预览按钮
   */
  showPreview?: boolean
  /**
   * 是否显示下载按钮
   */
  showDownload?: boolean
  onCancel?: () => void
  onDelete?: () => void
  onReupload?: () => void
  onPreview?: () => void
  onDownload?: () => void
  renderProps?: ICoverRenderProps | IDefaultRenderProps | IMiniRenderProps | IAIRenderProps
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  data: UploadFile | Attachment
}

const preCls = 'tita-ui-file-card'

export const FileCard: FC<IFileCardProps> = ({
  theme = 'default',
  className,
  style,
  renderProps,
  data,
  ...other
}) => {
  const Render = {
    default: DefaultRender,
    cover: CoverRender,
    mini: MiniRender,
    ai: AIRender,
  }[theme]

  return (
    <Render
      className={classNames(preCls, className)}
      style={style}
      data={data}
      {...other}
      {...renderProps}
    />
  )
}

export default React.memo(FileCard)
