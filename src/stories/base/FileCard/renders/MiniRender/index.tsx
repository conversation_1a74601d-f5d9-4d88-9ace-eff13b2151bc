import Tooltip from '@/stories/base/Tooltip/tooltip'
import { getLocale } from '@tita/utils'
import classNames from 'classnames'
import React, { FC } from 'react'
import { IFileCardProps } from '../..'
import './index.scss'
import Ellipsis from '@/stories/base/Ellipsis'
import Popup from '@/stories/base/Popup'
import Button from '@/stories/base/Button'
import OpenFileName from '@/stories/business/OpenFileName'

export interface IMiniRenderProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-mini-render'

export const MiniRender: FC<IMiniRenderProps & IFileCardProps> = React.memo(
  ({
    name,
    size,
    progress,
    uploading,
    cover,
    error,
    showCancel,
    showDelete,
    showDownload,
    showPreview,
    showReupload,
    onCancel,
    onDelete,
    onDownload,
    onPreview,
    onReupload,
    className,
    data,
    style,
  }) => {
    const showProgress = (progress && progress < 100) || uploading
    return (
      <div className={classNames(preCls, className)} style={style}>
        {!showProgress ? (
          <i
            className='tu-icon-guanlian'
            style={{
              color: error ? '#F05E5E' : '#2879FF',
            }}
          />
        ) : (
          <Loading />
        )}
        {!showProgress ? (
          <Popup
            action='hover'
            popupPlacement='bottomLeft'
            style={{
              padding: '10px 8px 10px 12px',
              borderRadius: '4px',
            }}
            popup={
              <div
                className={`${preCls}__popup flex justify-between items-center`}
              >
                <Ellipsis className='mr-8px text-#3f4755 text-[14px]'>
                  {' '}
                  {data?.documentSource === 8 || data?.DocumentSource === 8 ? (
                    <OpenFileName
                      openId={
                        data?.ClientUrl ||
                        data?.dfsUrl ||
                        data?.DfsUrl ||
                        data?.dfsPath
                      }
                    ></OpenFileName>
                  ) : (
                    name
                  )}
                </Ellipsis>
                <div className='flex space-x-8px'>
                  {showCancel && (
                    <Tooltip
                      overlay={getLocale('Rep_NewS_CanUpload') || '取消上传'}
                    >
                      <Button
                        icon='canceled'
                        type='text'
                        className='hover:text-primary'
                        onClick={onCancel}
                        size='least'
                        shape='square'
                      />
                    </Tooltip>
                  )}
                  {showDownload && (
                    <Tooltip overlay={getLocale('Mod_Download') || '下载'}>
                      <Button
                        icon='APP-xiazai'
                        type='text'
                        className='hover:text-primary'
                        onClick={onDownload}
                        size='least'
                        shape='square'
                      />
                    </Tooltip>
                  )}
                  {showPreview && (
                    <Tooltip overlay={getLocale('Mod_Preview') || '预览'}>
                      <Button
                        icon='yulan'
                        type='text'
                        className='hover:text-primary'
                        onClick={onPreview}
                        size='least'
                        shape='square'
                      />
                    </Tooltip>
                  )}
                  {showDelete && (
                    <Tooltip overlay={getLocale('Mod_Delete') || '删除'}>
                      <Button
                        icon='del'
                        type='text'
                        className='hover:text-primary'
                        onClick={onDelete}
                        size='least'
                        shape='square'
                      />
                    </Tooltip>
                  )}
                </div>
              </div>
            }
          >
            <div className='flex'>
              <Ellipsis className={`${preCls}__title`}>
                <p
                  onClick={onPreview}
                  style={{
                    cursor: showPreview ? 'pointer' : 'default',
                    color: error ? '#F05E5E' : '#2879FF',
                  }}
                >
                  {data?.documentSource === 8 || data?.DocumentSource === 8 ? (
                    <OpenFileName
                      openId={
                        data?.ClientUrl ||
                        data?.dfsUrl ||
                        data?.DfsUrl ||
                        data?.dfsPath
                      }
                    ></OpenFileName>
                  ) : (
                    name
                  )}
                </p>
              </Ellipsis>
            </div>
          </Popup>
        ) : (
          <Ellipsis className={`${preCls}__title`}>
            {' '}
            {data?.documentSource === 8 || data?.DocumentSource === 8 ? (
              <OpenFileName
                openId={
                  data?.ClientUrl || data?.dfsUrl || data?.DfsUrl || data?.dfsPath
                }
              ></OpenFileName>
            ) : (
              name
            )}
          </Ellipsis>
        )}
      </div>
    )
  }
)

function Loading() {
  return (
    <span className={classNames(preCls, 'animate-spin')}>
      <svg
        viewBox='0 0 1024 1024'
        focusable='false'
        data-icon='loading'
        width='1em'
        height='1em'
        fill='currentColor'
        aria-hidden='true'
      >
        <path d='M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z'></path>
      </svg>
    </span>
  )
}

export default MiniRender
