import Tooltip from '@/stories/base/Tooltip/tooltip'
import { getLocale } from '@tita/utils'
import classNames from 'classnames'
import React, { FC } from 'react'
import { IFileCardProps } from '../..'
import './index.scss'
import Ellipsis from '@/stories/base/Ellipsis'
import { isMobile } from '@/utils/platform'
import Button from '@/stories/base/Button'
import OpenFileName from '@/stories/business/OpenFileName'

export interface IDefaultRenderProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-default-render'

export const DefaultRender: FC<IDefaultRenderProps & IFileCardProps> = React.memo(
  ({
    name,
    size,
    progress,
    uploading,
    cover,
    error,
    showCancel,
    showDelete,
    showDownload,
    showPreview,
    showReupload,
    onCancel,
    onDelete,
    onDownload,
    onPreview,
    onReupload,
    className,
    data,
    style,
  }) => {
    const showProgress = progress && progress < 100
    const getSourceLabel = (source: number) => {
      switch (source) {
        case 6:
          return '来自钉盘'
        case 7:
          return '来自飞书文档'
        case 8:
          return '来自微盘'
        default:
          return ''
      }
    }
    return (
      <div
        className={classNames(preCls, className, {
          [`${preCls}--isMobile`]: isMobile(),
        })}
        style={style}
        onClick={(isMobile() && onPreview) || undefined}
      >
        <div className={`${preCls}__icon`}>
          {cover &&
            (typeof cover === 'string' ? (
              <img className={`${preCls}__icon-img`} src={cover} />
            ) : (
              cover
            ))}

          {uploading && (
            <div className={`${preCls}__icon-uploading`}>
              <div className={`${preCls}__icon-uploading-inner`}></div>
            </div>
          )}
        </div>
        <div className={`${preCls}__content`}>
          <Ellipsis className={`${preCls}__content__title`}>
            {data?.documentSource === 8 || data?.DocumentSource === 8 ? (
              <OpenFileName
                openId={data?.ClientUrl || data?.dfsUrl || data?.DfsUrl || data?.dfsPath}
              ></OpenFileName>
            ) : (
              name
            )}
          </Ellipsis>
          {showProgress && (
            <div className={`${preCls}__content__progress`}>
              <div
                className={`${preCls}__content__progress-bar`}
                style={{ width: `${progress}%` }}
              />
            </div>
          )}
          <div className={`${preCls}__content__subinfo-wrapper`}>
            {!showProgress &&
              (data?.documentSource === 8 ||
                data?.DocumentSource === 8 ||
                data?.documentSource === 7 ||
                data?.DocumentSource === 7 ||
                data?.documentSource === 6 ||
                data?.DocumentSource === 6) && (
                <p className={classNames(`${preCls}__content__source`, {})}>
                  {getSourceLabel(data?.documentSource || data?.DocumentSource)}
                </p>
              )}
            {!showProgress && size && (
              <p
                className={classNames(`${preCls}__content__size`, {
                  [`${preCls}__content__size--error`]: error,
                })}
              >
                {size}
              </p>
            )}
          </div>
        </div>
        <div className={`${preCls}__action`}>
          {showCancel && !isMobile() && (
            <Tooltip overlay={getLocale('Rep_NewS_CanUpload') || '取消上传'}>
              <span
                className={`${preCls}__action-btn`}
                onClick={(e) => {
                  e.stopPropagation()
                  onCancel?.()
                }}
              >
                <span className='tu-icon-canceled action-icon action-icon-delete' />
              </span>
            </Tooltip>
          )}
          {showCancel && isMobile() && (
            <Tooltip overlay={getLocale('Rep_NewS_CanUpload') || '取消上传'}>
              <Button
                icon='canceled'
                size='small'
                iconStyle={{
                  fontSize: 16,
                }}
                onClick={(e) => {
                  e.stopPropagation()
                  onCancel?.()
                }}
              />
            </Tooltip>
          )}
          {showPreview && !isMobile() && (
            <Tooltip overlay={getLocale('Mod_Preview') || '预览'}>
              <span
                className={`${preCls}__action-btn`}
                onClick={(e) => {
                  e.stopPropagation()
                  onPreview?.()
                }}
              >
                <span className='tu-icon-yulan action-icon' />
              </span>
            </Tooltip>
          )}
          {showDownload && !isMobile() && (
            <Tooltip overlay={getLocale('Mod_Download') || '下载'}>
              <span
                className={`${preCls}__action-btn`}
                onClick={(e) => {
                  e.stopPropagation()
                  onDownload?.()
                }}
              >
                <span className='tu-icon-APP-xiazai action-icon xiazai' />
              </span>
            </Tooltip>
          )}
          {showDownload && isMobile() && (
            <Tooltip overlay={getLocale('Mod_Download') || '下载'}>
              <Button
                icon='APP-xiazai'
                size='small'
                iconStyle={{
                  fontSize: 16,
                }}
                onClick={(e) => {
                  e.stopPropagation()
                  onDownload?.()
                }}
              />
            </Tooltip>
          )}
          {showDelete && !isMobile() && (
            <Tooltip overlay={getLocale('Mod_Delete') || '删除'}>
              <span
                className={`${preCls}__action-btn`}
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete?.()
                }}
              >
                <span className='tu-icon-del action-icon delete' />
              </span>
            </Tooltip>
          )}
          {showDelete && isMobile() && (
            <Tooltip overlay={getLocale('Mod_Delete') || '删除'}>
              <Button
                icon='H5-close-s'
                size='small'
                iconStyle={{
                  fontSize: 16,
                }}
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete?.()
                }}
              />
            </Tooltip>
          )}
        </div>
      </div>
    )
  }
)

export default DefaultRender
