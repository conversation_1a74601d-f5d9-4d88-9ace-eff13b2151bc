.tita-ui-default-render {
  width: auto;
  display: inline-flex;
  height: 60px;
  padding: 10px;
  box-sizing: border-box;
  font-size: 12px;
  background-color: #f7f8fa;
  align-items: center;
  border-radius: 8px;

  &--isMobile {
    background-color: rgba(240, 244, 250, 0.5);
    padding: 10px 12px 10px 15px;
  }

  &__icon {
    width: 36px;
    height: 36px;
    line-height: 36px;
    margin-right: 8px;
    text-align: center;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    flex-shrink: 0;

    &-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &-uploading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(20, 28, 40, 0.2);
    }
    &-uploading-inner {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      background-image: url(http://xfile6.tita.com/ux/tita-home-page/public/ui-loading.svg);
      background-size: 100% 100%;
      animation: titaUiUploading 1s linear infinite;
    }
  }

  &__content {
    width: 100%;
    font-size: 14px;
    line-height: 22px;
    color: #3f4755;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: keep-all;
    display: flex;
    flex-direction: column;
    
    &__title {
      margin-bottom: 0;
      text-align: left;
    }

    &__subinfo-wrapper{
      display: flex;
      align-items: center;
    }

    &__source{
      font-size: 12px;
      color: #89919f;
      line-height: 18px;
      margin-bottom: 0;
      text-align: left;
      margin-right: 4px;
    }

    &__size {
      font-size: 12px;
      color: #89919f;
      line-height: 18px;
      margin-bottom: 0;
      text-align: left;

      &--error {
        color: #f05e5e;
      }
    }

    &__progress {
      margin-top: 8px;
      width: 100%;
      height: 4px;
      border-radius: 2px;
      background: #e9ecf0;
      overflow: hidden;

      &-bar {
        background: #2879ff;
        height: 100%;
        transition: width 0.3s;
      }
    }
  }

  &__action {
    margin-left: 2px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    > * {
      margin-left: 10px;
    }

    &-btn {
      font-size: 14px;
      color: rgb(169, 184, 191);
      vertical-align: middle;
      cursor: pointer;
      align-items: center;
      border: 1px solid #dfe3ea;
      border-radius: 50%;
      box-sizing: border-box;
      display: flex;
      height: 20px;
      justify-content: center;
      margin-left: 10px;
      width: 20px;

      &:hover {
        .action-icon {
          color: #2879ff;
        }
      }
    }
  }
}
