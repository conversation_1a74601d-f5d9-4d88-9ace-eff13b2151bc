import Tooltip from '@/stories/base/Tooltip/tooltip'
import { getLocale } from '@tita/utils'
import classNames from 'classnames'
import React, { FC, useRef, RefObject, useState } from 'react'
import { IFileCardProps } from '../..'
import './index.scss'
import Ellipsis from '@/stories/base/Ellipsis'
import OpenFileName from '@/stories/business/OpenFileName'
import { download, previewFile } from '@/stories/business/Upload/utils'
import ImagePreview, { IImagePreviewRef } from '@/stories/base/ImagePreview'

export interface IAIRenderProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-ai-render'

export const AIRender: FC<IAIRenderProps & IFileCardProps> = React.memo(
  ({
    name,
    size,
    progress,
    uploading,
    cover,
    error,
    showCancel,
    showDelete,
    showDownload,
    showPreview = true,
    showReupload,
    onCancel,
    onDelete,
    onDownload,
    onReupload,
    className,
    data,
    style,
  }) => {
    const imagePreviewRef =
      useRef<IImagePreviewRef>() as RefObject<IImagePreviewRef>
    const showProgress = (progress && progress < 100) || uploading
    const onPreview = () => {
      if (!showPreview) return
      // @ts-ignore
      previewFile(data, (url) => {
        imagePreviewRef.current?.show(url)
      })
    }

    return (
      <div className={classNames(preCls, className)} style={style}>
        <div className='flex items-center gap-1 w-full'>
          {showProgress && <Loading />}
          {cover &&
            (typeof cover === 'string' ? (
              <img className={`${preCls}__icon-img shrink-0`} src={cover} />
            ) : (
              cover
            ))}
          <div className='flex flex-1 w-0'>
            <Ellipsis
              overlayStyle={{ color: '#fff!important' }}
              className={classNames(`${preCls}__title`, {
                [`${preCls}__title-error`]: error,
              })}
            >
              <span
                onClick={onPreview}
                style={
                  {
                    // cursor: showPreview ? 'pointer' : 'default',
                    // color: error ? '#F05E5E' : isHover ? '#2879ff' : '#3F4755',
                  }
                }
              >
                {data?.documentSource === 8 || data?.DocumentSource === 8 ? (
                  <OpenFileName
                    openId={
                      data?.ClientUrl ||
                      data?.dfsUrl ||
                      data?.DfsUrl ||
                      data?.dfsPath
                    }
                  ></OpenFileName>
                ) : (
                  name
                )}
              </span>
            </Ellipsis>
          </div>

          {showCancel && showProgress && (
            <div className={`${preCls}__cancel shrink-0`} onClick={onCancel}>
              <i className='tu-icon-canceled'></i>
            </div>
          )}
        </div>
        <ImagePreview ref={imagePreviewRef} />
      </div>
    )
  }
)

function Loading() {
  return (
    <span className={classNames(preCls, 'animate-spin')}>
      <svg
        viewBox='0 0 1024 1024'
        focusable='false'
        data-icon='loading'
        width='1em'
        height='1em'
        fill='currentColor'
        aria-hidden='true'
      >
        <path d='M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z'></path>
      </svg>
    </span>
  )
}

export default AIRender
