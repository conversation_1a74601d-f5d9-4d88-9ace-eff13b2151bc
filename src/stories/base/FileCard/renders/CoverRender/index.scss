.tita-ui-cover-render {
  width: 120px;

  &__progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(20, 28, 40, 0.3);

    &-uploading {
      width: 20px;
      height: 20px;
      background-image: url(http://xfile6.tita.com/ux/tita-home-page/public/ui-loading.svg);
      background-size: 100% 100%;
      position: absolute;
      top: 50%;
      left: 50%;
      animation: titaUiUploading 1s linear infinite;
    }

    &-bar-container {
      position: absolute;
      bottom: 8px;
      left: 10px;
      height: 6px;
      width: calc(100% - 20px);
      background-color: rgba(255, 255, 255, 0.6);
      border-radius: 10px;
    }
    &-bar {
      background-color: #2879ff;
      height: 100%;
      border-radius: 10px;
      transition: width 0.3s;
    }
  }
  &__icon {
    position: relative;
    width: 120px;
    height: 120px;
    overflow: hidden;
    position: relative;
    border-radius: 12px;

    &-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  &__icon:hover &__download-line {
    height: 30px;
    opacity: 1;
  }
  &__download-line {
    position: absolute;
    width: 100%;
    bottom: 0;
    color: #fff;
    background: rgba(20, 28, 40, 0.2);
    backdrop-filter: blur(2px);
    font-size: 12px;
    line-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0px 0px 10px 10px;
    cursor: pointer;
    height: 0;
    opacity: 0;
    transition: height 0.2s, opacity 0.3s, color 0.3s;

    &:hover {
      color: #1687d9;
    }

    &__icon {
      font-size: 16px;
    }
  }

  &__title {
    margin-top: 8px;
    margin-bottom: 0;
    color: #3f4755;
    font-size: 12px;
    text-align: center;
    width: 100%;
  }
}

@keyframes titaUiUploading {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}