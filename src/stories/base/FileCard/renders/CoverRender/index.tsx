import Tooltip from '@/stories/base/Tooltip/tooltip'
import { getLocale } from '@tita/utils'
import classNames from 'classnames'
import React, { FC } from 'react'
import { IFileCardProps } from '../..'
import './index.scss'
import Ellipsis from '@/stories/base/Ellipsis'
import { Corner } from '@/stories/base/Corner'
import Button from '@/stories/base/Button'
import { isMobile } from '@/utils/platform'
import OpenFileName from '@/stories/business/OpenFileName'

export interface ICoverRenderProps {
  coverStyle?: React.CSSProperties
  hideTitle?: boolean
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-cover-render'

export const CoverRender: FC<ICoverRenderProps & IFileCardProps> = React.memo(
  ({
    name,
    coverStyle,
    hideTitle,
    size,
    progress,
    uploading,
    cover,
    error,
    showCancel,
    showDelete,
    showDownload,
    showPreview,
    showReupload,
    onCancel,
    onDelete,
    onDownload,
    onPreview,
    onReupload,
    data,
    className,
    style,
  }) => {
    const showProgress = (!!progress && progress < 100) || uploading
    // const showProgress = true
    const content = (
      <div
        className={classNames(preCls, className)}
        style={{
          ...style,
          width: coverStyle?.width,
        }}
        onClick={onPreview}
      >
        <div className={`${preCls}__icon`} style={coverStyle}>
          {showProgress && (
            <div className={`${preCls}__progress`}>
              <div className={`${preCls}__progress-uploading`}></div>
              <div className={`${preCls}__progress-bar-container`}>
                <div
                  className={`${preCls}__progress-bar`}
                  style={{ width: `${progress}%` }}
                  // style={{ width: `50%` }}
                />
              </div>
            </div>
          )}
          {cover &&
            (typeof cover === 'string' ? (
              <img className={`${preCls}__icon-img`} src={cover} />
            ) : (
              cover
            ))}
          {!isMobile() && showDownload && !showProgress && (
            <div className={`${preCls}__download-line`} onClick={onDownload}>
              <i
                className={classNames(
                  'tu-icon-APP-xiazai',
                  `${preCls}__download-line__icon`
                )}
              />
              {getLocale('Mod_Download') || '下载'}
            </div>
          )}
        </div>
        {!hideTitle && (
          <Ellipsis className={`${preCls}__title`}>
            {data?.documentSource === 8 || data?.DocumentSource ? (
              <OpenFileName
                openId={
                  data?.ClientUrl || data?.dfsUrl || data?.DfsUrl || data?.dfsPath
                }
              ></OpenFileName>
            ) : (
              name
            )}
          </Ellipsis>
        )}
      </div>
    )
    if (showDelete) {
      return (
        <Corner
          corner={
            <Button icon='H5-close-s' primary size='mini' onClick={onDelete} />
          }
          tooltip='删除'
          position='topRight'
          offset={{
            x: '50%',
            y: '-50%',
          }}
        >
          {content}
        </Corner>
      )
    }
    return content
  }
)

export default CoverRender
