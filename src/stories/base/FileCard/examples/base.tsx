import React, { FC } from 'react'
import FileCard from '@/stories/base/FileCard'
import Panel from '../../Panel'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  return (
    <>
      <Panel title="默认主题">
        <div className='flex flex-wrap child-r-8px child-b-8px'>
          {new Array(4).fill(0).map((_, index) => (
            <FileCard
              key={index}
              name='超长的名字啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊.png'
              size='100.kb'
              cover='https://xfile6.tita.com/ux/tita-home-page/public/file-type-mind.svg'
              showDelete
              showDownload
              showPreview
            />
          ))}
        </div>
      </Panel>
      <Panel title="封面类型">
        <div className='flex flex-wrap child-r-8px child-b-8px'>
          {new Array(4).fill(0).map((_, index) => (
            <FileCard
              key={index}
              theme="cover"
              name='超长的名字啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊.png'
              size='100.kb'
              cover='https://xfile6.tita.com/ux/tita-home-page/public/file-type-mind.svg'
              showDelete
              showDownload
              showPreview
              renderProps={{
                coverStyle: {
                  width: 60,
                  height: 60,
                }
              }}
            />
          ))}
        </div>
      </Panel>
      <Panel title="迷你类型">
        <div className='flex flex-col child-b-8px'>
          {new Array(4).fill(0).map((_, index) => (
            <FileCard
              key={index}
              theme="mini"
              name='超长的名字啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊.png'
              size='100.kb'
              cover='https://xfile6.tita.com/ux/tita-home-page/public/file-type-mind.svg'
              showDelete
              showDownload
              showPreview
            />
          ))}
        </div>
      </Panel>
    </>
  )
}

export default React.memo(Base)
