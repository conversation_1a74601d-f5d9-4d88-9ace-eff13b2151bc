import React, { FC, useMemo } from 'react'
import dayjs from 'dayjs'

export interface IDateProps {
  /**
   * 日期
   * @editType string
   * @default 2023-02-09
   */
  date: string | number | dayjs.Dayjs | Date | Function | null | undefined
  placeholder?: string
  format?: string
  showYear?: boolean
  showTime?: boolean
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

// @ts-ignore
export const Date: FC<IDateProps> = React.memo(({ showYear: propsShowYear, format, showTime, placeholder, date }) => {
  if (!date && placeholder) return <span className="text-#bfc7d5">{placeholder}</span>
  if(!date) return <></>;
  if (date instanceof Function) return date()
  const isCurrentYear = dayjs(date).year() === dayjs().year()
  const showYear: boolean = useMemo(() => {
    if (!isCurrentYear) return true
    if (propsShowYear !== undefined) return propsShowYear
    return dayjs(date).year() !== dayjs().year()
  }, [isCurrentYear, propsShowYear])
  if (format) return dayjs(date).format(format)
  if (showTime) {
    return dayjs(date).format(showYear ? 'YYYY/MM/DD HH:mm' : 'MM/DD HH:mm')
  } else {
    return dayjs(date).format(showYear ? 'YYYY/MM/DD' : 'MM/DD')
  }
})

export default Date
