.tita-ui-button {
  background: #FFFFFF !important;
  box-sizing: border-box;
  display: inline-flex;
  height: auto;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  border-radius: 18px;
  transition: all .3s;
  cursor: pointer;
  white-space: nowrap;

  &--disablePadding {
    padding: 0px !important;
  }

  &--autoPaddingX {
    padding-left: 0;
    padding-right: 0;

    &:hover {
      padding-left: 12px;
      padding-right: 12px;
    }
  }
  &__loading {
    pointer-events: none;
    animation: titaUiButtonLoading 1s linear infinite;
  }
  &--default {
    background: #fff  !important;
    color: #3F4755;
    box-shadow: none;

    @media only screen and (min-width: 767px) {
      box-shadow: none;
      &:hover:not(&--noClick,&--primary,&--second,&--danger,.tita-ui-button--disabled) {
        color: #2879FF;
      }
    }
    &--primary {
      background: #2879FF !important;
      color: #fff;

      @media only screen and (min-width: 767px) {
        &:hover:not(.tita-ui-button--disabled) {
          background: #5C8EFF !important;
        }
      }
    }
    &--primary.tita-ui-button--loading {
      background: #5C8EFF !important;
    }
    &--second {
      background: rgba(40, 121, 255, .1) !important;
      color: #2879FF;

      @media only screen and (min-width: 767px) {
        &:hover {
          background: rgba(40, 121, 255, 0.2) !important;
        }
      }
    }
    &--danger.tita-ui-button--default--second-danger {
      background-color: #FDEBE9 !important;
      color: #F03D26;

      &:hover {
        background-color: #FDEBE9 !important;
        color: #F03D26 !important;
      }
    }
    &--danger {
      background: #F05E5E !important;
      color: #fff;

      &:hover {
        background: #F37E7E !important;
      }
    }
    &--orange {
      background: #FF7E3D !important;
      color: #fff !important;

      &:hover {
        background: #FF7E3D !important;
      }
    }
    &--gray {
      background: #F0F4FA !important;
      box-shadow: none;
    }
    &--gray.tita-ui-button--default--primary {
      background: #E9F1FF !important;
      color: #2879FF;
    }
    &--noClick {
      background: #F0F2F5 !important;
      color: #A4ACB9;
      cursor: not-allowed;
    }
  }
  &--border {
    background: #fff !important;
    border: 1px solid #DFE3EA;
    color: #6F7886;
    
    @media only screen and (min-width: 767px) {
      &:hover:not(&--noClick,&--danger,&--primary,.tita-ui-button--disabled) {
        border: 1px solid #2879FF;
        color: #2879FF;
      }
    }
    &--primary {
      border: 1px solid #2879FF;
      color: #2879FF;
      @media only screen and (min-width: 767px) {
        &:hover {
          color: #5C8EFF;
          background: rgba(40,121,255,0.1) !important;
        }
      }
    }
    &--second {
      border: 1px solid #2879FF;
      background: rgba(40, 121, 255, .1) !important;
      color: #2879FF;

      @media only screen and (min-width: 767px) {
        &:hover {
          background: rgba(40, 121, 255, 0.2) !important;
        }
      }
    }
    &--danger {
      border: 1px solid #F05E5E;
      color: #F05E5E;

      &:hover {
        background-color: rgba(240, 94, 94, 0.1) !important;
        border: 1px solid #F05E5E !important;
      }
    }
    &--orange {
      border: 1px solid #FF7E3D !important;
      color: #FF7E3D !important;

      &:hover {
        background-color: rgba(240, 94, 94, 0.1) !important;
        border: 1px solid #FF7E3D !important;
      }
    }
    &--noClick {
      border: 1px solid #E9ECF0;
      color: #6F7886;
      cursor: not-allowed;
    }
  }
  &--text {
    background: transparent !important;
    color: #3F4755;
    
    @media only screen and (min-width: 767px) {
      &:hover:not(&--noClick,.tita-ui-button--disabled) {
        background: #F0F4FA !important;
      }
    }
    &--primary {
      color: #2879FF;

      @media only screen and (min-width: 767px) {
        &:hover {
          color: #5C8EFF;
        }
      }
    }
    &--danger {
      color: #F05E5E;
    }
    &--noClick {
      color: #A4ACB9;
      cursor: not-allowed;
    }
  }
  &--link {
    background: transparent !important;
    color: #3F4755;
    
    &:hover:not(&--noClick,&--danger,&--primary,.tita-ui-button--disabled) {
      color: #2879FF;
    }
    &--primary {
      color: #2879FF;

      @media only screen and (min-width: 767px) {
        &:hover {
          color: #5C8EFF;
        }
      }
    }
    &--danger {
      color: #F05E5E;
    }
    &--noClick {
      color: #A4ACB9;
      cursor: not-allowed;
    }
  }
  &--disabled {
    opacity: .5;
    cursor: not-allowed;
    pointer-events: all !important;

  }

  &--shape {
    &-default {
      
    }
    &-square {
      border-radius: 8px;
    }
    &-circle {
      border-radius: 16px;
    }
  }

  &-size {
    &-default {
      height: 32px;
      min-height: 32px;

      &--isCircle {
        width: 32px;
        min-width: 32px;

        .tita-ui-button__icon {
          margin-right: 0;
        }
      }
    }
    &-large {
      height: 36px;
      min-height: 36px;
      font-size: 14px;

      &--isCircle {
        width: 36px;
        min-width: 36px;

        .tita-ui-button__icon {
          margin-right: 0;
        }
      }
    }
    &-small {
      height: 28px;
      min-height: 28px;
      font-size: 12px;
      i {
        font-size: 14px;
        line-height: 22px;
      }

      &--isCircle {
        width: 28px;
        min-width: 28px;

        .tita-ui-button__icon {
          margin-right: 0;
        }
      }
    }
    &-least {
      height: 24px;
      min-height: 24px;
      font-size: 12px;

      &--isCircle {
        width: 24px;
        min-width: 24px;

        .tita-ui-button__icon {
          margin-right: 0;
        }
      }
    }
    &-mini {
      height: 20px;
      min-height: 20px;
      font-size: 12px;

      &--isCircle {
        width: 20px;
        min-width: 20px;
        padding: 10px;

        .tita-ui-button__icon {
          margin-right: 0;
          width: 20px;
          font-size: 12px;
        }
      }
    }
    &-custom {
      height: 32px;
      font-size: 12px;
      padding: 0 12px;
    }
  }
  &-size-least &__icon,
  &-size-custom &__icon {
    font-size: 14px;
  }
  &__icon {
    display: flex;
    align-items: center;
    font-size: 16px;
    margin-right: 4px;
  }
}
@keyframes titaUiButtonLoading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}