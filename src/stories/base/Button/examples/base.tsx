import React, { FC } from 'react'
import Button, { IButtonProps } from '@/stories/base/Button'
import './base.scss'
import { CheckOutlined } from '@ant-design/icons'

export interface IBaseProps extends IButtonProps {
  /**
   * Is this the principal call to action on the page?11
   */
  type?: 'default' | 'border' | 'text' | 'link'
}

const Base: FC<IBaseProps> = (props) => {
  return (
    <>
      <p>type</p>
      <div className='space-x-2'>
        <Button>Hello Button!</Button>
        <Button type='border'>border Button!</Button>
        <Button type='text'>text Button!</Button>
        <Button type='link'>link Button!</Button>
      </div>

      <p className='mt-3'>形状</p>
      <div className='space-x-2'>
        <Button>Hello Button!</Button>
        <Button icon='H5-enjoy-s' primary></Button>
        <Button icon='H5-enjoy-s'></Button>
        <Button icon='H5-enjoy-s' type='border'></Button>
        <Button shape='square'>Hello Button!</Button>
      </div>

      <p className='mt-3'>icon</p>
      <div className='space-x-2'>
        <Button icon='H5-enjoy-s'>Hello Button!</Button>
        <Button icon='H5-enjoy-s' type='border'>border Button!</Button>
        <Button icon='H5-enjoy-s' type='text'>text Button!</Button>
        <Button icon='H5-enjoy-s' type='link'>link Button!</Button>
      </div>

      <p className='mt-3'>默认带主题色</p>
      <div className='space-x-2'>
        <Button icon='H5-enjoy-s' primary>default Button!</Button>
        <Button icon='H5-enjoy-s' primary type='border'>border Button!</Button>
        <Button icon='H5-enjoy-s' primary type='text'>text Button!</Button>
        <Button icon='H5-enjoy-s' primary type='link'>link Button!</Button>
      </div>

      <p className='mt-3'>second</p>
      <div className='space-x-2'>
        <Button icon='H5-enjoy-s' second>default Button!</Button>
        <Button icon='H5-enjoy-s' second></Button>
        <Button icon={<CheckOutlined />} second></Button>
        <Button icon={<CheckOutlined />} second type='border'>border Button!</Button>
        <Button icon='H5-enjoy-s' second type='border'>border Button!</Button>
        <Button icon='H5-enjoy-s' second type='text'>text Button!</Button>
        <Button icon='H5-enjoy-s' second type='link'>link Button!</Button>
      </div>

      <p className='mt-3'>gray</p>
      <div className='space-x-2'>
        <Button icon='H5-enjoy-s' gray>default Button!</Button>
        <Button icon='H5-enjoy-s' gray></Button>
        <Button icon='H5-enjoy-s' gray type='border'>border Button!</Button>
        <Button icon='H5-enjoy-s' gray type='text'>text Button!</Button>
        <Button icon='H5-enjoy-s' gray type='link'>link Button!</Button>
      </div>

      <p className='mt-3'>危险的</p>
      <div className='space-x-2'>
        <Button icon='H5-enjoy-s' danger>default Button!</Button>
        <Button icon='H5-enjoy-s' danger second>default Button!</Button>
        <Button icon='H5-enjoy-s' danger type='border'>border Button!</Button>
        <Button icon='H5-enjoy-s' danger type='text'>text Button!</Button>
        <Button icon='H5-enjoy-s' danger type='link'>link Button!</Button>
      </div>

      <p className='mt-3'>禁用</p>
      <div className='space-x-2'>
        <Button icon='H5-enjoy-s' disabled>Hello Button!</Button>
        <Button icon='H5-enjoy-s' disabled primary>Hello Button!</Button>
        <Button icon='H5-enjoy-s' disabled type='border'>border Button!</Button>
        <Button icon='H5-enjoy-s' disabled type='text'>text Button!</Button>
        <Button icon='H5-enjoy-s' disabled type='link'>link Button!</Button>
      </div>

      <p className='mt-3'>不可点击</p>
      <div className='space-x-2'>
        <Button icon='H5-enjoy-s' noClick>Hello Button!</Button>
        <Button icon='H5-enjoy-s' noClick type='border'>border Button!</Button>
        <Button icon='H5-enjoy-s' noClick type='text'>text Button!</Button>
        <Button icon='H5-enjoy-s' noClick type='link'>link Button!</Button>
      </div>

      <p className='mt-3'>Loading</p>
      <div className='space-x-2'>
        <Button loading>Hello Button!</Button>
      </div>
    </>
  )
}

export default React.memo(Base)
