import React, { FC } from 'react'
import Button, { IButtonProps } from '@/stories/base/Button'

export interface ISizeProps extends IButtonProps {
  /**
   * Is this the principal call to action on the page?11
   */
  type?: 'default' | 'primary' | 'dashed' | 'text' | 'link'
}

const Size: FC<ISizeProps> = (props) => {
  return (
    <>
      <div className="space-x-8px">
        <Button primary size="mini">最小尺寸</Button>
        <Button primary size="least">小尺寸</Button>
        <Button primary size="small">中尺寸</Button>
        <Button primary>默认尺寸</Button>
        <Button primary size="large">大尺寸</Button>
      </div>
      <div className="space-x-8px">
        <Button primary type="text" size="mini">最小尺寸</Button>
        <Button primary type="text" size="least">小尺寸</Button>
        <Button primary type="text" size="small">中尺寸</Button>
        <Button primary type="text">默认尺寸</Button>
        <Button primary type="text" size="large">大尺寸</Button>
      </div>
      <div className="space-x-8px">
        <Button primary icon="upload" size="mini" />
        <Button primary icon="upload" size="least" />
        <Button primary icon="upload" size="small" />
        <Button primary icon="upload" />
        <Button primary icon="upload" size="large" />
      </div>
    </>
  )
}

export default React.memo(Size)
