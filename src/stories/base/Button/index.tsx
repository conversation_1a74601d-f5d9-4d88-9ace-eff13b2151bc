import { useDebounceFn } from 'ahooks'
import classNames from 'classnames'
import React, { FC, useMemo } from 'react'
import './index.scss'

const preCls = 'tita-ui-button'

export interface IButtonProps extends Omit<React.DetailedHTMLProps<React.ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, 'type'> {
  /**
   * 点击事件
   */
  onClick?: React.MouseEventHandler<HTMLButtonElement>
  /**
   * 按钮类型
   * @default default
   */
  type?: 'default' | 'border' | 'text' | 'link'
  /**
   * 是否显示主题色
   */
  primary?: boolean
  /**
   * 橙色主题色
   */
  orange?: boolean
  /**
   * loading
   */
  loading?: boolean
  /**
   * 次级的主题色
   */
  second?: boolean
  /**
   * 灰色的主题色
   */
  gray?: boolean
  /**
   * 危险的
   */
  danger?: boolean
  /**
   * 默认没有左右 padding，鼠标 hover 之后才展示
   */
  autoPaddingX?: boolean
  /**
   * 链接，当 type 为 link 时才有效
   */
  href?: string
  /**
   * 跳转方式，当 type 为 link 时才有效
   */
  target?: React.HTMLAttributeAnchorTarget
  /**
   * 是否禁用
   */
  disabled?: boolean
  /**
   * 禁用防抖
   */
  disableDebounce?: boolean
  /**
   * 不可点击
   */
  noClick?: boolean
  /**
   * 按钮大小
   * @default default
   */
  size?: 'large' | 'default' | 'small' | 'least' | 'mini' | 'custom'
  /**
   * 形状
   * @default default
   */
  shape?: 'default' | 'square' | 'circle'
  /**
   * 最小宽度
   */
  minWidth?: number
  /**
   * 图标
   */
  icon?: string | React.ReactNode
  /**
   * 图标颜色
   */
  iconColor?: string
  /**
   * 按钮内容
   * @editType string
   * @default Click me!
   */
  children?: React.ReactNode | React.ReactNode[]
  className?: string
  style?: React.CSSProperties
  disablePadding?: boolean
  iconStyle?: React.CSSProperties
}

export const Button: FC<IButtonProps> = (props) => {
  const {
    type = 'default',
    size = 'default',
    disabled = false,
    primary = false,
    orange = false,
    second = false,
    disableDebounce,
    loading,
    gray,
    danger = false,
    noClick = false,
    autoPaddingX = false,
    className,
    shape = 'default',
    minWidth,
    icon,
    iconColor,
    style,
    children,
    onClick,
    href,
    target = '_blank',
    disablePadding,
    iconStyle,
    ...other
  } = props

  const isCircle = icon && !children

  const canSubmit = !disabled && !loading

  const { run } = useDebounceFn((e) => canSubmit && onClick && onClick(e), {
    wait: 500,
    leading: true,
    trailing: false,
  })

  const renderIcon = useMemo(() => {
    if (typeof icon === 'string') {
      return <i className={`${preCls}__icon tu-icon-${icon}`} style={{ color: iconColor, ...iconStyle }} />
    }
    return <span className={`${preCls}__icon`} style={{ color: iconColor, ...iconStyle }}>{icon}</span>
  }, [icon, iconStyle])

  const button = (
    <button
      type="button"
      className={classNames(preCls, className, {
        [`${preCls}--${type}`]: type,
        [`${preCls}--shape-${shape}`]: shape,
        [`${preCls}--${type}--noClick`]: noClick,
        [`${preCls}--${type}--primary`]: primary,
        [`${preCls}--${type}--orange`]: orange,
        [`${preCls}--${type}--second`]: second,
        [`${preCls}--${type}--gray`]: gray,
        [`${preCls}--${type}--danger`]: danger,
        [`${preCls}--${type}--second-danger`]: second && danger,
        [`${preCls}--disabled`]: disabled,
        [`${preCls}--loading`]: loading,
        [`${preCls}--autoPaddingX`]: !disabled && autoPaddingX,
        [`${preCls}-size-${size}`]: size,
        [`${preCls}-size-${size}--isCircle`]: isCircle,
        [`${preCls}--disablePadding`]: disablePadding
      })}
      onClick={canSubmit && disableDebounce ? onClick : run}
      style={{ minWidth, ...style }}
      {...other}
    >
      {loading && <i className={`${preCls}__icon ${preCls}__loading tu-icon-gengxin1`} style={{ color: iconColor }} />}
      {icon && !loading && renderIcon}
      {children}
    </button>
  )

  if (type === 'link' && href) {
    return <a href={href} target={target}>{button}</a>
  }

  return button
}

export default Button