import classNames from 'classnames'
import dayjs from 'dayjs'
import React, { FC, useEffect, useState } from 'react'
import DateText from '../Date'
import { getSpaceDays } from '../Gantt/utils/date'
import Tooltip from '../Tooltip'
import { getLocale } from '@tita/utils'
import './index.scss'

export interface IDateSurplusProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  startDate?: string | number | Date | dayjs.Dayjs | null | undefined
  deadLine: string | number | Date | dayjs.Dayjs | null | undefined
  warningDays?: number
  /**
   *  具体的过期/剩余天数
   */
  remainDay?: number | string
  noTips?: boolean
  isOver?: boolean
  defaultTextColor?: string
  /** 
   * @type string
   * 如果有目标字段，则不展示 文本内容，只展示格式化后的日期
   */
  disableText?: string
}

const preCls = 'tita-ui--date-surplus'

export const DateSurplus: FC<IDateSurplusProps> = React.memo((props) => {

  const { className, startDate, deadLine: propsDeadLine, warningDays = 30, style, remainDay, noTips, defaultTextColor, disableText, isOver:propsIsOver } = props
  const [isOver, setIsOver] = useState(propsIsOver)
  const deadLine = propsDeadLine ? new Date(dayjs(propsDeadLine).toDate()): ''

  useEffect(() => {
    if (propsIsOver !== undefined) return  setIsOver(propsIsOver)
    if (!deadLine) return
    if (isOver) return setIsOver(true)
    setIsOver(false)
  }, [deadLine, remainDay, propsIsOver])

  const getDayNum = (day1: Date, day2: Date) => {
    return Math.abs(getSpaceDays(day1, day2)) + 2
  }

  const getDay = (day: number, currentDate: Date, deadlineDate: Date) => {
    if (!deadLine) return
    // 单独计算剩余/过期天数
    let deadLineContent = ''
    const showYear = deadlineDate.getFullYear() !== currentDate.getFullYear()
    showYear ? deadLineContent = dayjs(deadlineDate).format('YYYY/MM/DD') : deadLineContent = dayjs(deadlineDate).format('MM/DD')
    if (remainDay == null || remainDay === '') {
      // 是否过期
      // if (isOver) {
      //   const days = getDayNum(currentDate, deadlineDate) - 2
      //   if (days > 0) return `过期${days}天`
      // }

      // // 是否到达倒计时
      // const temp = new Date(currentDate)
      // const targetMs = temp.setDate(temp.getDate() + day)
      // const isUptoEnd = targetMs >= deadlineDate.getTime() // 是否到达倒计时周期内
      // if (isUptoEnd) {
      //   const days = getDayNum(deadlineDate, currentDate)
      //   if (dayjs(deadLine).format('YYYY/MM/DD') === dayjs(currentDate).format('YYYY/MM/DD')) return `剩余${days - 1}天`
      //   return `剩余${days}天`
      // }

      return deadLineContent
    }
    if (remainDay !== 0) {
      const matches = (remainDay as string).match(/\d+/);
      const num = parseInt(matches ? matches[0] : '');
      /** 
       *  如果已经过期,则忽略剩余天数限制
       *  如果没有过期, 则添加warningDays限制
       */
      if ((num > warningDays && !isOver)) return deadLineContent
      if (disableText && remainDay.toString().includes(disableText)) return deadLineContent
      return remainDay
    }

  }
  const currentDate = new Date()
  const getDateWord = () => {
    if (!startDate && !deadLine) return getLocale('Pro_page_Plan_ClosingDate')
    if (noTips && !!remainDay) return remainDay
    return (remainDay === 0 || remainDay === '') ? <DateText date={deadLine} /> : getDay(warningDays, currentDate, deadLine as Date)
  }

  /** 是否跨年 */
  const isStraddleYear =
    startDate && deadLine
      ? dayjs(startDate).year() !== dayjs(deadLine).year()
      : false

  return (
    <Tooltip
      overlay={
        startDate && deadLine ? (
          <p>
            <DateText date={startDate} showYear={isStraddleYear} />
            ~
            <DateText date={deadLine} showYear={isStraddleYear} />
          </p>
        ) : (
          getLocale('Mod_Startendtime')
        )
      }
      placement='top'
    >
      <div
        className={classNames(preCls, className, {
          [`${preCls}-over`]: isOver,
        })}
        style={{ ...style, color: defaultTextColor }}
      >
        {getDateWord()}
      </div>
    </Tooltip>
  )
})

export default DateSurplus
