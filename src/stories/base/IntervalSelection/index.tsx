import classNames from 'classnames'
import React, {
  memo,
  forwardRef,
  useMemo,
  useImperativeHandle,
  useCallback,
  useState,
  useRef,
  useEffect,
} from 'react'
import { SuperTable } from '../Table'
import './index.scss'
import { defaultColors, getInitIntervalList, operationTypeList } from './utils'
import { Input } from '../Input'
import Select from '../Select'
import ColorSelector from '../ColorSelector'
import cloneDeep from 'lodash/cloneDeep'
import { useRefState } from '@tita/hooks'
import { current } from '@reduxjs/toolkit'
import { getRandomStr } from '@tita/utils'

export interface IIntervalSelectionProps {
  className?: string
  style?: React.CSSProperties
  canChooseColor?: boolean // 是否可以选择颜色
  initIntervalList?: Array<IIntervalItemProps> // 默认区间
  oneLimit?: boolean // 是否所有区间共用一套处理逻辑
  openDefaultInterval?: boolean // 是否开启默认区间计算
  intervalStart?: number // 计算默认区间所需起始区间
  intervalEnd?: number // 计算默认区间所需结束区间
  step?: number // 默认区间步长
  allRequired?: (isRequired: boolean) => void
}

export interface IntervalSelectionRef {
  getData: () => Array<IIntervalItemProps>
  getVaild: () => { isVaild: boolean; errRow: number | null }
}
export interface IIntervalItemProps {
  color?: string
  lowerLimit: string | number
  lowerOperationType: string
  upperLimit: string | number | null
  upperOperationType: string
  scoreText: string
  uniqueId?: string
}

const preCls = 'tita-ui-interval-selection'

export const IntervalSelection = memo(
  forwardRef<IntervalSelectionRef, IIntervalSelectionProps>((props, ref) => {
    const {
      className,
      style,
      canChooseColor = false,
      initIntervalList = [],
      oneLimit = false,
      openDefaultInterval = false,
      intervalStart = 1,
      intervalEnd = 100,
      step = 10,
      allRequired,
    } = props

    const [intervalList, intervalsRef, setIntervals] = useRefState<Array<any>>(
      getInitIntervalList(
        openDefaultInterval,
        initIntervalList,
        intervalStart,
        intervalEnd,
        step
      )
    )

    const DEFAULT_OPERATIONTYPE = operationTypeList[0].value

    const handleCheck = useCallback(
      (list: Array<any>) => {
        // 除最后一行 所有上限均有值
        if (allRequired) {
          const isAllRequired = list.every(
            (v, ind) =>
              ind == list.length - 1 ||
              typeof v.upperLimit === 'number' ||
              v.upperLimit
          )
          allRequired(isAllRequired)
        }
      },
      [allRequired]
    )

    useEffect(() => {
      if (initIntervalList.length && allRequired) {
        handleCheck(initIntervalList)
      }
    }, [])

    const isVaildFn = useCallback(() => {
      // 除第一行和最后一行 每一行上限值大于下限
      const list = intervalsRef.current;
        let i = 0
        for (; i < list.length; i++) {
          const { lowerLimit, upperLimit } = list[i] || {}
            if (!(+lowerLimit < +upperLimit)) {
              break
            }
        }
        // 处理for循环多走一次
        if (i === list.length) {
          i--;
        }
        const vaild = i === list.length - 1
        return {
          isVaild: vaild,
          errRow: vaild ? null : i
        }
    }, [intervalsRef.current])

    useImperativeHandle(ref, () => ({
      getData: () => intervalList,
      getVaild: () => { return isVaildFn() }
    }))

    const createInterval = () => {
      const copy = cloneDeep(intervalsRef.current)
      const lowerLimit =
      copy.length === 0
          ? null
          : copy[copy.length - 1].upperLimit

      let lowerOperationType
      // 除第一行数据外，下一行数据的下限均根据上一行数据设置变化
      // 上一行上限规则是 <=，下一行下限规则是 <；上一行上限规则是 <，下一行下限规则是 <=
      if (copy.length === 0) {
        lowerOperationType = '<'
      } else {
        lowerOperationType =
        copy[copy.length - 1].upperOperationType ===
          DEFAULT_OPERATIONTYPE
            ? '<'
            : '≤'
      }

      const upperOperationType =
        oneLimit && intervalList.length
          ? intervalList[0].upperOperationType
          : DEFAULT_OPERATIONTYPE

      const interval: IIntervalItemProps = {
        uniqueId: getRandomStr(),
        lowerLimit,
        lowerOperationType,
        upperLimit: null,
        upperOperationType,
        scoreText: '得分',
      }
      if (canChooseColor) {
        const colorInd = copy.length % defaultColors.length
        interval['color'] = defaultColors[colorInd]
      }
      copy.push(interval)
      setIntervals(copy)
      intervalsRef.current = copy
    }

    const deleteInterval = (index: number) => {
      const copy = cloneDeep(intervalsRef.current)
      copy.splice(index, 1)
      // 非首行删除时 被删除的上一行的上限赋值给被删除下一行的下限
      if (index > 0) {
        copy[index].lowerLimit = copy[index - 1].upperLimit;
      }
      copy[0].lowerLimit = -1
      setIntervals(copy)
      intervalsRef.current = copy
    }

    // @ts-ignore
    const onChange = useCallback((value?: string | number, type: string, index: number) => {
        const copy = cloneDeep(intervalsRef.current)
        copy[index][type] = value
        if (type === 'upperOperationType' && copy[index + 1]) {
          const lowerOperationType =
            value === DEFAULT_OPERATIONTYPE ? '<' : '<='
          if (oneLimit) {
            copy.forEach((_, ind) => {
              copy[ind][type] = value
              copy[ind]['lowerOperationType'] = lowerOperationType
            })
          } else {
            copy[index + 1].lowerOperationType = lowerOperationType
          }
        } else if (type === 'upperLimit' && copy[index + 1]) {
          copy[index + 1].lowerLimit = value
        }
        setIntervals(copy)
        intervalsRef.current = copy
        handleCheck(copy)
      },
      [intervalList, oneLimit]
    )

    const renderColumns = useMemo(() => {
      const interval = {
        title: '区间',
        dataIndex: 'index',
        key: 'index',
        render: ({ rowIdx }: any) => {
          return <span style={{ color: '#3F4755' }}>{rowIdx + 1}.</span>
        },
        width: 58,
      }
      const color = {
        title: '颜色',
        dataIndex: 'color',
        key: 'color',
        render: ({ value, rowIdx }: any) => {
          return (
            <ColorSelector
              color={value}
              onChange={(c) => {
                onChange(c, 'color', rowIdx)
              }}
            ></ColorSelector>
          )
        },
        width: 52,
      }
      const lowerLimit = {
        title: '下限',
        dataIndex: 'lowerLimit',
        key: 'lowerLimit',
        render: ({ value, rowIdx }: any) => {
          // 第一行不展示
          return rowIdx === 0 ? (
            <></>
          ) : (
            <Input
              style={{ width: '100%', color: '#3F4755' }}
              valueFormat='number'
              value={value}
              disabled
            />
          )
        },
        flex: 1,
      }
      const lowerOperationType = {
        title: '',
        dataIndex: 'lowerOperationType',
        key: 'lowerOperationType',
        render: ({ value, rowIdx }: any) => {
          return rowIdx === 0 ? (
            <></>
          ) : (
            <span style={{ color: '#3F4755' }}>{value}</span>
          )
        },
        width: 44,
      }
      const scoreText = {
        title: '',
        dataIndex: 'scoreText',
        key: 'scoreText',
        render: ({ value }: any) => {
          return <span style={{ color: '#3F4755' }}>得分</span>
        },
        width: 60,
      }
      const upperOperationType = {
        title: '',
        dataIndex: 'upperOperationType',
        key: 'upperOperationType',
        render: ({ value, rowIdx }: any) => {
          // 最后一行不展示
          return rowIdx && rowIdx === intervalsRef.current.length - 1 ? (
            <></>
          ) : (
            <Select
              menuData={operationTypeList}
              initialValue={value}
              value={value}
              onChange={(v) => {
                onChange(v, 'upperOperationType', rowIdx)
              }}
            ></Select>
          )
        },
        width: 76,
      }
      const upperLimit = {
        title: '上限',
        dataIndex: 'upperLimit',
        key: 'upperLimit',
        render: ({ value, rowIdx }: any) => {
          // 最后一行不展示
          return rowIdx && rowIdx === intervalsRef.current.length - 1 ? (
            <></>
          ) : (
            <Input
              valueFormat='number'
              style={{ width: '100%', color: '#3F4755' }}
              value={value}
              onChange={(v) => {
                onChange(v, 'upperLimit', rowIdx)
              }}
            />
          )
        },
        flex: 1,
      }
      const operation = {
        title: '',
        dataIndex: 'operation',
        key: 'operation',
        render: ({ rowIdx }: any) => {
          return intervalsRef.current.length > 2 &&
            rowIdx < intervalsRef.current.length - 1 ? (
            <div className={`${preCls}__del-icon`}>
              <i
                className='tu-icon-del'
                onClick={() => {
                  deleteInterval(rowIdx)
                }}
              ></i>
            </div>
          ) : (
            <></>
          )
        },
        width: 56,
      }

      return canChooseColor
        ? [
            interval,
            color,
            lowerLimit,
            lowerOperationType,
            scoreText,
            upperOperationType,
            upperLimit,
            operation,
          ]
        : [
            interval,
            lowerLimit,
            lowerOperationType,
            scoreText,
            upperOperationType,
            upperLimit,
            operation,
          ]
    }, [canChooseColor])

    return (
      <div className={classNames(preCls, className)} style={style}>
        <div>
          <SuperTable
            theme='minimalist'
            pinnedHeader
            columns={renderColumns}
            height={'100%'}
            disableResize={true}
            dataSource={intervalsRef.current}
            rowKey={'uniqueId'}
          />
          <span className={`${preCls}__add-interval`} onClick={createInterval}>
            + 添加区间
          </span>
        </div>
      </div>
    )
  })
)

export default IntervalSelection
