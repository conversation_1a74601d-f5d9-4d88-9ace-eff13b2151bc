import { getRandomStr } from "@tita/utils";
import { IIntervalItemProps } from ".";

const defaultColors = ['#588DFF', '#8663FF', '#29D5FF', '#43E7B4', '#CBE600', '#FFCF00'];

const operationTypeList = [
  { label: '≤', value: '<=' },
  { label: '<', value: '<' }
]

const getInitIntervalList = (openDefaultInterval: boolean, initIntervalList: Array<IIntervalItemProps>, intervalStart: number, intervalEnd: number, step: number) => {
  if (!openDefaultInterval || initIntervalList.length) {
    const list = initIntervalList.map(item => {
      return {
        uniqueId: getRandomStr(),
        ...item
      }
    })
    return list;
  }
  const len = intervalEnd / step;

  const res = [];
  for (let i = 0; i < len; i++) {
    const colorInd = i % defaultColors.length
    const color = defaultColors[colorInd]
    res.push({
      uniqueId: getRandomStr(),
      color,
      lowerLimit: i === 0 ? 0 : step * i,
      upperLimit: step * (i + 1),
      lowerOperationType: '<',
      upperOperationType: '<=',
    })
  }

  return res;
}

export { defaultColors, operationTypeList, getInitIntervalList };