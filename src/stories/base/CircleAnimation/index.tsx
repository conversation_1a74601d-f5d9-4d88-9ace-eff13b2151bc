import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'

export interface ICircleAnimationProps {
  /**
   * 圆环宽度
   */
  width: number
  /**
   * 圆环高度
   */
  height: number
  borderColor?: string
  borderWidth?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-circle-animation'

export const CircleAnimation: FC<ICircleAnimationProps> = React.memo(({ width, height, borderColor = 'black', borderWidth = 1, children, className, style }) => {
  const cx = width / 2; // 椭圆中心点 x 坐标
  const cy = height / 2; // 椭圆中心点 y 坐标
  const rx = width / 2; // 椭圆的长半轴
  const ry = height / 2; // 椭圆的短半轴
  const pathData = `M ${cx - rx} ${cy} A ${rx} ${ry} 0 1 0 ${cx + rx} ${cy} A ${rx} ${ry} 0 1 0 ${cx - rx} ${cy}`;
  return (
    <div className={classNames(preCls, className)} style={{ ...style, width, height }}>
      <svg className={`${preCls}__ellipse`} width={width} height={height}>
        {/* 使用 path 标签绘制椭圆 */}
        <path d={pathData} fill="transparent" stroke={borderColor} strokeWidth={borderWidth} />
      </svg>
      <div className={`${preCls}__ball`} style={{ offsetPath: `path('${pathData}')` }}>{children}</div>
    </div>
  )
})

export default CircleAnimation
