import React, { <PERSON> } from 'react'
import CircleAnimation from '@/stories/base/CircleAnimation'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({ }) => {
  return (
    <CircleAnimation width={500} height={200} style={{ transform: 'rotate(-20deg)' }}>
      <div style={{ width: 50, height: 50, backgroundColor: 'red' }}></div>
    </CircleAnimation>
  )
}

export default React.memo(Base)
