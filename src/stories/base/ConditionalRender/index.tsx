import React, { FC, useState } from 'react'
import classNames from 'classnames'
import './index.scss'
import { ConditionalRenderContext } from './context'
import { useUpdateEffect } from 'ahooks'

export * from './item'

export interface IConditionalRenderProps {
  value: string | number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-conditional-render'

export const ConditionalRender: FC<IConditionalRenderProps> = React.memo(({ value, children, className, style }) => {
  const [currentValue, setcurrentValue] = useState(value)
  useUpdateEffect(() => {
    setcurrentValue(value)
  }, [value])
  return (
    <ConditionalRenderContext.Provider value={{
      currentValue
    }}>
      <div className={classNames(preCls, className)} style={style}>
        {children}
      </div>
    </ConditionalRenderContext.Provider>
  )
})

export default ConditionalRender
