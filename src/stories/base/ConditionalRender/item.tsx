import React, { FC, useContext } from 'react'
import classNames from 'classnames'
import { ConditionalRenderContext } from './context'
import './item.scss'

export interface IConditionalRenderItemProps {
  value: string | number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}
const preCls = 'tita-ui-conditional-render-item'

export const ConditionalRenderItem: FC<IConditionalRenderItemProps> = React.memo(({ value, children, className, style }) => {
  const { currentValue } = useContext(ConditionalRenderContext)
  if (currentValue !== value) return null
  return (
    <div className={classNames(preCls, className, {
      [`${preCls}--show`]: currentValue === value
    })} style={style}>
      {children}
    </div>
  )
})

export default ConditionalRenderItem
