import React, { <PERSON> } from 'react'
import ListContainer from '@/stories/base/ListContainer'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  return (
    <ListContainer itemSelecter=".list-container-item">
      {new Array(10).fill(0).map((_, i1) => (
        <div
          className='flex border border-gray-100 h-9 shadow-md mr-10px'
          key={i1}
        >
          {new Array(3).fill(0).map((_, i2) => (
            <div
              className='list-container-item border-r border-gray-100 h-9'
              style={{ width: parseInt(Math.random() * 100 + 50 + 'px') }}
              key={i2}
            >
              {i1}-{i2}
            </div>
          ))}
        </div>
      ))}
    </ListContainer>
  )
}

export default React.memo(Base)
