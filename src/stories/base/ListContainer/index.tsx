import React, {
  FC,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useSpring, animated } from '@react-spring/web'
import classNames from 'classnames'
import './index.scss'
import InnerShadow from '../InnerShadow'
import { useSize } from 'ahooks'
import { useRefState } from '@tita/hooks'

export interface IListContainerProps {
  disableBtn?: boolean
  disableShadow?: boolean
  itemSelecter?: string | (() => Element[])
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  containerStyle?: React.CSSProperties
}

export interface IListContainerRef {
  next: () => void
  back: () => void
  recalculate: () => void
}

const preCls = 'tita-ui-list-container'

export const ListContainer = React.memo(
  forwardRef<IListContainerRef, IListContainerProps>(
    (
      {
        disableBtn,
        disableShadow,
        itemSelecter,
        children,
        className,
        style,
        containerStyle,
      },
      ref
    ) => {
      const contentRef = useRef() as React.MutableRefObject<HTMLDivElement>
      const contentRefSize = useSize(contentRef)
      const contentWidth = (contentRefSize && contentRefSize.width) || 0

      const [contentRect, setContentRect] = useState<DOMRect>()
      const [itemRect, setItemRect] = useState<DOMRect[]>([])
      const [_, itemDomRef, setItemDom] = useRefState<Element[]>([])

      const [currentX, setCurrentX] = useState(0)
      const [{ x }, api] = useSpring(() => ({ x: 0 }))
      // x 偏移缓存，用来做回退
      const [xCache, xCacheRef, setXCache] = useRefState<number[]>([])

      function calculateItemRect() {
        if (!itemSelecter) return

        const items =
          typeof itemSelecter === 'function'
            ? itemSelecter()
            : Array.from(document.querySelectorAll(itemSelecter))

        setItemDom(items)
        itemDomRef.current = items

        setItemRect(
          // @ts-ignore
          itemDomRef.current.map((item) => item.getBoundingClientRect()) || []
        )
      }

      useEffect(() => calculateItemRect(), [itemSelecter])

      useEffect(() => {
        if (!contentWidth) return
        setContentRect(contentRef.current.getBoundingClientRect())
      }, [contentWidth])

      const hasNext = useMemo(() => {
        if (!contentRect) return false
        // 有超出容器右侧的元素，就视为有下一页
        return itemRect.some(
          (item) =>
            item.x + item.width + currentX > contentRect.x + contentRect.width
        )
      }, [currentX, contentWidth, itemRect, contentRect])

      const toNext = () => {
        if (!contentRect) return false
        const length = itemRect.length
        // 找到下一页的第一个元素
        const fn = (item: any) => {
          const itemLeft = item.x + x.get()
          const right = contentRect.x + contentRect.width
          if (itemLeft < right && itemLeft + item.width > right) return true
          if (itemLeft > right) return true
        }
        const item = itemRect.find(fn)
        const curIndex = itemRect.findIndex(fn)

        if (!item) return

        let newX

        // 剩余的元素要移动的距离
        const restWidth = itemRect
          .slice(curIndex)
          .reduce((acc, cur) => acc + cur.width, 0)
        if (restWidth < contentWidth) {
          // 只偏移剩余元素的长度
          newX = -(
            itemRect[length - 1].x +
            itemRect[length - 1].width -
            contentRect.x -
            contentRect.width
          )
        } else {
          newX = -(item.x - contentRect.x)
        }

        xCacheRef.current.push(x.get())
        setXCache([...xCacheRef.current])
        setCurrentX(newX)

        api.start({ x: newX })
      }

      const toPre = () => {
        xCacheRef.current.pop() || 0
        if (!contentRect) return false
        // 找到下一页的第一个元素
        const fn = (item: any) => {
          const itemLeft = item.x + x.get()
          const right = contentRect.x // + contentRect.width
          // if (itemLeft < right && itemLeft + item.width > right) return true
          if (itemLeft < right) return true
        }
        // @ts-ignore
        const item = itemRect.findLast(fn)
        // @ts-ignore
        const curIndex = itemRect.findLastIndex(fn)

        if (!item) return

        let newX

        // 剩余的元素要移动的距离
        const restWidth = itemRect
          .slice(0, curIndex)
          .reduce((acc, cur) => acc + cur.width, 0)
        if (restWidth < contentWidth) {
          // 只偏移剩余元素的长度
          newX = 0
        } else {
          newX = -(item.x - item.width - contentRect.width)
        }
        setXCache([...xCacheRef.current])

        setCurrentX(newX)
        api.start({ x: newX })
      }

      useImperativeHandle(ref, () => ({
        next: toNext,
        back: toPre,
        recalculate: () => {
          calculateItemRect()
        },
      }))

      return (
        <div className={classNames(preCls, className)} style={style}>
          {!disableBtn && (
            <div
              className={classNames(`${preCls}__left-btn`, {
                [`${preCls}__show`]: !!xCache.length,
              })}
              onClick={toPre}
            >
              <i className='tu-icon-left' />
            </div>
          )}
          {!disableBtn && (
            <div
              className={classNames(`${preCls}__right-btn`, {
                [`${preCls}__show`]: hasNext,
              })}
              onClick={toNext}
            >
              <i className='tu-icon-APP-xi' />
            </div>
          )}

          {!disableShadow && (
            <InnerShadow left={!!xCache.length} right={hasNext} />
          )}

          <div ref={contentRef} className={`${preCls}__content`}>
            <animated.div
              style={{ x, ...containerStyle }}
              className={`${preCls}__children-container`}
            >
              {children}
            </animated.div>
          </div>
        </div>
      )
    }
  )
)

export default ListContainer
