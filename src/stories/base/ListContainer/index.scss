.tita-ui-list-container {
  position: relative;

  &__content {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  &__children-container {
    display: flex;
    width: max-content;
    min-width: 100%;
    height: 100%;
  }
  &:hover &__show {
    transform: translateY(-50%) scale(1) !important;
    opacity: 1 !important;
    pointer-events: inherit !important;
  }
  
  &__left-btn,
  &__right-btn {
    position: absolute;
    z-index: 20;
    top: 50%;
    width: 36px;
    height: 36px;
    background: #ffffff;
    box-shadow: 0px 8px 16px 0px rgba(127, 145, 180, 0.3);
    border-radius: 24px;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transform: translateY(-50%) scale(.3);
    opacity: 0;
    transition: opacity .3s, transform .2s;
    pointer-events: none;
  }
  &__left-btn {
    left: -18px;
  }
  &__right-btn{
    right: -18px;
  }
}