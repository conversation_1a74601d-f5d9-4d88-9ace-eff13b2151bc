import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'
import { FormItemProps, useFormItemValue } from '@/hooks/useFormItemValue'

export interface IColorListSelecterProps extends FormItemProps<string> {
  /**
   * 可选颜色
   * @editData ["#588DFF","#8663FF","#00BD16","#FF8827","#FF6953","#B2BED4"]
   * @editType json
   */
  colors?: string[]
  /**
   * 自定义渲染
   */
  renderItem?: (props: {
    color: string
    selected: boolean
    onChange: (color: string) => void
  }) => React.ReactNode
  disabled?: boolean
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-color-list-selecter'

export const ColorListSelecter: FC<IColorListSelecterProps> = React.memo(
  ({ colors, renderItem, className, style, ...other }) => {
    const formValue = useFormItemValue(other)
    return (
      <div
        className={classNames(preCls, className, {
          [`${preCls}--disabled`]: other.disabled,
        })}
        style={style}
      >
        {colors?.map((color) => {
          const selected = color === formValue.value

          if (renderItem) {
            return renderItem({
              color,
              selected,
              onChange: (color) => {
                formValue.set(color)
              },
            })
          }

          return (
            <div
              key={color}
              className={classNames(`${preCls}-item`, {
                [`${preCls}-item-selected`]: selected,
              })}
              onClick={() => {
                formValue.set(color)
              }}
              style={{
                backgroundColor: color,
                boxShadow: selected
                  ? `0 0 0 2px #fff, 0 0 0 3px ${color}`
                  : 'none',
              }}
            ></div>
          )
        })}
      </div>
    )
  }
)

export default ColorListSelecter
