/**
 * 已知两直角边长，求斜边长
 * @param edge1 边长1
 * @param edge2 边长2
 */
export const getHypotenuseWidth = (edge1: number, edge2: number) => Math.sqrt(edge1 * edge1 + edge2 * edge2);

/**
 * 已知斜边长和角度，求直角边长
 * @param long 斜边长
 * @param angle 角度
 * @returns 
 */
export function getRightAngleEdge(long: number, angle: number){
  //获得弧度
  var radian = 2 * Math.PI / 360 * angle;
  return {
    y: Math.sin(radian) * long,
    x: Math.cos(radian) * long
  };
}