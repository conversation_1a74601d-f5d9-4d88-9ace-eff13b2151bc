import React, {
  FC,
  useEffect,
  MutableRefObject,
  forwardRef,
  useImperativeHandle,
} from 'react'
import classNames from 'classnames'
import dayjs from 'dayjs'
import $ from 'jquery'
import Head from './components/Head'
import { useSize } from 'ahooks'
import { useRef } from 'react'
import Works from './components/Works'
import { GanttContext, GanttContextValue } from './context'
import {
  LinkContext,
  TaskKey,
  ViewMode,
  Task,
  DefaultTask,
  TaskTime,
} from './types'
import { useState } from 'react'
import { getDefaultFirstDate, getSpaceDays } from './utils/date'
import { useDrag } from '@use-gesture/react'
import { defaultColumnWidth } from './constant'
import { getAngle } from './utils/math'
import Link from './components/Link'
import Scroll, { ITitaScrollRef } from '../TitaScroll'
import { useMemo } from 'react'
import { getHypotenuseWidth, getRightAngleEdge } from './utils/triangle'
import { arr2Dic } from '@/libs/array'
import { useCallback } from 'react'
import { createPrecedingTasks } from './utils'
import './index.scss'
import HeadBg from './components/HeadBg'
import { getLocale } from '@tita/utils'
import Loading from '../Loading'
import Virtual from '@/stories/base/Virtual'
import WorkRender from './components/Works'
import List from '@/stories/base/Virtual/list'
import isNil from 'lodash/isNil'

export * from './types'

export type GanttViewMode = ViewMode

export interface IGanttProps {
  /**
   * 甘特图开始时间
   * @editData 2023-01-01
   */
  startTime?: TaskTime
  /**
   * 甘特图结束时间
   * @editData 2023-01-30
   */
  endTime?: TaskTime
  /**
   * 展示模式
   * @editData day
   */
  viewMode?: ViewMode
  /**
   * 任务数据
   * @editType json
   */
  tasks?: Task[]
  todayTooltip?: any
  /**
   * 任务数据
   * @editType json
   * @editData [{"value":"0","isMilepost":true,"startTime":"2023-01-01","endTime":"2023-01-31","panelData":{"label":"这是一个里程碑"}},{"value":"0-1","milepostKey":"0","startTime":"2023-01-01","endTime":"2023-01-05","progress":60,"panelData":{"head":"https://ts1.cn.mm.bing.net/th/id/R-C.********************************?rik=NPLwVdDs1R0yZw&riu=http%3a%2f%2fimg.crcz.com%2fallimg%2f202003%2f11%2f1583903640898589.jpg&ehk=3Z0G6rXkUb%2faHrH5FLPrOf%2biRUg4vqAWoKSYNKQrhUw%3d&risl=&pid=ImgRaw&r=0&sres=1&sresct=1","label":"任务1"}},{"value":"0-2","milepostKey":"0","startTime":"2023-01-06","endTime":"2023-01-12","progress":80,"panelData":{"head":"https://ts1.cn.mm.bing.net/th/id/R-C.********************************?rik=NPLwVdDs1R0yZw&riu=http%3a%2f%2fimg.crcz.com%2fallimg%2f202003%2f11%2f1583903640898589.jpg&ehk=3Z0G6rXkUb%2faHrH5FLPrOf%2biRUg4vqAWoKSYNKQrhUw%3d&risl=&pid=ImgRaw&r=0&sres=1&sresct=1","label":"任务2"}},{"value":"0-3","milepostKey":"0","startTime":"2023-01-15","endTime":"2023-01-30","progress":20,"panelData":{"head":"https://ts1.cn.mm.bing.net/th/id/R-C.********************************?rik=NPLwVdDs1R0yZw&riu=http%3a%2f%2fimg.crcz.com%2fallimg%2f202003%2f11%2f1583903640898589.jpg&ehk=3Z0G6rXkUb%2faHrH5FLPrOf%2biRUg4vqAWoKSYNKQrhUw%3d&risl=&pid=ImgRaw&r=0&sres=1&sresct=1","label":"任务3"}}]
   */
  defaultTasks?: Task[]
  render?: (
    data: DefaultTask['panelData'],
    task?: Task,
    index?: number
  ) => React.ReactNode
  /**
   * 初始位置
   */
  initialPosition?: 'today'

  container?: React.FC<Task>
  onScroll?: React.UIEventHandler<HTMLDivElement>
  confirmAutoOffset?: (
    precedingTaskKey: TaskKey,
    latterTaskKey: TaskKey
  ) => Promise<boolean>
  onDoubleClick?: (taskKey: TaskKey, type: number) => void
  onRemoveLink?: (currentTaskKey: TaskKey, latterTaskKey: TaskKey) => void
  onCreateLink?: (precedingTaskKey: TaskKey, latterTaskKey: TaskKey) => void
  onChangeTaskDate?: (
    taskKey: TaskKey,
    type: 'left' | 'right',
    startTime?: TaskTime,
    endTime?: TaskTime
  ) => void
  onMoveTask?: (
    taskKey: TaskKey,
    day: number,
    startTime: TaskTime,
    endTime: TaskTime
  ) => void
  onChangeTaskProgress?: (taskKey: TaskKey, progress: number) => void

  children?: React.ReactNode
  className?: string
  /**
   * style
   * @editType json
   */
  style?: React.CSSProperties
  readonly?: boolean
}

export interface GanttRef {
  scrollToDate: (date: TaskTime, offsetX?: number) => void
}

const preCls = 'tita-ui-gantt'

const getStartTime = (ganttStartTime: TaskTime, viewMode: ViewMode) => {
  if (viewMode === 'month')
    return dayjs(`${dayjs(ganttStartTime).subtract(1, 'year').year()}-01-01`)
  const monthStart = `${dayjs(ganttStartTime)
    .subtract(1, 'month')
    .format('YYYY-MM')}-01`
  return dayjs(monthStart)
}
const getScrollToDateLeft = (
  targetDate: dayjs.Dayjs,
  startTime: dayjs.Dayjs,
  columnWidth: number
) => {
  return dayjs(targetDate).diff(startTime, 'day') * columnWidth
}

export const Gantt = forwardRef<GanttRef>(
  (
    {
      startTime: ganttStartTime,
      endTime: ganttEndTime,
      tasks,
      defaultTasks,
      viewMode = 'day',
      render,
      todayTooltip,
      container,
      onScroll,
      confirmAutoOffset,
      onDoubleClick,
      onRemoveLink,
      onCreateLink,
      onChangeTaskDate,
      onMoveTask,
      onChangeTaskProgress,
      className,
      style,
      readonly = false,
    }: IGanttProps,
    ref
  ) => {
    const [_tasks, setTasks] = useState(
      createPrecedingTasks((defaultTasks || tasks) as Task[])
    )
    useEffect(() => {
      if (tasks) setTasks(createPrecedingTasks(tasks))
    }, [tasks])

    const columnWidth = defaultColumnWidth[viewMode]
    const containerRef = useRef() as MutableRefObject<HTMLDivElement>
    const scrollRef = useRef() as MutableRefObject<ITitaScrollRef>
    const scrollContentRef = useRef() as MutableRefObject<HTMLDivElement>
    const { width: containerWidth, height: containerHeight } = useSize(
      containerRef
    ) || { width: 0, height: 0 }
    const { width: scrollContentWidth, height: scrollContentHeight } = useSize(
      scrollContentRef
    ) || { width: 0, height: 0 }

    const containerRectRef = useRef() as MutableRefObject<DOMRect>

    useImperativeHandle(ref, () => ({
      scrollToDate: (date: TaskTime, offsetX?: number) => {
        const left =
          getScrollToDateLeft(dayjs(date), startTime, columnWidth) -
          (offsetX || containerWidth / 4)
        if (scrollLeft === left) {
          setScrollLeft(left + 0.001)
          scrollRef.current.scrollLeft(left + 0.001)
          return
        }
        scrollRef.current.scrollLeft(left)
        setScrollLeft(left)
      },
      scrollYTo: (y: number) => {
        scrollRef.current.getScrollRef().scrollTop = y
      },
    }))

    const [startTime, setStartTime] = useState(
      ganttStartTime
        ? getStartTime(ganttStartTime, viewMode)
        : getDefaultFirstDate(viewMode)
    )
    useEffect(() => {
      setStartTime(
        ganttStartTime
          ? getStartTime(ganttStartTime, viewMode)
          : getDefaultFirstDate(viewMode)
      )
    }, [viewMode, ganttStartTime])

    const [endTime, setEndTime] = useState(
      ganttEndTime
        ? dayjs(ganttEndTime).add(1, 'month')
        : getDefaultFirstDate(viewMode)
    )
    useEffect(() => {
      setEndTime(
        ganttEndTime
          ? dayjs(ganttEndTime).add(1, 'month')
          : getDefaultFirstDate(viewMode)
      )
    }, [viewMode, ganttEndTime])

    // 首次加载时，需要将滚动条定位到当前日期
    // 后面能从外部触发定位
    const [scrollLeft, setScrollLeft] = useState(-1)
    const timerRef = useRef<any>()
    useEffect(() => {
      if (scrollLeft === -1 && startTime && containerWidth) {
        if (timerRef.current) clearTimeout(timerRef.current)
        timerRef.current = setTimeout(() => {
          const left =
            getScrollToDateLeft(dayjs(), startTime, columnWidth) -
            containerWidth / 2
          if (left <= 0 && !ready) setReady(true)
          setScrollLeft(left)
        }, 200)
      }
    }, [startTime, containerWidth])

    const toDayRef = useRef(false)
    const [ready, setReady] = useState(true)

    useEffect(() => {
      if (!scrollRef.current || toDayRef.current || scrollLeft <= 0) return
      toDayRef.current = true
      scrollRef.current.scrollLeft(scrollLeft)
      setReady(true)
    }, [scrollLeft, scrollRef.current])

    const [downLink, setDownLink] = useState(false)
    const [angle, setAngle] = useState(0)
    const [width, setWdith] = useState(0)
    const linkContextRef = useRef<LinkContext>(
      {}
    ) as React.MutableRefObject<LinkContext>

    const isScrollingRef = useRef(false)
    const scrollSpeed = 5

    const dropScrollStartRef = useRef({ x: 0, y: 0 })

    function startScrolling(direction: 'left' | 'right' | 'top' | 'bottom') {
      if (!isScrollingRef.current) {
        isScrollingRef.current = true
        scrollContent(direction)
      }
    }

    function stopScrolling() {
      isScrollingRef.current = false
    }

    function scrollContent(direction: 'left' | 'right' | 'top' | 'bottom') {
      if (!isScrollingRef.current) return

      if (direction === 'right') {
        scrollRef.current.scrollLeft((left) => left + scrollSpeed, {
          disableAnimate: true,
        })
      } else if (direction === 'left') {
        scrollRef.current.scrollLeft((left) => left - scrollSpeed, {
          disableAnimate: true,
        })
      } else if (direction === 'top') {
        scrollRef.current.scrollTop((top) => top - scrollSpeed, {
          disableAnimate: true,
        })
      } else if (direction === 'bottom') {
        scrollRef.current.scrollTop((top) => top + scrollSpeed, {
          disableAnimate: true,
        })
      }

      requestAnimationFrame(() => scrollContent(direction))
    }

    const scrollDomRef = useRef<HTMLDivElement>()

    const dragBind = useDrag((state) => {
      const {
        movement: [mx, my],
        last,
        values,
        first,
      } = state
      if (first) {
        containerRectRef.current = containerRef.current.getBoundingClientRect()

        const scroll = scrollRef.current.getScrollRef()
        scrollDomRef.current = scroll
        dropScrollStartRef.current = {
          x: scroll.scrollLeft,
          y: scroll.scrollTop,
        }
      }

      if (!downLink) return

      const offsetScrollX =
        // @ts-ignore
        scrollDomRef.current.scrollLeft - dropScrollStartRef.current.x
      const offsetScrollY =
        // @ts-ignore
        scrollDomRef.current.scrollTop - dropScrollStartRef.current.y

      setWdith(getHypotenuseWidth(mx + offsetScrollX, my + offsetScrollY))
      setAngle(getAngle(mx + offsetScrollX, my + offsetScrollY, 0, 0))

      const mouseX = values[0]
      const mouseY = values[1]

      // 检测鼠标是否接近容器右边缘
      if (mouseX > containerRectRef.current.right - 20) {
        startScrolling('right')
      }
      // 检测鼠标是否接近容器左边缘
      else if (mouseX < containerRectRef.current.left + 20) {
        startScrolling('left')
      }
      // 检测鼠标是否接近容器上边缘
      else if (mouseY < containerRectRef.current.top + 72) {
        startScrolling('top')
      }
      // 检测鼠标是否接近容器下边缘
      else if (mouseY > containerRectRef.current.bottom - 20) {
        startScrolling('bottom')
      }
      // 如果鼠标不在容器的边缘，停止滚动
      else {
        stopScrolling()
      }

      if (last) {
        isScrollingRef.current = false
        const type = linkContextRef.current.currentLinkType
        // @ts-ignore
        const index = currentDragTaskInfo.taskIndex
        const { startTime: taskStartTime, endTime: taskEndTime } =
          // @ts-ignore
          currentDragTaskInfo.task
        // 计算任务距离开始位置的天数
        const left =
          type === 'left'
            ? getSpaceDays(startTime, taskStartTime) * columnWidth - 12
            : getSpaceDays(startTime, taskEndTime, false) * columnWidth + 12

        // 计算移动的 x 和 y 的距离
        const { x, y } = getRightAngleEdge(width, angle)
        const moveToX = left + x
        const toTop = y < 0
        const moveNum = Math.ceil((Math.abs(y) - 17) / 34)
        if (moveNum <= 0) return

        const moveToIndex = index + (toTop ? -moveNum : moveNum)
        const moveToTask = _tasks[moveToIndex]
        const moveToTaskLeft =
          getSpaceDays(startTime, moveToTask.startTime) * columnWidth
        const moveToTaskWidth =
          getSpaceDays(moveToTask.startTime, moveToTask.endTime, false) *
          columnWidth
        const moveToTaskRight =
          moveToTaskLeft + (moveToTaskWidth <= 90 ? 90 : moveToTaskWidth)
        if (
          moveToX >= moveToTaskLeft &&
          moveToX <= moveToTaskRight &&
          // @ts-ignore
          !moveToTask.isMilepost
        ) {
          if (type === 'left')
            onCreateLinkHandler(
              moveToTask.value,
              // @ts-ignore
              currentDragTaskInfo.task.value
            )
          else
            onCreateLinkHandler(
              // @ts-ignore
              currentDragTaskInfo.task.value,
              moveToTask.value
            )
        }
      }
    })

    const currentDragTaskInfo = useMemo(() => {
      if (!downLink) {
        setWdith(0)
        setAngle(0)
        return
      }
      const taskIndex = _tasks.findIndex(
        ({ value }) => linkContextRef.current.currentKey === value
      )
      return { taskIndex, task: _tasks[taskIndex] }
    }, [downLink, linkContextRef.current])

    const tasksDic = useMemo(() => {
      return arr2Dic(_tasks, 'value', { withIndex: true }) as Record<
        TaskKey,
        Task & { _index: number }
      >
    }, [_tasks])

    const onMouseOverHandler = () => {
      if (downLink) {
        setTimeout(() => setDownLink(false), 200)
      }
    }

    useEffect(() => {
      const mouseUp = () => {
        setDownLink(false)
        setWdith(0)
        setAngle(0)
      }
      document.addEventListener('mouseup', mouseUp)
      return () => {
        document.removeEventListener('mouseup', mouseUp)
      }
    }, [])

    // 删除关联
    const onRemoveLinkHandler = useCallback(
      (currentTaskKey: TaskKey, latterTaskKey: TaskKey) => {
        const currentTask = tasksDic[currentTaskKey]
        const latterTask = tasksDic[latterTaskKey]

        // @ts-ignore
        currentTask.latterTasks = currentTask.latterTasks.filter(
          // @ts-ignore
          (key) => key !== latterTaskKey
        )
        _tasks[currentTask._index] = { ...currentTask }

        // @ts-ignore
        latterTask.precedingTasks = latterTask.precedingTasks.filter(
          // @ts-ignore
          (key) => key !== currentTaskKey
        )
        _tasks[latterTask._index] = { ...latterTask }

        setTasks([..._tasks])

        if (onRemoveLink) onRemoveLink(currentTaskKey, latterTaskKey)
      },
      [onRemoveLink, _tasks, tasksDic]
    )

    // 创建关联
    const onCreateLinkHandler = useCallback(
      async (precedingTaskKey: TaskKey, latterTaskKey: TaskKey) => {
        const precedingTask = tasksDic[precedingTaskKey]
        const latterTask = tasksDic[latterTaskKey]

        if (precedingTask.cantLink || latterTask.cantLink) return

        // 判断是否可进行连接
        if (
          // @ts-ignore
          precedingTask.precedingTasks &&
          // @ts-ignore
          precedingTask.precedingTasks.includes(latterTaskKey)
        )
          return
        if (
          // @ts-ignore
          precedingTask.latterTasks &&
          // @ts-ignore
          precedingTask.latterTasks.includes(latterTaskKey)
        )
          return

        // 时间是否冲突
        const days = dayjs(precedingTask.endTime).diff(
          latterTask.startTime,
          'day'
        )

        // 是否允许自动调整时间
        let autoOffset = true
        if (confirmAutoOffset && days > 0) {
          autoOffset = await confirmAutoOffset(precedingTaskKey, latterTaskKey)
          if (!autoOffset) return
        }

        // 创建后置连接
        // @ts-ignore
        if (!precedingTask.latterTasks) precedingTask.latterTasks = []
        // @ts-ignore
        precedingTask.latterTasks.push(latterTaskKey)
        _tasks[precedingTask._index] = { ...precedingTask }

        // 创建前置连接
        // @ts-ignore
        if (!latterTask.precedingTasks) latterTask.precedingTasks = []
        // @ts-ignore
        latterTask.precedingTasks.push(precedingTaskKey)
        _tasks[latterTask._index] = { ...latterTask }
        loopLatter(precedingTaskKey, precedingTask.endTime)

        setTasks([..._tasks])

        if (onCreateLink) onCreateLink(precedingTaskKey, latterTaskKey)
      },
      [onCreateLink, _tasks, tasksDic]
    )

    // 如果时间冲突，则修改前后置任务时间
    // TODO: 自动调整前后置任务的时间
    const loopPre = (value: TaskKey, currentStartTime: TaskTime) => {
      const task = tasksDic[value]
      // @ts-ignore
      const precedingTasks: TaskKey[] = task.precedingTasks
      // 如果没有前置任务，需要再走一遍后置任务处理修复间隔
      if (!precedingTasks || !precedingTasks.length) return

      // 循环判断所有的前置任务与当前任务之间的天数是否小于移动的天数，如果小于，则移动差值的天数，然后继续 loop
      precedingTasks.forEach((value) => {
        const preTask = tasksDic[value]
        const diffDays =
          dayjs(dayjs(currentStartTime).format('YYYY/MM/DD')).diff(
            dayjs(preTask.endTime).format("YYYY/MM/DD"),
            'day'
          ) - 1;
        const separateDay =
          task.panelData.preTasks?.find(
            (item: any) => item.preTaskId === preTask.panelData.id
          )?.separateDay || 0
        // 如果小于 0，说明有交叉，需要将该任务也向前移动
        if (diffDays < 0 + separateDay) {
          if (preTask.autoOffset !== false && preTask.cantMove !== true) {
            // @ts-ignore
            // console.log(preTask.panelData.label, '向前移动', Math.abs(diffDays), '天');
            preTask.startTime = dayjs(preTask.startTime)
              .add(diffDays - separateDay, 'day')
              .format('YYYY-MM-DD HH:mm:ss')
            preTask.endTime = dayjs(preTask.endTime)
              .add(diffDays - separateDay, 'day')
              .format('YYYY-MM-DD HH:mm:ss')
            _tasks[preTask._index] = { ...preTask }
          } else {
            // @ts-ignore
            // console.log(preTask.panelData.label, '禁止移动', 'autoOffset', preTask.autoOffset, 'cantMove', preTask.cantMove);
          }

          loopPre(preTask.value, preTask.startTime)
        }
      })
    }
    const loopLatter = (value: TaskKey, currentEndTime: TaskTime) => {
      const task = tasksDic[value]
      // @ts-ignore
      const latterTasks: TaskKey[] = task.latterTasks
      if (!latterTasks || !latterTasks.length) return
      // 循环判断所有的前置任务与当前任务之间的天数是否小于移动的天数，如果小于，则移动差值的天数，然后继续 loop
      latterTasks.forEach((value) => {
        const latterTask = tasksDic[value]
        const separateDay =
          latterTask.panelData.preTasks?.find(
            (item: any) => item.preTaskId === task.panelData.id
          )?.separateDay
        const diffDays =
          dayjs(dayjs(currentEndTime).format('YYYY/MM/DD')).diff(
            dayjs(latterTask.startTime).format('YYYY/MM/DD'),
            'day'
          ) +
          1;
        // 是否设置过间隔
        const hasSeparateDay = !isNil(separateDay)
        // 如果小于 0，说明有交叉，需要将该任务也向前移动
        if (
          (!hasSeparateDay && diffDays > 0) ||
          (hasSeparateDay && diffDays + (separateDay || 0) != 0 )
        ) {
          if (latterTask.autoOffset !== false && latterTask.cantMove !== true) {
            // @ts-ignore
            // console.log(latterTask.panelData.label, '向后移动', Math.abs(diffDays), '天');
            latterTask.startTime = dayjs(latterTask.startTime)
              .add(diffDays + (separateDay || 0), 'day')
              .format('YYYY-MM-DD HH:mm:ss')
            latterTask.endTime = dayjs(latterTask.endTime)
              .add(diffDays + (separateDay || 0), 'day')
              .format('YYYY-MM-DD HH:mm:ss')
            // @ts-ignore
            if (latterTask.overdueDays) latterTask.overdueDays -= diffDays
            _tasks[latterTask._index] = { ...latterTask }
          } else {
            // @ts-ignore
            // console.log(latterTask.panelData.label, '禁止移动', 'autoOffset', latterTask.autoOffset, 'cantMove', latterTask.cantMove);
          }

          loopLatter(latterTask.value, latterTask.endTime)
        }
      })
    }

    // 更新任务时间
    const onChangeTaskDateHandler = useCallback<
      Required<GanttContextValue>['onChangeTaskDate']
    >(
      ({ value, days, type, startTime, endTime }) => {
        const task = tasksDic[value]
        // 修改自身的时间
        if (startTime) task.startTime = startTime
        if (endTime) task.endTime = endTime
        _tasks[task._index] = { ...task }

        switch (type) {
          // 修改前置任务
          case 'left':
            loopPre(value, startTime as TaskTime)
            break

          // 修改后置任务
          case 'right':
            // @ts-ignore
            if (_tasks[task._index].overdueDays)
              // @ts-ignore
              _tasks[task._index].overdueDays -= days
            loopLatter(value, endTime as TaskTime)
            break
        }

        setTasks([..._tasks])

        if (onChangeTaskDate)
          onChangeTaskDate(value, type, task.startTime, task.endTime)
      },
      [onChangeTaskDate, _tasks, tasksDic]
    )

    const onMoveTaskHandler = useCallback(
      (taskKey: TaskKey, day: number) => {
        const task = tasksDic[taskKey]
        task.startTime = dayjs(task.startTime)
          .add(day, 'd')
          .format('YYYY-MM-DD HH:mm:ss')
        task.endTime = dayjs(task.endTime)
          .add(day, 'd')
          .format('YYYY-MM-DD HH:mm:ss')
        // @ts-ignore
        if (task.overdueDays) task.overdueDays -= day
        _tasks[task._index] = { ...task }

        if (day < 0) loopPre(taskKey, task.startTime)
        else loopLatter(taskKey, task.endTime)

        setTasks([..._tasks])

        if (onMoveTask) onMoveTask(taskKey, day, task.startTime, task.endTime)
      },
      [onMoveTask, _tasks, tasksDic]
    )

    const onChangeTaskProgressHandler = useCallback(
      (taskKey: TaskKey, progress: number) => {
        const task = tasksDic[taskKey]

        // @ts-ignore
        task.progress = progress

        _tasks[task._index] = { ...task }
        setTasks([..._tasks])

        if (onChangeTaskProgress) onChangeTaskProgress(taskKey, progress)
      },
      [onChangeTaskProgress, _tasks, tasksDic]
    )

    const [showTopShadow, setShowTopShadow] = useState(false)
    const onScrollHandler: React.UIEventHandler<HTMLDivElement> = useCallback(
      (e) => {
        if (onScroll) onScroll(e)

        const { scrollTop } = e.target as HTMLDivElement
        setShowTopShadow(scrollTop > 0)
      },
      [onScroll]
    )
    
    const workRender = (data, index, ref, style) => {
      return <WorkRender key={data.data.value} task={data.data} style={style}></WorkRender>
    }

    return (
      <GanttContext.Provider
        value={{
          tasks: _tasks,
          tasksDic,

          render,

          viewMode,
          columnWidth,

          startTime,
          setStartTime,
          endTime,
          setEndTime,

          downLink,
          setDownLink,
          linkContextRef,

          containerWidth,
          containerHeight,
          scrollContentWidth,
          scrollContentHeight,

          container,
          onDoubleClick,
          onRemoveLink: onRemoveLinkHandler,
          onCreateLink: onCreateLinkHandler,
          onChangeTaskDate: onChangeTaskDateHandler,
          onMoveTask: onMoveTaskHandler,
          onChangeTaskProgress: onChangeTaskProgressHandler,
          readonly,
        }}
      >
        <div
          ref={containerRef}
          className={classNames(preCls, className, {
            [`${preCls}--showTopShadow`]: showTopShadow,
          })}
          style={style}
          {...dragBind()}
        >
          {_tasks.length === 0 && (
            <div className={`${preCls}__empty`}>
              {getLocale('Pro_Butt_Nodata')}
            </div>
          )}
          <Loading loading={!ready} />
          {/* <div
            className={`${preCls}__scroll-wrapper`}
          > */}
          <Virtual
            height={containerHeight}
            averageItemHeight={34}
            datas={_tasks}
            //@ts-ignore
            render={workRender}
            className={`${preCls}__scroll`}
            // style={style}
            disableTopShadow
            rowKey='value'
            barInset
            fixedSize
            ref={scrollRef}
            innerStyle={{ width: 'max-content' }}
            onMouseOver={onMouseOverHandler}
            onScroll={onScrollHandler}
          >
            <div className={`${preCls}__scroll-content`} ref={scrollContentRef}>
              <Head
                viewMode={viewMode}
                containerHeight={containerHeight}
                todayTooltip={todayTooltip}
              />
              <HeadBg
                viewMode={viewMode}
                containerHeight={scrollContentHeight}
                todayTooltip={todayTooltip}
              />
              <div
                className='tita-ui-gantt-works'
                style={{ height: _tasks.length * 34 }}
              >
                <List />
              </div>
              {/* @ts-ignore */}
              {downLink && currentDragTaskInfo && (
                <Link
                  index={currentDragTaskInfo && currentDragTaskInfo.taskIndex}
                  type={linkContextRef.current.currentLinkType}
                  startTime={
                    currentDragTaskInfo && currentDragTaskInfo.task.startTime
                  }
                  endTime={
                    currentDragTaskInfo && currentDragTaskInfo.task.endTime
                  }
                  angle={angle}
                  width={width}
                />
              )}
            </div>
          </Virtual>
          {/* </div> */}
          {/* <Scroll
            className={`${preCls}__scroll`}
            height='100%'
            barInset
            disableTopShadow
            ref={scrollRef}
            onMouseOver={onMouseOverHandler}
            onScroll={onScrollHandler}
          >
            <div className={`${preCls}__scroll-content`} ref={scrollContentRef}> */}
          {/* 头部区域 */}
          {/* <Head
                viewMode={viewMode}
                containerHeight={containerHeight}
                todayTooltip={todayTooltip}
              />
              <HeadBg
                viewMode={viewMode}
                containerHeight={scrollContentHeight}
                todayTooltip={todayTooltip}
              /> */}
          {/* 任务区域 */}
          {/* <Works tasks={_tasks} /> */}
          {/* @ts-ignore */}
          {/* {downLink && currentDragTaskInfo && (
                <Link
                  index={currentDragTaskInfo && currentDragTaskInfo.taskIndex}
                  type={linkContextRef.current.currentLinkType}
                  startTime={
                    currentDragTaskInfo && currentDragTaskInfo.task.startTime
                  }
                  endTime={
                    currentDragTaskInfo && currentDragTaskInfo.task.endTime
                  }
                  angle={angle}
                  width={width}
                />
              )} */}
          {/* </div> */}
          {/* </Scroll> */}
        </div>
      </GanttContext.Provider>
    )
  }
)

export default React.memo(Gantt)
