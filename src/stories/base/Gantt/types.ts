import dayjs from 'dayjs'
import React from 'react'

export type TaskKey = number | string

export interface BaseTask {
  value: TaskKey
  startTime: TaskTime
  endTime: TaskTime
  visible?: boolean
  disable?: boolean
  autoOffset?: boolean
  cantMove?: boolean
  cantLink?: boolean
  showOverdue?: boolean
  panelData: PanelData
}
export type PanelData = Record<string, any> & {
  head?: string
  label: string
  theme?: string
  progressTheme?: string
  tootip?: string | React.ReactNode
}

export interface MilepostTask extends BaseTask {
  /** 是否是里程碑 */
  isMilepost: true
}

export interface DefaultTask extends BaseTask {
  /** 所属里程碑的 key */
  milepostKey: TaskKey
  panelStyle?: React.CSSProperties
  progress?: number
  precedingTasks?: TaskKey[]
  latterTasks?: TaskKey[]
  className?: string
}

export type Task = DefaultTask | MilepostTask

export type TaskTime = string | number | dayjs.Dayjs | Date
export type ViewMode = 'month' | 'week' | 'day'


export interface MonthItem {
  year: number
  month: number
  days: number
  date: dayjs.Dayjs
}

export type LinkContext = {
  currentKey?: TaskKey
  currentLinkType?: 'left' | 'right'
  targetKey?: TaskKey
}