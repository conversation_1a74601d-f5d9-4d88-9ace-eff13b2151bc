import dayjs from 'dayjs'
import React, { FC, useCallback } from 'react'
import Gantt, { TaskKey, Task, IGanttProps, PanelData } from '../'

const headLink = 'https://ts1.cn.mm.bing.net/th/id/R-C.********************************?rik=NPLwVdDs1R0yZw&riu=http%3a%2f%2fimg.crcz.com%2fallimg%2f202003%2f11%2f1583903640898589.jpg&ehk=3Z0G6rXkUb%2faHrH5FLPrOf%2biRUg4vqAWoKSYNKQrhUw%3d&risl=&pid=ImgRaw&r=0&sres=1&sresct=1'

const currentDate = dayjs()
const currentMonth = dayjs().format('YYYY-MM')
const startTime = currentDate.format('YYYY-MM-01')
const endTime = currentDate.format('YYYY-MM-30')

const mockTask: Task[] = [
  {
    value: '0',
    isMilepost: true,
    startTime: startTime,
    endTime: endTime,
    panelData: {
      label: '这是一个里程碑'
    }
  },
  ...new Array(20).fill(0).map((_, index) => ({
    value: `0-${index}`,
    milepostKey: '0',
    startTime: currentMonth + '-' + (index + 1 + '').padStart(2, '0'),
    endTime: currentMonth + '-' + (index + 2 + '').padStart(2, '0'),
    progress: 60,
    panelData: {
      head: headLink,
      label: '任务' + index
    }
  })),
]

export interface IBaseProps {
  children?: React.ReactNode
  className?: string
  /**
   * style
   * @editType json
   * @editData {"height": "500px"}
   */
  style?: React.CSSProperties
}

const preCls = 'tita-ui-Base'

const Base: FC<IBaseProps> = () => {
  const onRemoveLinkHandler = useCallback((currentTaskKey: TaskKey, latterTaskKey: TaskKey) => {

  }, [])
  return (
    <div className="h-[300px]">
      <Gantt defaultTasks={mockTask} startTime={startTime} endTime={endTime} onRemoveLink={onRemoveLinkHandler} />
      {/* <Gantt tasks={mockTask} style={{ height: 200 }} /> */}
    </div>
  )
}

export default React.memo(Base)
