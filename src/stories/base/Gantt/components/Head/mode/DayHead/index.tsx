import React, { FC, useContext, useEffect } from 'react'
import classNames from 'classnames'
import { useState } from 'react'
import dayjs from 'dayjs'
import { getMonthData, isToday, isWeekEnd } from '../../../../utils/date'
import TadyLine from '../../../TodayLine'
import { GanttContext } from '../../../../context'
import { MonthItem } from '../../../../types'
import './index.scss'

export interface IGanttDayHeadProps {
  containerHeight: number
  todayTooltip?: any
  columnWidth?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const rootPreCls = 'tita-ui-gantt-head'
const preCls = 'tita-ui-gantt-day-head'

interface IHeadProps extends MonthItem {
  containerHeight: number
  columnWidth: number
  todayTooltip?: any
}

const Head: FC<IHeadProps> = ({ year, month, days, todayTooltip, columnWidth, containerHeight }) => {
  return (
    <div className={`${rootPreCls}__item`}>
      {/* year */}
      <div className={`${rootPreCls}__year-month`}>{year}/{month}</div>
      {/* days */}
      <div className={`${rootPreCls}__days`}>
        {new Array(days).fill(0).map((_, i) => {
          const weekEnd = isWeekEnd(year, month, i + 1)
          return (
            <div key={i} style={{ width: columnWidth }} className={classNames(`${rootPreCls}__days-item`, {
              [`${rootPreCls}__days-item--weekend`]: weekEnd
            })}>
              <span>{i + 1}</span>
              {isToday(year, month, i + 1) && <TadyLine height={containerHeight - 64} todayTooltip={todayTooltip} />}
            </div>
          )
        })}
      </div>
    </div>
  )
}

const GanttDayHead: FC<IGanttDayHeadProps> = ({ todayTooltip, containerHeight, columnWidth = 40, className, style }) => {
  const { startTime, endTime } = useContext(GanttContext)

  const [months, setMonth] = useState<MonthItem[]>(getMonthData(startTime, endTime))

  useEffect(() => {
    setMonth(getMonthData(startTime, endTime))
  }, [startTime, endTime])

  return (
    <div className={classNames(preCls, className)} style={style}>
      {months.map(item => <Head key={`${item.year}_${item.month}`} {...item} todayTooltip={todayTooltip} columnWidth={columnWidth} containerHeight={containerHeight} />)}
    </div>
  )
}

export default React.memo(GanttDayHead)
