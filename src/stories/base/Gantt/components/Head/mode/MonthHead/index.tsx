import React, { FC, useContext, useEffect, useRef } from 'react'
import classNames from 'classnames'
import { useState } from 'react'
import dayjs from 'dayjs'
import { useUnInitEffect } from '@/hooks/useUnInitEffect'
import { getMonthDayNum, getYearData, isCurrentMonth, isToday, isWeekEnd } from '../../../../utils/date'
import TadyLine from '../../../TodayLine'
import './index.scss'
import { GanttContext } from '@/stories/base/Gantt/context'

export interface IGanttMonthHeadProps {
  containerHeight: number
  todayTooltip?: any
  columnWidth?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const rootPreCls = 'tita-ui-gantt-head'
const preCls = 'tita-ui-gantt-month-head'

export interface YearMonthItem {
  year: number
  monthDays: number[]
}

const currentYear = dayjs().year()
const currentMonth = dayjs().month() + 1
const currentDateNum = dayjs().date()

/** 创建某个月的日数据 */
const createYearMonthDays = (year: number) => {
  const monthDays = new Array(12).fill(0).map((_, i) => {
    const month = i + 1
    const days = getMonthDayNum(year, month)
    return days
  })
  
  return { year, monthDays }
}

const currentDate = dayjs(`${currentYear}-${currentMonth}-01`)
const firstDate = currentDate.subtract(1, 'M')
const lastDate = currentDate.add(1, 'M')

const defaultMonthData = [
  createYearMonthDays(currentYear - 1),
  createYearMonthDays(currentYear),
  createYearMonthDays(currentYear + 1),
]

interface IHeadProps extends YearMonthItem {
  containerHeight: number
  columnWidth: number
  todayTooltip?: any
}

const Head: FC<IHeadProps> = ({ year, monthDays, columnWidth, todayTooltip, containerHeight }) => {
  return (
    <div className={`${rootPreCls}__item`}>
      {/* year */}
      <div className={`${rootPreCls}__year-month`}>{year}</div>
      {/* days */}
      <div className={`${rootPreCls}__days`}>
        {monthDays.map((days, i) => {
          // 是不是最后一天
          const monthLastDay = isToday(year, i + 1, days, `${year}-12-${days}`)
          return (
            <div key={i} style={{ width: days * columnWidth }} className={classNames(`${rootPreCls}__days-item`)}>
              <span>{i + 1} 月</span>
              {monthLastDay && <div className={`${rootPreCls}__last-day`} style={{ height: containerHeight - 64 }} />}
              {isCurrentMonth(year, i + 1) && <TadyLine todayTooltip={todayTooltip} autoCenter={false} height={containerHeight - 64} style={{ left: currentDateNum * columnWidth - columnWidth / 2 }} />}
            </div>
          )
        })}
      </div>
    </div>
  )
}

const GanttMonthHead: FC<IGanttMonthHeadProps> = ({ todayTooltip, containerHeight, columnWidth = 4, className, style }) => {
  const { startTime, endTime } = useContext(GanttContext)
  const [years, setYears] = useState<YearMonthItem[]>(getYearData(startTime, endTime))
  
  useEffect(() => {
    setYears(getYearData(startTime, endTime))
  }, [startTime, endTime])

  return (
    <div className={classNames(preCls, className)} style={style}>
      {years.map(item => <Head key={item.year} {...item} todayTooltip={todayTooltip} columnWidth={columnWidth} containerHeight={containerHeight} />)}
    </div>
  )
}

export default React.memo(GanttMonthHead)
