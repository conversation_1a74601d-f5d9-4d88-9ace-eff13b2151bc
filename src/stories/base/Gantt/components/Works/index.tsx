import React, { FC, useContext, useRef, useEffect, useState } from 'react'
import classNames from 'classnames'
import TaskPanel from '../TaskPanel'
import MilepostLine from '../MilepostLine'
import { Task } from '../../types'
import AutoPosition from '../AutoPosition'
import { GanttContext } from '../../context'
import './index.scss'

export interface IGanttWorksProps {
  task: Task
  // children?: React.ReactNode
  // className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-gantt-works'

const GanttWorks: FC<IGanttWorksProps> = ({ task, style }) => {
  const { readonly } = useContext(GanttContext)
  return (
    <div
      key={task.value}
      style={style}
      className={classNames(`${preCls}__task-line`)}
    >
      {!task.stastartTime && !task.endTime ? null : task.isMilepost ? (
        <AutoPosition
          value={task.value}
          disable={task.disable || readonly}
          startTime={task.startTime}
        >
          <MilepostLine
            panelData={task.panelData}
            panelStyle={task.panelStyle}
            startTime={task.startTime}
            endTime={task.endTime}
          />
        </AutoPosition>
      ) : (
        <AutoPosition
          value={task.value}
          disable={task.disable || readonly}
          startTime={task.startTime}
          cantMove={task.cantMove}
        >
          <TaskPanel {...task} disable={task.disable || readonly} />
        </AutoPosition>
      )}
    </div>
  )
}

export default React.memo(GanttWorks)
