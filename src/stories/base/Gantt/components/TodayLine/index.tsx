import React, { FC } from 'react'
import classNames from 'classnames'
import Tooltip from '@/stories/base/Tooltip'
import './index.scss'

export interface IGanttTadyLineProps {
  height: number
  todayTooltip?: any
  autoCenter?: boolean
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-gantt-tady-line'

const GanttTadyLine: FC<IGanttTadyLineProps> = ({ todayTooltip = '今日', autoCenter = true, height, className, style }) => {
  return (
    <Tooltip overlay={todayTooltip} placement="top">
      <div className={classNames(preCls, className, {
        [`${preCls}--autoCenter`]: autoCenter
      })} style={{ ...style, height }}></div>
    </Tooltip>
  )
}

export default React.memo(GanttTadyLine)
