.tita-ui-gantt-task-panel {
  position: relative;
  height: 24px;
  border-radius: 15px;
  z-index: 7;
  background-color: rgba(223, 223, 223, .7);
  transition: box-shadow .3s;

  &--isMini &__over-hidden {
    opacity: 0;
  }
  &--isMini {
    width: 8px !important;
  }

  &:hover:not(&--disable):not(&--cantMove) &__over-hidden {
    padding: 2px 15px;
  }
  &:hover &__left-bar {
    transform: translateX(12px);
  }
  &:hover &__right-bar {
    transform: translateX(-12px);
  }
  &:hover &__progress-bar {
    transform: scale(1) translateX(-50%) translateY(15px);
  }

  // &__mini-block {
  //   width: 8px;
  //   height: 100%;
  //   border-radius: 15px;
  // }
  // &:hover &__mini-content {
  //   opacity: 0;
  //   display: none;
  // }
  &__mini-content {
    position: absolute;
    display: flex;
    max-width: max-content!important;
    align-items: center;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  &__over-hidden {
    position: relative;
    display: flex;
    align-items: center;
    border-radius: 15px;
    padding: 2px;
    width: 100%;
    height: 100%;
    overflow: hidden;
    transition: padding .3s, opacity .3s;
    z-index: 1;
    color: #fff;
  }

  &__left-bar, &__right-bar {
    position: absolute;
    width: 12px;
    height: 100%;
    background-color: rgba(255, 255, 255, .3);
    transition: transform .3s;
    cursor: ew-resize;
    z-index: 3;

    &::before {
      content: '';
      position: absolute;
      width: 10px;
      height: 10px;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-image: url(https://xfile6.tita.com/ux/tita-home-page/public/drag-bar.png);
      background-repeat: no-repeat;
      background-size: contain;
    }
  }

  &__left-bar {
    left: -12px;
  }
  &__right-bar {
    right: -12px;
  }

  &__content {
    cursor: hand;
    display: flex;
    overflow: hidden;
    user-select: none;
    position: relative;
    z-index: 1;
  }
  &__progress {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: rgba(0, 0, 0, .2);
    z-index: 0;
  }
  &__svg-path {
    position: absolute;
    top: 10px;
    pointer-events: none;
    overflow: visible !important;
    z-index: 5;
  }

  &__head {
    position: relative;
    width: 20px;
    height: 20px;
    border: 1px solid #ffffff;
    margin-right: 4px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;

    img {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
      user-select: none;
      pointer-events: none;
    }
  }
  &__label {
    font-size: 12px;
    font-weight: 400;
    color: inherit;
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__overdue-days {
    position: absolute;
    left: 0px;
    top: 0;
    height: 100%;
    background-color: rgba(247, 174, 174, .5);
    z-index: 0;
    border-radius: 15px;
    pointer-events: none;
  }

  &__progress-bar {
    position: absolute;
    width: 15px;
    height: 15px;
    bottom: 0;
    z-index: 0;
    transform: scale(0) translateX(-50%);
    transition: transform .3s;
    cursor: pointer;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, .2));

    &__triangle {
      position: absolute;
      width: 0;
      height: 0;
      border-bottom: 6px solid rgba(40, 121, 255, 1);
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      filter: drop-shadow(0 0 1px rgba(255, 255, 255, 1));
      left: 50%;
      transform: translateX(-57%);
    }
    &__bar-num {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 5px;
      font-size: 12px;
      color: rgba(40, 121, 255, 1);
      white-space: nowrap;
      z-index: 10;
    }
  }
}
