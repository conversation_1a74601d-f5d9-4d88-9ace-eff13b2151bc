import React, { FC, useCallback, useContext, useMemo, useState } from 'react'
import dayjs from 'dayjs'
import { TaskKey, TaskTime, DefaultTask } from '../../types'
import TaskPanel from './ui'
import { GanttContext } from '../../context'
import DragBar from '../DragBar'
import Path from '../Path'
import { getSpaceDays } from '../../utils/date'
import classNames from 'classnames'

export interface IGanttTaskPanelProps {
  value: TaskKey
  overdueDays?: number
  earlyDays?: number
  latterTasks: DefaultTask['latterTasks']
  startTime: TaskTime
  endTime: TaskTime
  disable: boolean
  cantLink: boolean
  panelStyle: DefaultTask['panelStyle']
  cantMove: DefaultTask['cantMove']
  panelData: DefaultTask['panelData']
  onMoveLeft: (mx: number) => void
  onMoveRight: (mx: number) => void
  progress?: number
  children?: React.ReactNode
}

const preCls = 'tita-ui-gantt-task-panel'

const GanttTaskPanel: FC<IGanttTaskPanelProps> = (props) => {
  const {
    value,
    overdueDays,
    earlyDays,
    disable,
    cantLink,
    panelStyle,
    progress,
    cantMove,
    latterTasks = [],
    startTime: taskStartTime,
    endTime: taskEndTime,
    panelData,
    onMoveLeft,
    onMoveRight,
  } = props

  const { type } = panelData

  const {
    columnWidth,
    render,
    container,
    tasksDic,
    onChangeTaskProgress,
    onChangeTaskDate,
    onDoubleClick,
  } = useContext(GanttContext)
  const width = useMemo(() => {
    return getSpaceDays(taskStartTime, taskEndTime, false) * columnWidth
  }, [taskStartTime, taskEndTime, columnWidth])

  const content = useMemo(() => {
    if (!panelData) return ''
    const { head, label } = panelData
    if (render) {
      const currentTask = tasksDic[value]
      return render(panelData, currentTask, currentTask._index)
    }
    return (
      <>
        {head && (
          <div className={`${preCls}__head`}>
            <img src={head} alt='head' />
          </div>
        )}
        <p className={`${preCls}__label`}>{label}</p>
      </>
    )
  }, [render, panelData])

  const onChangeProgressHandler = useCallback(
    (progress: number) => {
      if (onChangeTaskProgress) onChangeTaskProgress(value, progress)
    },
    [onChangeTaskProgress]
  )

  const onChangeWidthHandler = useCallback(
    (mx: number, type: 'left' | 'right') => {
      if (!onChangeTaskDate) return
      let days = Math.round(mx / columnWidth)
      switch (type) {
        case 'left':
          onChangeTaskDate({
            value,
            type: 'left',
            days,
            startTime: dayjs(taskStartTime)
              .add(days, 'day')
              .format('YYYY-MM-DD HH:mm:ss'),
            endTime: undefined,
          })
          return
        case 'right':
          onChangeTaskDate({
            value,
            type: 'right',
            days,
            startTime: undefined,
            endTime: dayjs(taskEndTime)
              .add(days, 'day')
              .format('YYYY-MM-DD HH:mm:ss'),
          })
          return
      }
    },
    [onChangeTaskProgress, columnWidth]
  )

  const onDoubleClickHandler = () => {
    if (onDoubleClick) onDoubleClick(value, type)
  }

  const isMini = width <= 40

  const [hoverPanel, setHoverPanel] = useState(false)
  const onPanelOver = useCallback(() => {
    setHoverPanel(true)
  }, [])
  const onPanelOut = useCallback(() => {
    setHoverPanel(false)
  }, [])

  const [hoverBar, setHoverBar] = useState(false)
  const onBarOver = useCallback(() => {
    setHoverBar(true)
  }, [])
  const onBarOut = useCallback(() => {
    setHoverBar(false)
  }, [])

  const tootip = useMemo(() => {
    if (!panelData) return ''
    if (panelData.tootip) return panelData.tootip
    return (
      <div>
        <p>{panelData.label}</p>
        <p>{dayjs(taskStartTime).format('YYYY/MM/DD')}~{dayjs(taskEndTime).format('YYYY/MM/DD')}</p>
      </div>
    )
  }, [panelData, taskStartTime, taskEndTime])

  const taskPanel = (
    // @ts-ignore
    <TaskPanel
      {...panelData}
      tootip={tootip}
      style={panelStyle}
      cantMove={cantMove}
      width={width}
      disable={disable}
      overdueDays={overdueDays}
      earlyDays={earlyDays}
      progress={progress}
      content={content}
      hoverPanel={hoverPanel}
      hoverBar={hoverBar}
      onDoubleClick={onDoubleClickHandler}
      onChangeProgress={onChangeProgressHandler}
      onChangeWidth={onChangeWidthHandler}
      onMoveLeft={onMoveLeft}
      onMoveRight={onMoveRight}
      unitWidth={columnWidth}
    />
  )

  const overdueDaysEle = useMemo(() => {
    if(overdueDays && overdueDays > 0){
      return (
        <div
          className={classNames(`${preCls}__overdue-days`)}
          style={{ width: overdueDays * columnWidth + width }}
        ></div>
      )
    }
    return null
  }, [overdueDays])

  return (
    <DragBar
      value={value}
      disable={disable}
      cantLink={cantLink}
      onMouseOver={onPanelOver}
      onMouseOut={onPanelOut}
      onMouseOverBar={onBarOver}
      onMouseOutBar={onBarOut}
    >
      {taskPanel}
      {overdueDaysEle}
      <svg
        className={`${preCls}__svg-path`}
        style={{ left: isMini ? 8 : width }}
      >
        {latterTasks.map((taskKey) => (
          <Path
            key={taskKey}
            disable={disable}
            isMini={isMini}
            currentTaskKey={value}
            targetTaskKey={taskKey}
          />
        ))}
      </svg>
    </DragBar>
  )
}

export default React.memo(GanttTaskPanel)
