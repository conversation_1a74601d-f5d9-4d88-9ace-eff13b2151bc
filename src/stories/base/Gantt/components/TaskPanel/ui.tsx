import React, { FC, MutableRefObject, useRef, useState } from 'react'
import classNames from 'classnames'
import { useDrag } from '@use-gesture/react'
import { useEffect } from 'react'
import { useSize, useDebounce } from 'ahooks'
import Tooltip from '@/stories/base/Tooltip'
import { DefaultTask } from '../../types'
import './index.scss'

export interface IGanttTaskPanelProps {
  width: number
  progress?: number
  overdueDays?: number
  earlyDays?: number
  unitWidth: number
  theme: string
  progressTheme: string
  tootip: string | React.ReactNode
  disable?: boolean
  hoverPanel?: boolean
  hoverBar?: boolean
  cantMove?: DefaultTask['cantMove']
  isLongTerm?: boolean
  onDoubleClick?: React.MouseEventHandler<HTMLDivElement>
  onChangeProgress?: (progress: number) => void
  onChangeWidth?: (mx: number, type: 'left' | 'right') => void
  onMoveLeft: (mx: number, last: boolean) => void
  onMoveRight: (mx: number, last: boolean) => void
  content: React.ReactNode
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-gantt-task-panel'

const GanttTaskPanel: FC<IGanttTaskPanelProps> = ({ theme, progressTheme, cantMove, disable, tootip, hoverBar, hoverPanel, overdueDays, earlyDays, content, progress = 0, width, unitWidth, isLongTerm, onDoubleClick, onChangeProgress, onChangeWidth, onMoveLeft, onMoveRight, className, style }) => {
  const [_progress, setProgress] = useState(progress)
  const [dragWidth, setDragWidth] = useState(false)
  const [down, setDown] = useState(false)
  const [_width, setWidth] = useState(width)
  const [disableMini, setDisableMini] = useState(false)
  const [hoverOverHidden, setHoverOverHidden] = useState(false)
  const [showMiniContent, setShowMiniContent] = useState(false)
  const showMiniContentDebouncedValue = useDebounce(showMiniContent, { wait: 200 });
  
  useEffect(() => {
    if (hoverBar) {
      setShowMiniContent(false)
      return
    }
    if (hoverOverHidden) {
      setShowMiniContent(false)
      return
    }
    if (!hoverPanel && !hoverOverHidden) {
      setShowMiniContent(true)
      return
    }
  }, [hoverPanel, hoverOverHidden])

  const panelRef = useRef() as MutableRefObject<HTMLDivElement>
  const { width: panelWidth } = useSize(panelRef) || { width: 0 }

  useEffect(() => {
    setWidth(width)
  }, [width])

  useEffect(() => {
    setProgress(progress)
  }, [progress])

  const dragEndTimeRef = useRef<number>()

  const onMouseOverHandler = () => {
    const nowDate = Date.now()
    if (dragEndTimeRef.current && nowDate - dragEndTimeRef.current < 200) {
      setDisableMini(true)
    }
  }

  const dragLeftBind = useDrag(({ movement: [mx], event, active, last }) => {
    event.stopPropagation()
    setDragWidth(active)

    if (last && mx > -unitWidth / 2 && mx < unitWidth / 2) {
      setWidth(width)
      return
    }
    const maxX = width - 30
    if (mx >= maxX) mx = maxX

    let newWidth = width - mx
    setWidth(newWidth)
    onMoveLeft(mx, last)

    if (last && onChangeWidth) {
      dragEndTimeRef.current = Date.now()
      onChangeWidth(mx, 'left')
    }
  })

  const dragRightBind = useDrag(({ movement: [mx], event, active, last }) => {
    event.stopPropagation()
    setDragWidth(active)

    if (last && mx < unitWidth / 2 && mx > -unitWidth / 2) {
      setWidth(width)
      return
    }
    const minX = -(width - 30)
    if (mx < minX) mx = minX

    let newWidth = width + mx
    setWidth(newWidth)
    if (onMoveRight) onMoveRight(mx, last)

    if (last && onChangeWidth) {
      dragEndTimeRef.current = Date.now()
      onChangeWidth(mx, 'right')
    }
  })

  const dragBind = useDrag(({ movement: [mx], event, active, last }) => {
    event.stopPropagation()
    setDown(active)

    let newProgress = Math.floor(progress + mx / panelWidth * 100)
    if (newProgress >= 100) newProgress = 100
    if (newProgress <= 0) newProgress = 0
    setProgress(newProgress)

    if (last && onChangeProgress) onChangeProgress(_progress)
  })

  const isMini = _width <= 40 && !dragWidth && !disableMini
  // 提早完成
  const isEarly = earlyDays && earlyDays > 0
  const finalWidth = _width < 30 ? 30 : _width

  return (
    <div
      onDoubleClick={onDoubleClick}
      ref={panelRef}
      className={classNames(preCls, className, {
        [`${preCls}--isMini`]: isMini && showMiniContentDebouncedValue,
        [`${preCls}--disable`]: disable,
        [`${preCls}--cantMove`]: cantMove,
      })}
      style={{ ...style, width: finalWidth, maxWidth: finalWidth }}
      onMouseOver={onMouseOverHandler}
      onMouseOut={() => {
        setDisableMini(false)
      }}
    >
      <Tooltip overlay={tootip} placement='top'>
        <div
          className={`${preCls}__over-hidden`}
          onMouseOver={() => setHoverOverHidden(true)}
          onMouseOut={() => setHoverOverHidden(false)}
          style={{
            width: finalWidth,
            maxWidth: isEarly ? _width - earlyDays * unitWidth : _width,
            backgroundColor: theme,
          }}
        >
          {!disable && !cantMove && (
            <div className={`${preCls}__left-bar`} {...dragLeftBind()}></div>
          )}
          <div className={`${preCls}__content`}>{content}</div>
          <div
            className={`${preCls}__progress`}
            style={{ width: `${_progress}%`, backgroundColor: progressTheme }}
          ></div>
          {!disable && !cantMove && !isLongTerm && (
            <div className={`${preCls}__right-bar`} {...dragRightBind()}></div>
          )}
        </div>
      </Tooltip>
      {isMini && showMiniContentDebouncedValue && (
        <Tooltip overlay={tootip} placement='top'>
          <div className={`${preCls}__mini-content`}>{content}</div>
        </Tooltip>
      )}
      {/* {!disable && (
        <div className={`${preCls}__progress-bar`} style={{ left: `${_progress}%` }} {...dragBind()}>
          <div className={`${preCls}__progress-bar__triangle`}></div>
          {down && <p className={`${preCls}__progress-bar__bar-num`}>{_progress}%</p>}
        </div>
      )} */}
    </div>
  )
}

export default React.memo(GanttTaskPanel)
