import React, { FC, useContext } from 'react'
import classNames from 'classnames'
import { GanttContext, GanttContextValue } from '../../context'
import { TaskKey } from '../../types'
import './index.scss'

export interface GanttIDragLinkProps {
  value: TaskKey
  onMouseOver?: () => void
  onMouseOut?: () => void
  onMouseOverBar?: () => void
  onMouseOutBar?: () => void
  disable?: boolean
  cantLink?: boolean
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-gantt-drag-link'

const GanttDragLink: FC<GanttIDragLinkProps> = ({ value, disable, cantLink, onMouseOver, onMouseOut, onMouseOverBar, onMouseOutBar, className, style, children }) => {
  const { downLink, setDownLink, linkContextRef, onCreateLink, readonly } = useContext(GanttContext) as Required<GanttContextValue>
  const onMouseDownLeftHandler = () => {
    linkContextRef.current.currentKey = value
    linkContextRef.current.currentLinkType = 'left'
    setDownLink(true)
  }
  const onMouseDownRightHandler = () => {
    linkContextRef.current.currentKey = value
    linkContextRef.current.currentLinkType = 'right'
    setDownLink(true)
  }
  const onMouseOverHandler = () => {
    if (onMouseOver) onMouseOver()
    setDownLink(false)
  }
  const onMouseOutHandler = () => {
    if (onMouseOut) onMouseOut()
    if (!downLink || linkContextRef.current.currentKey === value) return
    // @ts-ignore
    linkContextRef.current.targetKey = undefined
  }
  return (
    <div
      className={classNames(preCls, className)}
      style={style}
      onMouseOver={onMouseOverHandler}
      onMouseOut={onMouseOutHandler}
    >
      {!disable && !cantLink && !readonly && (
        <div
          className={`${preCls}__left-bar`}
          onMouseDown={onMouseDownLeftHandler}
          onMouseOver={onMouseOverBar}
          onMouseOut={onMouseOutBar}
        ></div>
      )}
      {children}
      {!disable && !cantLink && !readonly && (
        <div
          className={`${preCls}__right-bar`}
          onMouseDown={onMouseDownRightHandler}
          onMouseOver={onMouseOverBar}
          onMouseOut={onMouseOutBar}
        ></div>
      )}
    </div>
  )
}

export default React.memo(GanttDragLink)
