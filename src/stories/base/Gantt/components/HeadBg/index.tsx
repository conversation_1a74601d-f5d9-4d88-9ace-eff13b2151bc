import React, { FC, useMemo } from 'react'
import classNames from 'classnames'
import DayHead from './mode/DayHead'
import WeekHead from './mode/WeekHead'
import MonthHead from './mode/MonthHead'
import './index.scss'

export interface IGanttHeadProps {
  containerHeight: number
  todayTooltip?: any
  viewMode: 'month' | 'week' | 'day'
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-gantt-head-bg'

const GanttHead: FC<IGanttHeadProps> = ({ todayTooltip, containerHeight, viewMode, className, style }) => {
  const head = useMemo(() => {
    switch (viewMode) {
      case 'day':
        return <DayHead containerHeight={containerHeight} todayTooltip={todayTooltip} />
      case 'week':
        return <WeekHead containerHeight={containerHeight} todayTooltip={todayTooltip} />
      case 'month':
        return <MonthHead containerHeight={containerHeight} todayTooltip={todayTooltip} />
    }
  }, [viewMode, containerHeight])
  return (
    <div className={classNames(preCls, className)} style={style}>
      {head}
    </div>
  )
}

export default React.memo(GanttHead)
