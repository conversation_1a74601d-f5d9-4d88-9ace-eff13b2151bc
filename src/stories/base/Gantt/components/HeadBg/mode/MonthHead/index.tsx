import React, { FC, useContext, useEffect } from 'react'
import classNames from 'classnames'
import { useState } from 'react'
import { getYearData, isToday } from '../../../../utils/date'
import { GanttContext } from '@/stories/base/Gantt/context'
import './index.scss'

export interface IGanttMonthHeadProps {
  containerHeight: number
  todayTooltip?: any
  columnWidth?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const rootPreCls = 'tita-ui-gantt-head-bg'
const preCls = 'tita-ui-gantt-month-head-bg'

export interface YearMonthItem {
  year: number
  monthDays: number[]
}

interface IHeadProps extends YearMonthItem {
  containerHeight: number
  columnWidth: number
  todayTooltip?: any
}

const Head: FC<IHeadProps> = ({ year, monthDays, columnWidth, todayTooltip, containerHeight }) => {
  return (
    <div className={`${rootPreCls}__item`}>
      {/* days */}
      <div className={`${rootPreCls}__days`}>
        {monthDays.map((days, i) => {
          // 是不是最后一天
          const monthLastDay = isToday(year, i + 1, days, `${year}-12-${days}`)
          return (
            <div key={i} style={{ width: days * columnWidth }} className={classNames(`${rootPreCls}__days-item`)}>
              {monthLastDay && <div className={`${rootPreCls}__last-day`} style={{ height: containerHeight - 52 }} />}
            </div>
          )
        })}
      </div>
    </div>
  )
}

const GanttMonthHead: FC<IGanttMonthHeadProps> = ({ todayTooltip, containerHeight, columnWidth = 4, className, style }) => {
  const { startTime, endTime } = useContext(GanttContext)
  const [years, setYears] = useState<YearMonthItem[]>(getYearData(startTime, endTime))
  
  useEffect(() => {
    setYears(getYearData(startTime, endTime))
  }, [startTime, endTime])

  return (
    <div className={classNames(preCls, className)} style={style}>
      {years.map(item => <Head key={item.year} {...item} todayTooltip={todayTooltip} columnWidth={columnWidth} containerHeight={containerHeight} />)}
    </div>
  )
}

export default React.memo(GanttMonthHead)
