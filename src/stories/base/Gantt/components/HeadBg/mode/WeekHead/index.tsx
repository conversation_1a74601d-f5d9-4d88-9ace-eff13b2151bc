import React, { FC, useContext, useEffect } from 'react'
import classNames from 'classnames'
import { useState } from 'react'
import { isWeekEnd, isToday, getMonthData } from '../../../../utils/date'
import { GanttContext } from '@/stories/base/Gantt/context'
import { MonthItem } from '@/stories/base/Gantt/types'
import './index.scss'

export interface IGanttWeekHeadProps {
  containerHeight: number
  todayTooltip?: any
  columnWidth?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const rootPreCls = 'tita-ui-gantt-head-bg'
const preCls = 'tita-ui-gantt-week-head-bg'

interface IHeadProps extends MonthItem {
  containerHeight: number
  columnWidth: number
  todayTooltip?: any
}

const Head: FC<IHeadProps> = ({ year, month, days, columnWidth, todayTooltip, containerHeight }) => {
  return (
    <div className={`${rootPreCls}__item`}>
      {/* days */}
      <div className={`${rootPreCls}__days`}>
        {new Array(days).fill(0).map((_, i) => {
          const weekEnd = isWeekEnd(year, month, i + 1)
          // 是不是最后一天
          const monthLastDay = isToday(year, month, i + 1, `${year}-${month}-${days}`)
          return (
            <div key={i} style={{ width: columnWidth, maxWidth: columnWidth }} className={classNames(`${rootPreCls}__days-item ${rootPreCls}__days-item--week`, {
              [`${rootPreCls}__days-item--weekend`]: isWeekEnd(year, month, i + 1)
            })}>
              {monthLastDay && <div className={`${rootPreCls}__last-day`} style={{ height: containerHeight - 64 }} />}
              {weekEnd && <div className={`${rootPreCls}__week-bg`} style={{ height: containerHeight - 52 }} />}
            </div>
          )
        })}
      </div>
    </div>
  )
}

const GanttWeekHead: FC<IGanttWeekHeadProps> = ({ todayTooltip, containerHeight, columnWidth = 14.4, className, style }) => {
  const { startTime, endTime } = useContext(GanttContext)

  const [months, setMonth] = useState<MonthItem[]>(getMonthData(startTime, endTime))

  useEffect(() => {
    setMonth(getMonthData(startTime, endTime))
  }, [startTime, endTime])

  return (
    <div className={classNames(preCls, className)} style={style}>
      {months.map(item => <Head key={`${item.year}_${item.month}`} {...item} todayTooltip={todayTooltip} columnWidth={columnWidth} containerHeight={containerHeight} />)}
    </div>
  )
}

export default React.memo(GanttWeekHead)
