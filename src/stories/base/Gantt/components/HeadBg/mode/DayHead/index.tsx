import React, { FC, useContext, useEffect } from 'react'
import classNames from 'classnames'
import { useState } from 'react'
import { getMonthData, isToday, isWeekEnd } from '../../../../utils/date'
import { GanttContext } from '../../../../context'
import { MonthItem } from '../../../../types'
import './index.scss'

export interface IGanttDayHeadProps {
  containerHeight: number
  todayTooltip?: any
  columnWidth?: number
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const rootPreCls = 'tita-ui-gantt-head-bg'
const preCls = 'tita-ui-gantt-day-head-bg'

interface IHeadProps extends MonthItem {
  containerHeight: number
  columnWidth: number
  todayTooltip?: any
}

const HeadBG: FC<IHeadProps> = ({ year, month, days, todayTooltip, columnWidth, containerHeight }) => {
  return (
    <div className={`${rootPreCls}__item`}>
      {/* days */}
      <div className={`${rootPreCls}__days`}>
        {new Array(days).fill(0).map((_, i) => {
          const weekEnd = isWeekEnd(year, month, i + 1)
          // 是不是最后一天
          const monthLastDay = isToday(year, month, i + 1, `${year}-${month}-${days}`)
          return (
            <div key={i} style={{ width: columnWidth }} className={classNames(`${rootPreCls}__days-item`, {
              [`${rootPreCls}__days-item--weekend`]: weekEnd
            })}>
              {monthLastDay && <div className={`${rootPreCls}__last-day`} style={{ height: containerHeight - 64 }} />}
              {weekEnd && <div className={`${rootPreCls}__week-bg`} style={{ height: containerHeight - 52 }} />}
            </div>
          )
        })}
      </div>
    </div>
  )
}

const GanttDayHead: FC<IGanttDayHeadProps> = ({ todayTooltip, containerHeight, columnWidth = 40, className, style }) => {
  const { startTime, endTime } = useContext(GanttContext)

  const [months, setMonth] = useState<MonthItem[]>(getMonthData(startTime, endTime))

  useEffect(() => {
    setMonth(getMonthData(startTime, endTime))
  }, [startTime, endTime])

  return (
    <div className={classNames(preCls, className)} style={style}>
      {months.map(item => <HeadBG key={`${item.year}_${item.month}`} {...item} todayTooltip={todayTooltip} columnWidth={columnWidth} containerHeight={containerHeight} />)}
    </div>
  )
}

export default React.memo(GanttDayHead)
