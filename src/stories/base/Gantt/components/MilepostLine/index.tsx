import React, { FC, useContext, useMemo } from 'react'
import { DefaultTask, TaskTime } from '../../types'
import MilepostLine from './ui'
import { GanttContext } from '../../context'
import { getSpaceDays } from '../../utils/date'
import dayjs from 'dayjs'

export interface IGanttMilepostLineProps {
  panelData: DefaultTask['panelData']
  panelStyle: DefaultTask['panelStyle']
  startTime: TaskTime
  endTime: TaskTime
  children?: React.ReactNode
}

const GanttMilepostLine: FC<IGanttMilepostLineProps> = ({ panelStyle, panelData, startTime: taskStartTime, endTime: taskEndTime }) => {
  const { columnWidth } = useContext(GanttContext)
  const width = useMemo(() => {
    return getSpaceDays(taskStartTime, taskEndTime, false) * columnWidth
  }, [taskStartTime, taskEndTime, columnWidth])
  
  const tootip = useMemo(() => {
    if (!panelData) return ''
    if (panelData.tootip) return panelData.tootip
    return (
      <div>
        <p>{panelData.label}</p>
        <p>{dayjs(taskStartTime).format('YYYY/MM/DD')}~{dayjs(taskEndTime).format('YYYY/MM/DD')}</p>
      </div>
    )
  }, [panelData, taskStartTime, taskEndTime])
  return (
    <MilepostLine tootip={tootip} style={panelStyle} theme={panelData && panelData.theme} width={width} />
  )
}

export default React.memo(GanttMilepostLine)
