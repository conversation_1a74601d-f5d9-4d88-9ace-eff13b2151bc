import React, { FC } from 'react'
import classNames from 'classnames'
import Tooltip from '@/stories/base/Tooltip'
import './index.scss'

export interface IGanttMilepostLineProps {
  width: number
  tootip: any
  theme?: string
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-gantt-milepost-line'

const GanttMilepostLine: FC<IGanttMilepostLineProps> = ({ tootip, theme, width, className, style }) => {
  const line = <div className={classNames(preCls, className)} style={{ ...style, width, backgroundColor: theme }} />
  if (tootip) return <Tooltip overlay={tootip} placement="top">{line}</Tooltip>
  return line
}

export default React.memo(GanttMilepostLine)
