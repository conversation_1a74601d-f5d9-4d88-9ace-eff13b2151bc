.tita-ui--paragraph2 {
  border-radius: 8px;
  // height: 40px;
  .tita-ui-ellipsis__single {
    height: 100%;
  }
  &__single-text-wrapper {
    display: flex;
    align-items: center;
    // width: 100%;
    // height: 100%;
    padding: 5px 8px;
    border-radius: 8px;
    transition: background-color 0.3s;
    transition: padding 0.3s;
    min-width: 100px;
    min-height: 28px;
    &:hover {
      background: #f0f4fa;
    }
  }
  &--disabled {
    pointer-events: none;
    cursor: not-allowed;
    &:hover {
      background: #fff !important;
    }
  }
  // &__single-input-wrapper {
  //   width: 100%;
  // }
  &__single-input-wrapper, &__multiple-input-wrapper {
    position: relative;
    width: 100%;
    .tita-ui--paragraph2__popup-content {
      position: absolute;
      left: 0;
      width: 100%;
      background-color: #fff;
      bottom: -28px;
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      z-index: 2;
      &-handle-group {
        min-width: 138px;
        button {
          font-size: 13px;
          font-weight: 400;
        }
      }
      &-error-tip {
        font-size: 12px;
        font-weight: 400;
        color: #f05e5e;
        line-height: 18px;
        width: calc(100% - 138px);
      }
    }
  }
  &__single-input-wrapper{
    .tita-ui--paragraph2__popup-content {
    bottom: -33px;
    }
  }
  &__scroll-contenner{
    overflow-y: auto;
    white-space: pre-wrap;
  }
  &__input {
    width: 100%;
  }
  .error-status {
    border: 1px solid #f05e5e;
  }
  &__multiple-text-wrapper {
    transition: background-color 0.3s;
    transition: padding 0.3s;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    word-break: break-word;
    border-radius: 8px;
    padding: 5px 8px;
    box-sizing: border-box;
    min-width: 100px;
    min-height: 28px;
    &:hover {
      background-color: #f0f4fa;
    }
  }

  &--autoPaddingX {
    padding-left: 0;
    padding-right: 0;

    &:hover {
      padding-left: 8px;
      padding-right: 8px;
    }
    &:focus {
      padding-left: 8px;
      padding-right: 8px;
    }
  }
  &--disabled {
    pointer-events: inherit;
    &:hover {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }

  &--placeholder{
    color: #BFC7D5;
  }
}

.tita-ui--paragraph2__popup {
  padding: 8px 0;
  border-radius: 0!important;
  box-shadow: none;
  &-content {
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    &-handle-group {
      min-width: 138px;
      button {
        font-size: 13px;
      }
    }
    &-error-tip {
      font-size: 12px;
      font-weight: 400;
      color: #f05e5e;
      line-height: 18px;
      width: calc(100% - 138px);
    }
  }
}

.tita-ui--paragraph2.editting {
  flex: 1;
}
