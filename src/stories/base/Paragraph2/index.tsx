// @ts-nocheck
import classNames from 'classnames'
import React, { FC, useEffect, useState, useRef, useLayoutEffect } from 'react'
import Ellipsis from '../Ellipsis'
import { IInputProps, ITextAreaProps, Input, TextArea } from '../Input/index'
import { useSize, useDebounce } from 'ahooks'
import { Handler, StringAndNumber } from '../Input/types'
import Button from '../Button'
import Popup from '../Popup'
import './index.scss'
import { getLocale } from '@tita/utils';

export interface OriginProps extends IInputProps, ITextAreaProps {}
export interface IParagraph2Props extends Omit<OriginProps, 'mode'> {
  className?: string
  style?: React.CSSProperties
  value?: string
  initialValue?: string
  ellipse?: boolean
  editable?: boolean
  mode?: 'single' | 'multiple'
  contentMode?: 'ellipsis' | 'scroll'
  max?: number
  disabled?: boolean
  showPopup?: boolean
  blurSubmit?: boolean
  onChange?: Handler
  onSubmit?: Handler
  onCancle?: () => void
  onEnter?: Handler
  disableEnterSubmit?: boolean
  maxRows?: number
  autoPaddingX?: boolean
  placeholder?: string
}

const preCls = 'tita-ui--paragraph2'

export const Paragraph2: FC<IParagraph2Props> = ({
  className,
  style,
  initialValue,
  value = '',
  ellipse,
  editable,
  mode = 'single',
  contentMode = 'ellipsis',
  showPopup = true,
  onChange,
  onSubmit,
  blurSubmit = false,
  disableEnterSubmit,
  max,
  disabled,
  maxRows = 3,
  autoPaddingX,
  placeholder,
  ...restProps
}) => {
  const [editing, setEditing] = useState(false)
  const [innerValue, setInnerValue] = useState<StringAndNumber>(
    initialValue?.toString() || value?.toString() || undefined
  )
  const [originValue, setOriginValue] = useState<StringAndNumber>(
    initialValue?.toString() || value?.toString() || undefined
  )
  const [hasError, setHasError] = useState(() => {
    return max ? (innerValue as string)?.length > max : false
  })

  const inputRef = useRef(null)
  const contentRefSize = useSize(inputRef)

  const debounceValue = useDebounce(innerValue, { wait: 300 })

  useEffect(() => {
    setInnerValue(value)
    setOriginValue(value)
  }, [value])

  useEffect(() => {
    if (!max || !debounceValue) return
    setHasError(!!((debounceValue as string)?.length > max))
  }, [debounceValue, max])

  const onEditChange = (value: StringAndNumber) => {
    setInnerValue(value)
    // onChange?.(value)
  }

  const onCancle = () => {
    setInnerValue(originValue)
    setEditing(false)
  }

  const onOk = () => {
    onSubmit?.(innerValue)
    setOriginValue(innerValue)
    setEditing(false)
  }
  const disabledSubmit = (hasError || !(innerValue as string)?.length)
  const onEnter = () => {
    if (!disableEnterSubmit && !disabledSubmit) onOk()
  }

  const onBlur = () => {
    if (!innerValue) {
      setInnerValue(originValue)
    }
    if (blurSubmit && !disabledSubmit) {
      onOk()
    }
  }

  const renderPopupContent = () => {
    return (
      <div className={`${preCls}__popup-content`}>
        {hasError && (
          <div className={`${preCls}__popup-content-error-tip`}>
            {`${getLocale('Mod_Entered')}${
              (debounceValue as string).length
            }${getLocale('Mod_Words')}最多可输入${max}字`}
          </div>
        )}
        <div className={`${preCls}__popup-content-handle-group`}>
          <Button
            size='small'
            type='border'
            className='mr-2.5 w-[64px]'
            onClick={onCancle}
          >
            {getLocale('Mod_Cancel')}
          </Button>
          <Button
            size='small'
            primary
            className='w-[64px]'
            disabled={disabledSubmit}
            onClick={onOk}
          >
            {getLocale('OKR_MyO_Butt_Determine')}
          </Button>
        </div>
      </div>
    )
  }

  const getPopupContainer = (element: HTMLElement) => {
    return element
  }

  const renderContent = () => {
    if (mode === 'single') {
      if (!editing) {
        return (
          <div
            className={classNames(`${preCls}__single-text-wrapper`, {
              [`${preCls}--autoPaddingX`]: autoPaddingX,
              [`${preCls}--placeholder`]: !innerValue,
              [`${preCls}--disabled`]: disabled,
            })}
            onClick={() => !disabled && setEditing(true)}
          >
            {!innerValue? (
              placeholder
            ) : (
              <Ellipsis>{innerValue}</Ellipsis>
            )}
          </div>
        )
      } else {
        return showPopup ? (
          // @ts-ignore
          // <Popup
          //   extraClass={`${preCls}__popup`}
          //   popupPlacement={'bottom'}
          //   popup={renderPopupContent()}
          //   popupVisible={editing}
          //   style={{ width: contentRefSize?.width }}
          //   mask={false}
          //   popupTransitionName=''
          //   getPopupContainer={getPopupContainer}
          //   maskClosable={false}
          //   destroyPopupOnHide={true}
          // >
          <div className={`${preCls}__single-input-wrapper`} ref={inputRef}>
            <Input
              autoFocus={true}
              className={classNames(`${preCls}__input`, {
                'error-status': hasError,
              })}
              onEnter={onEnter}
              onChange={onEditChange}
              placeholder={placeholder}
              value={innerValue}
              onBlur={onBlur}
              {...restProps}
            ></Input>
            {renderPopupContent()}
          </div>
        ) : (
          <div className={`${preCls}__single-input-wrapper`} ref={inputRef}>
            <Input
              autoFocus={true}
              className={classNames(`${preCls}__input`, {
                'error-status': hasError,
              })}
              onEnter={onEnter}
              onChange={onEditChange}
              placeholder={placeholder}
              value={innerValue}
              onBlur={onBlur}
              {...restProps}
            ></Input>
          </div>
        )
      }
    } else if (mode === 'multiple') {
      if (!editing) {
        return (
          <div
            onClick={() => !disabled && setEditing(true)}
            className={classNames(`${preCls}__multiple-text-wrapper`, {
              'error-status': hasError,
              [`${preCls}--autoPaddingX`]: autoPaddingX,
              [`${preCls}--placeholder`]: !innerValue,
              [`${preCls}--disabled`]: disabled,
            })}
          >
            {!innerValue ? (
              placeholder
            ) : contentMode === 'ellipsis' ? (
              <Ellipsis mode='multiple' style={{ WebkitLineClamp: maxRows }}>
                {innerValue}
              </Ellipsis>
            ) : (
              <pre className={`${preCls}__scroll-contenner`}>{innerValue}</pre>
            )}
          </div>
        )
      } else {
        return showPopup ? (
          // @ts-ignore
          // <Popup
          //   extraClass={`${preCls}__popup`}
          //   popupPlacement={'bottom'}
          //   popup={renderPopupContent()}
          //   popupVisible={editing}
          //   popupTransitionName=''
          //   style={{ width: contentRefSize?.width }}
          //   mask={false}
          //   maskClosable={false}
          //   destroyPopupOnHide={true}
          // >
          <div className={`${preCls}__multiple-input-wrapper`} ref={inputRef}>
            {/* @ts-ignore */}
            <TextArea
              autoFocus={true}
              className={classNames(`${preCls}__input`, {
                'error-status': hasError,
              })}
              autoSize={{
                maxRows,
              }}
              placeholder={placeholder}
              onPressEnter={onEnter}
              onChange={onEditChange}
              value={innerValue}
              {...restProps}
            ></TextArea>
            {renderPopupContent()}
          </div>
        ) : (
          <div className={`${preCls}__multiple-input-wrapper`} ref={inputRef}>
            <TextArea
              autoFocus={true}
              className={classNames(`${preCls}__input`, {
                'error-status': hasError,
              })}
              autoSize={{
                maxRows,
              }}
              onEnter={onEnter}
              onChange={onEditChange}
              placeholder={placeholder}
              value={innerValue}
              {...restProps}
            ></TextArea>
          </div>
        )
      }
    }
  }
  return (
    <div
      className={classNames(preCls, className, { editting: editing })}
      style={style}
    >
      {renderContent()}
    </div>
  )
}

export default Paragraph2
