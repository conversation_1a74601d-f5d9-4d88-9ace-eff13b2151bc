import classNames from 'classnames'
import React, { FC, forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import Tooltip from '../Tooltip'
import { ITooltipProps } from '../Tooltip/tooltip'
import './index.scss'
import { isMobile } from '@/utils/platform'
import { useUpdateEffect } from 'ahooks'

export interface IEllipsisProps
  extends Omit<ITooltipProps, 'overlay' | 'children'> {
  /**
   * tooltip 内容
   * @editType string
   * @default Hello!
   */
  overlay?: React.ReactNode | (() => React.ReactNode)
  /**
   * tooltip 内容
   * @editType string
   * @default 这是一段超长的内容这是一段超长的内容这是一段超长的内容这是一段超长的内容这是一段超长的内容这是一段超长的内容这是一段超长的内容这是一段超长的内容这是一段超长的内容这是一段超长的内容这是一段超长的内容这是一段超长的内容这是一段超长的内容
   */
  children?: React.ReactNode
  onOverflowChange?: (overflow: boolean) => void
  disbaleTips?: boolean
  placement?: ITooltipProps['placement']
  className?: string
  style?: React.CSSProperties
  mode?: 'single' | 'multiple'
  onClick?: React.MouseEventHandler<HTMLDivElement>
}

const preCls = 'tita-ui-ellipsis'

const ua = navigator.userAgent.toLowerCase();
const isSafari = ua.includes('safari') && !ua.includes('chrome') && !ua.includes('chromium') && !ua.includes('edg');

export const Ellipsis: FC<IEllipsisProps> = React.memo(
  forwardRef(({
    overlay,
    placement = 'top',
    children,
    className,
    disbaleTips,
    style,
    mode = 'single',
    visibleOnMobile,
    onOverflowChange,
    onClick,
    ...other
  }, ref) => {
    const containerRef = useRef() as React.MutableRefObject<HTMLDivElement>
    const contentRef = useRef() as React.MutableRefObject<HTMLDivElement>
    const [showTooltip, setShowTooltip] = useState(false)

    useUpdateEffect(() => {
      onOverflowChange?.(showTooltip)
    }, [showTooltip])

    const handleReCalc = () => {
      if (mode === 'single') {
        setShowTooltip(
          contentRef.current.offsetWidth >
            containerRef.current.offsetWidth
        )
      } else {
        setShowTooltip(
          contentRef.current.offsetHeight >
            containerRef.current.offsetHeight
        )
      }
    }

    useImperativeHandle(ref, () => ({
        reCalc: handleReCalc
      }
    ))

    useUpdateEffect(() => {
      handleReCalc()
    }, [children])
    

    useEffect(() => {
      if (!containerRef.current || (isMobile() && !visibleOnMobile)) return

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            observer.disconnect()
            if (mode === 'single') {
              let offsetWidth = contentRef.current?.offsetWidth || 0
              if (isSafari && offsetWidth) {
                offsetWidth += 0.5
              }
              setShowTooltip(
                offsetWidth >
                  containerRef.current?.offsetWidth
              )
            } else {
              setShowTooltip(
                contentRef.current?.offsetHeight >
                  containerRef.current?.offsetHeight
              )
            }
          }
        })
      })
      observer.observe(containerRef.current)
      return () => observer.disconnect()
    }, [containerRef])

    const renderContent = () => {
      if (mode === 'single') {
        return (
          <div
            ref={containerRef}
            className={classNames(`${preCls}__single`, className)}
            style={style}
            onClick={onClick}
          >
            {children}
            <div
              className='inline-block absolute opacity-0 left-0 pointer-events-none select-none'
              ref={contentRef}
            >
              {children}
            </div>
          </div>
        )
      } else {
        return (
          <div
            ref={containerRef}
            className={classNames(`${preCls}__multiple`, className)}
            style={style}
            onClick={onClick}
          >
            {children}
            <div
              className='absolute opacity-0 left-0 top-0 w-full pointer-events-none select-none'
              ref={contentRef}
            >
              {children}
            </div>
          </div>
        )
      }
    }
    return (
      (showTooltip && !disbaleTips && (
        <Tooltip
          overlay={overlay || children}
          placement={placement}
          visibleOnMobile={visibleOnMobile}
          trigger={isMobile() ? 'click' : 'hover'}
          {...other}
        >
          {renderContent()}
        </Tooltip>
      )) ||
      renderContent()
    )
  }
))

export default Ellipsis
