import React, { FC } from 'react'
import Ellipsis from '@/stories/base/Ellipsis'
import Panel from '../../Panel'

export interface IBaseProps {}

const Base: FC<IBaseProps> = ({}) => {
  return (
    <>
      <Panel className=' w-60'>
        <Ellipsis mode='single' visibleOnMobile>
          我会默认将内容作为
          tips。测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈
        </Ellipsis>
        <Ellipsis overlay='Hello!'>
          你也可以自定义 tips
          内容。测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈
        </Ellipsis>
      </Panel>
      <Panel className=' w-60 h-24'>
        <Ellipsis
          mode='multiple'
          overlay='        你也可以自定义 tips
        内容。测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈'
        >
          你也可以自定义 tips
          内容。测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈测试哈哈
        </Ellipsis>
      </Panel>
    </>
  )
}

export default React.memo(Base)
