.tita-ui-ellipsis__single {
  position: relative;
  display: inline-block;
  width: unset;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
  &::before{
    content: '';
    display: block;
  }
}

.tita-ui-ellipsis__multiple {
  width: 100%;
  height: 100%;
  position: relative;
  line-height: 22px;
  /* 2.设置旧版弹性盒 */
  display: -webkit-box;
  /* 3. 控制行数*/
  -webkit-line-clamp: 2;
  /* 4. 设置子元素的排列方式  垂直排列*/
  -webkit-box-orient: vertical;
  /* 5.溢出隐藏 */
  overflow: hidden;
}

.rc-tooltip{
  .tita-ui-ellipsis__single {
    color: #fff;
  }
  .tita-ui-ellipsis__multiple {
    color: #fff;
  }
}

// .tita-ui-ellipsis__multiple {
//   width: 100%;
//   height: 100%;
//   line-height: 30px;
//   position: relative;
//   /* 文本两端对齐方式 */
//   text-align: justify;
//   /* 溢出隐藏 */
//   overflow: hidden;
// }

// .tita-ui-ellipsis__multiple:after {
//   content: '...';
//   position: absolute;
//   right: 0;
//   bottom: 0;
//   width: 16px;
// }
