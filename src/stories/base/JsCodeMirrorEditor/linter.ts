// import * as eslint from 'eslint-linter-browserify'
import globals from 'globals'
import { IToken } from './interface'

// 使用 ESLint 的 flat config 格式
// const linter = new eslint.Linter()

// 创建符合 `eslint-linter-browserify` 支持的配置
const config: any = {
  // eslint configuration
  languageOptions: {
    globals: {
      ...globals.node,
    },
    parserOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
    },
  },
  rules: {
    semi: ['error', 'never'],
  },
}

// export function lintCode(
//   code: string,
//   variableList: { key: string; value: string }[]
// ) {
//   const currentVariableList = variableList?.map((item, index) => {
//     return {
//       ...item,
//       jsKey: `a${index}_111111111111`?.slice(0, item.value?.length),
//     }
//   })
//   try {
//     // 执行代码校验
//     const defaultCode = currentVariableList?.reduce((acc, cur) => {
//       return acc + `var ${cur.jsKey}\n`
//     }, '')
//     let newCode = code
//     currentVariableList?.forEach((item) => {
//       newCode = newCode?.replace(`#${item.value}`, item.jsKey)
//     })
//     let messages: any[] = []
//     if (code) {
//       const strCode = `${newCode} \n ${defaultCode}`
//       messages = linter.verify(strCode, config)
//     }

//     // 如果没有错误，返回一个空数组
//     return messages || []
//   } catch (error) {
//     console.error('Error during linting:', error)
//     return []
//   }
// }

interface DiagnosticError {
  from: number
  to: number
  message: string
}

export function validateExcelFormula(
  code: string,
  tokens: IToken[],
  variableList: { key: string; value: string }[]
): DiagnosticError[] {
  const errors: DiagnosticError[] = []
  const stack: { type: 'paren'; startToken: IToken }[] = []
  const functionStack: {
    name: string
    argCount: number
    startToken: IToken
    parenDepth: number
  }[] = []

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i]

    if (token.type === 'Identifier' && !variableList.some((item) => `#${item.value}` === token.tokenValue)){
      errors.push({
        from: token.start,
        to: token.end,
        message: '请选择正确的变量',
      })
    }

    if (token.type === 'Boolean' && !(token.tokenValue.toString() === 'true' || token.tokenValue.toString() === 'false')) {
      errors.push({
        from: token.start,
        to: token.end,
        message: '请输入正确的条件值，如：true, false',
      })
    }

    if (token.type === 'Equals' && tokens[i + 1]?.type === 'Equals') {
      errors.push({
        from: token.start,
        to: token.end + 1,
        message: '不支持使用 "==", 请使用单个 "=" 表示比较',
      })
    }

    if (
      token.type === 'Punctuator' && token.tokenValue === '!' &&
      tokens[i + 1]?.type === 'Equals' && tokens[i + 1].tokenValue === '='
    ) {
      errors.push({
        from: token.start,
        to: tokens[i + 1].end,
        message: '不支持 "! =" 的写法，请直接写成 "!="',
      })
    }

    if (token.type === 'Keyword') {
      const next = tokens[i + 1]
      if (!next || next.type !== 'Punctuator' || next.value !== '(') {
        errors.push({
          from: token.start,
          to: token.end,
          message: `函数 ${token.value} 缺少 (`,
        })
      } else {
        functionStack.push({
          name: token.value,
          argCount: 0,
          startToken: token,
          parenDepth: stack.length + 1, // 将要进入的括号层级
        })
      }
    }

    if (token.type === 'Punctuator') {
      if (token.value === '(') {
        stack.push({ type: 'paren', startToken: token })
      }

      if (token.value === ')') {
        const last = stack.pop()
        if (!last || last.type !== 'paren') {
          errors.push({
            from: token.start,
            to: token.end,
            message: '多余的右括号 )',
          })
        } else {
          // 检查是否有对应的函数出栈（与此括号层级匹配）
          const func = functionStack[functionStack.length - 1]
          if (func && func.parenDepth === stack.length + 1) {
            functionStack.pop()
          }
        }
      }

      if (token.value === ',') {
        // 判断是否是第一个 token，或者前面也是逗号（连续逗号）
        const prev = tokens[i - 1]
        if (!prev || (prev.type === 'Punctuator' && prev.value === ',')) {
          errors.push({
            from: token.start,
            to: token.end,
            message: '逗号使用不当',
          })
        }

        // 正确嵌套函数内的参数计数
        for (let j = functionStack.length - 1; j >= 0; j--) {
          if (functionStack[j].parenDepth === stack.length) {
            functionStack[j].argCount += 1
            break
          }
        }
      }
    }
  }

  for (const item of stack) {
    errors.push({
      from: item.startToken.start,
      to: item.startToken.end,
      message: '缺少右括号 )',
    })
  }

  return errors
}
