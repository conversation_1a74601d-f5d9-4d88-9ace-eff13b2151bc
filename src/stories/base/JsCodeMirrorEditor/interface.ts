import { BasicSetupOptions } from '@uiw/react-codemirror'
import {
  Extension,
} from '@codemirror/state'
export interface IJsCodeMirrorEditorProps {
  className?: string
  style?: React.CSSProperties
  mode?: Function
  showActiveLine?: boolean
  value?: string
  defaultValue?: string
  height?: string
  minHeight?: number
  maxHeight?: number
  width?: number
  minWidth?: number
  maxWidth?: number
  autoFocus?: boolean
  placeholder?: string
  theme?: 'light' | 'dark' | Extension
  basicSetup?: BasicSetupOptions
  editable?: boolean
  readOnly?: boolean
  onChange?: (value: string, currentToken?: string) => void
  functionList?: string[]
  variableList?: {
    key: string;
    value: string;
    label: string; 
  }[],
  getNextKey?: (currentKey: string, direction: 'next' | 'prev') => string
  hintVariable?: string
}

export type IJsMirrorEditorGetTokenizeResult = [
  error: any,
  data: IToken[]
]

export interface IJsCodeMirrorEditorRef {
  insert: (name: string, type: 'field' | 'formula' | 'symbol', cursorPos?: number) => void
  getTokenize: (str?: string) => any[]
  triggerLint?: () => any[]
  setTriggerLint: (errors?: ValidationResult[]) => void
  replaceContent: (str: string) => void
}

export interface ValidationResult {
  valid?: boolean
  error?: string
  from?: number // 错误起始位置
  to?: number // 错误结束位置
  severity?: 'error' | 'warning' | 'info'
  message?: string
}

export interface IToken {
  type: string
  value: string
  tokenValue: string
  start: number
  end: number
}