import React, {
  Ref,
  useState,
  useCallback,
  useRef,
  forwardRef,
  useImperativeHandle,
  memo,
  useMemo,
} from 'react'
import { useUpdateEffect } from 'ahooks'
import { javascript } from '@codemirror/lang-javascript'
import CodeMirror from '@uiw/react-codemirror'
import { EditorView, keymap } from '@codemirror/view'
import { linter } from '@codemirror/lint'
import { StateEffect, StateField } from '@codemirror/state'
import { defaultKeymap } from '@codemirror/commands'
import classNames from 'classnames'
import {
  formulaFormatValidate,
  // runLintCode,
  validateExcelFormulaWithCM,
} from './validateFormula'
import * as FormulaFunc from '@formulajs/formulajs'
import {
  IJsCodeMirrorEditorProps,
  IJsCodeMirrorEditorRef,
  IToken,
  ValidationResult,
} from './interface'
import useVariablePlugin from './plugin/useVariablePlugin'
import useBracketPlugin from './plugin/useBracketPlugin'
import useHintPlugin from './plugin/useHintPlugin'
import {
  checkContentAfterCursor,
  getCurrentTokenValue,
  tokenizeFormula,
} from './utils'
import Ellipsis from '@/stories/base/Ellipsis'
import { tokenize } from 'esprima'
import './index.scss'

const preCls = 'tita-ui-js-code-mirror-editor'

export interface JsCodeMirrorEditorRef extends IJsCodeMirrorEditorRef {}

export const JsCodeMirrorEditor = forwardRef(
  (
    {
      className,
      style,
      theme = 'light',
      basicSetup,
      placeholder = '请输入公式',
      autoFocus = true,
      mode = javascript,
      showActiveLine = false,
      functionList = [],
      variableList = [],
      height = '300px',
      onChange,
      defaultValue,
      getNextKey,
      editable = true,
      hintVariable,
      ...restProps
    }: IJsCodeMirrorEditorProps,
    ref: Ref<IJsCodeMirrorEditorRef>
  ) => {
    const [value, setValue] = useState(defaultValue || '')
    const [lintErrors, setLintErrors] = useState<ValidationResult[]>([])

    const editorRef = useRef<EditorView | null>(null)
    const { variableExtension, insertVariable, initializeVariables } =
      useVariablePlugin({
        variableList: variableList?.map((i) => i.value) || [],
      })
    const { bracketExtension } = useBracketPlugin()
    const { hintExtension, updateHint } = useHintPlugin({ functionList })

    useUpdateEffect(() => {
      setValue(defaultValue || '')
    }, [defaultValue])
    const handleInsertVariable = (name: string, back: number = 0) => {
      if (editorRef.current) {
        insertVariable(editorRef.current, name, back)
      }
    }

    const handleInsertFunction = (funcName: string) => {
      if (editorRef.current) {
        const view = editorRef.current
        const cursor = view.state.selection.main.head
        const line = view.state.doc.lineAt(cursor)
        const textBeforeCursor = line.text.slice(0, cursor - line.from)

        // 获取当前token
        const currentTokenValue = getCurrentTokenValue(view)

        // 检查是否有匹配的函数提示
        const hasMatchingHint = functionList.some((fn) =>
          fn.toUpperCase().startsWith(currentTokenValue.toUpperCase())
        )

        if (currentTokenValue && hasMatchingHint) {
          // 替换当前token
          const from = cursor - currentTokenValue.length
          const to = cursor
          const insertText = `${funcName}()`
          const cursorPos = from + funcName.length + 1 // 光标放在括号内

          view.dispatch({
            changes: { from, to, insert: insertText },
            selection: { anchor: cursorPos, head: cursorPos },
          })
        } else {
          // 原始插入逻辑
          const result = checkContentAfterCursor(view)
          const insertText = `${cursor === 0 ? '' : ' '}${funcName}()${
            result.meetsCondition ? ' ' : ''
          }`
          const cursorPos = cursor + funcName.length + (cursor === 0 ? 1 : 2)

          view.dispatch({
            changes: { from: cursor, to: cursor, insert: insertText },
            selection: { anchor: cursorPos, head: cursorPos },
          })
        }

        view.focus()
      }
    }

    const handleInsertSymbol = (
      symbol: string,
      cursorPosOffset: number = 0
    ) => {
      if (editorRef.current) {
        const view = editorRef.current
        const cursor = view.state.selection.main.head
        const insertText = `${symbol}`
        const cursorPos = cursor + (cursorPosOffset || symbol.length)
        view.dispatch({
          changes: { from: cursor, to: cursor, insert: insertText },
          selection: { anchor: cursorPos, head: cursorPos },
        })
        view.focus()
      }
    }

    const handleInsert = (
      name: string,
      type: 'field' | 'formula' | 'symbol',
      cursorPos?: number
    ) => {
      if (type === 'field') {
        handleInsertVariable(name)
      } else if (type === 'formula') {
        handleInsertFunction(name)
      } else if (type === 'symbol') {
        handleInsertSymbol(name, cursorPos)
      }
    }

    /** 获取 tokenize */
    const handleGetTokenize = (val?: string) => {
      const value = val || editorRef.current?.state.doc.toString()
      let tokenizeData: IToken[] = []
      try {
        tokenizeData = tokenizeFormula(functionList, value)
        return [null, tokenizeData]
      } catch (error) {
        return [error, []]
      }
    }

    const getLintErrors = (doc: string, view: EditorView) => {
      const lintErrors =
        formulaFormatValidate(doc, variableList, functionList) || []
      const formulaErrors =
        validateExcelFormulaWithCM(view).map((err) => ({
          from: err.from || 0,
          to: err.to || doc.length,
          severity: 'error',
          message: err.error || '公式错误',
        })) || []

      return lintErrors.length ? lintErrors : formulaErrors
    }

    useImperativeHandle(ref, () => ({
      insert: handleInsert,
      getTokenize: handleGetTokenize, // 暴露获取当前token的方法
      triggerLint: () => {
        if (editorRef.current) {
          const view = editorRef.current
          const doc = view.state.doc.toString()
          const errors = getLintErrors(doc, view)
          view.dispatch({
            effects: StateEffect.appendConfig.of([combinedLinter]),
          })
          setLintErrors(errors)
          return errors
        }
        return []
      },
      setTriggerLint: (errors?: ValidationResult[]) => {
        if (editorRef.current) {
          setLintErrors(errors || [])
          const view = editorRef.current
          // 创建一个临时的 linter 扩展用于显示外部传入的错误
          const customLinter = linter((view) => {
            return errors || []
          })
          // 重新配置 editor 的 linter 扩展为 customLinter
          view.dispatch({
            effects: StateEffect.appendConfig.of([customLinter]),
          })
        }
      },
      replaceContent: (newContent: string) => {
        if (editorRef.current) {
          const view = editorRef.current
          if (newContent !== view.state.doc.toString()) {
            view.dispatch({
              changes: { from: 0, to: view.state.doc.length, insert: newContent },
            })
  
            // 调用 initializeVariables 重新初始化变量
            initializeVariables(view)
          }
        }
      },
    }))

    const onValueChange = useCallback(
      (val: string, viewUpdate: any) => {
        if (editorRef.current) {
          const view = editorRef.current
          const currentToken = getCurrentTokenValue(view)
          setValue(val)
          onChange?.(val, currentToken)
          setLintErrors([])
        }
      },
      [onChange]
    )

    const onBlur = useCallback(() => {
      if (editorRef.current) {
        const val = editorRef.current?.state.doc.toString()
        onChange?.(val, '')
        updateHint(editorRef.current, '')
      }
    }, [onChange])

    const combinedLinter = linter((view) => {
      // const lintErrors = runLintCode(view.state.doc.toString(), variableList)
      const val = view.state.doc.toString()
      const lintErrors =
        formulaFormatValidate(val, variableList, functionList) || []
      const formulaErrors =
        validateExcelFormulaWithCM(view).map((err) => ({
          from: err.from || 0,
          to: err.to || val.length,
          severity: 'error',
          message: err.error || '公式错误',
        })) || []

      return lintErrors.length
        ? lintErrors
        : formulaErrors.length
        ? formulaErrors
        : []
    })
    const getNewHint = (editorView: EditorView, direction: 'next' | 'prev', key: 'ArrowUp' | 'ArrowDown') => {
      const currentTokenValue = getCurrentTokenValue(editorView)
      let match = functionList.find((fn) =>
        fn.startsWith(currentTokenValue.toUpperCase())
      )
      match = getNextKey?.(match || '', direction)
      
      if (editorRef.current && match) {
        updateHint(
          editorRef.current,
          match?.slice(currentTokenValue.length) || ''
        )
        return true
      } else {
        return defaultKeymap.find((key) => key.key === key)?.run?.(editorView)
      }
    }
    const customKeymap = keymap.of([
      ...defaultKeymap.filter(
        (key) =>
          ![
            'Tab',
            'Backspace',
            'Delete',
            'ArrowUp',
            'ArrowDown',
            'ArrowLeft',
            'ArrowRight',
            'Enter'
          ].includes(key.key)
      ),
      {
        key: 'ArrowUp',
        run: (editorView) => getNewHint(editorView, 'prev', 'ArrowUp')
      },
      {
        key: 'ArrowDown',
        run: (editorView) => getNewHint(editorView, 'next', 'ArrowDown')
      },
      {
        key: 'Tab',
        run: (editorView) => {
          if (hintVariable) {
            const currentTokenValue = getCurrentTokenValue(editorView)
            handleInsertVariable(hintVariable, currentTokenValue.length)
            return true
          }
          return false
        },
      },
      {
        key: 'Enter',
        run: (editorView) => {
          if (hintVariable) {
            const currentTokenValue = getCurrentTokenValue(editorView)
            handleInsertVariable(hintVariable, currentTokenValue.length)
            return true
          }
        },
      },
    ])

    const codeEditor = useMemo(() => {
      return (
        <CodeMirror
          value={value}
          onChange={onValueChange}
          theme={theme}
          height={height}
          basicSetup={{
            lineNumbers: false,
            foldGutter: false,
            autocompletion: false,
            defaultKeymap: false,
            highlightActiveLine: false,
            ...basicSetup,
          }}
          editable={editable}
          indentWithTab={false}
          extensions={[
            customKeymap,
            mode(),
            bracketExtension, // 高亮括号
            hintExtension, // 输入提示插件
            // lintGutter(), //如果启用 lintGutter，则会在行号旁边显示错误图标，lintGutter 则会出现
            // combinedLinter, // 公式检查
            variableExtension, // 插入变量插件
            EditorView.lineWrapping, // 自动换行
          ]}
          placeholder={placeholder}
          autoFocus={autoFocus}
          onCreateEditor={(view) => {
            editorRef.current = view
          }}
          onBlur={onBlur}
          {...restProps}
        />
      )
    }, [height, value, placeholder, editable, hintVariable])

    return (
      <div
        className={classNames(preCls, className)}
        style={{
          overflow: showActiveLine ? 'unset' : 'hidden',
          ...style,
          width: restProps.width,
        }}
      >
        {codeEditor}
        <div className={`${preCls}_error`}>
          {lintErrors?.length ? (
            <Ellipsis mode='single'>{lintErrors?.[0]?.message}</Ellipsis>
          ) : (
            ''
          )}
        </div>
      </div>
    )
  }
)

export default memo(JsCodeMirrorEditor)
