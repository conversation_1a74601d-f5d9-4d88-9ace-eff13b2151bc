import { EditorState } from '@codemirror/state'
import { EditorView } from '@codemirror/view'
import { syntaxTree } from '@codemirror/language'
import { IToken } from './interface'
// 解析匹配的括号
export function findMatchingBrackets(state: any) {
  let stack = []
  let brackets: any = {}
  let totalLines = state.doc.lines

  for (let line = 1; line <= totalLines; line++) {
    let content = state.doc.line(line).text
    for (let i = 0; i < content.length; i++) {
      let char = content[i]
      let pos = { line, ch: i }

      if ('({['.includes(char)) {
        stack.push({ char, pos })
      } else if (')}]'.includes(char)) {
        if (stack.length > 0) {
          let last: any = stack.pop()
          if (
            (last.char === '(' && char === ')') ||
            (last.char === '[' && char === ']') ||
            (last.char === '{' && char === '}')
          ) {
            brackets[JSON.stringify(last.pos)] = pos
            brackets[JSON.stringify(pos)] = last.pos
          }
        }
      }
    }
  }
  return brackets
}

export const getCurrentTokenValue = (view: EditorView) => {
  const cursorPos = view.state.selection.main.head // 获取光标位置
  const tree = syntaxTree(view.state) // 获取语法树
  const nodeAtCursor = tree.resolve(cursorPos, -1) // 获取光标位置附近的语法节点

  // 获取 token 的起始位置和结束位置
  const tokenStart = nodeAtCursor.from
  const tokenEnd = nodeAtCursor.to

  // 获取该 token 的值
  const tokenValue = view.state.doc.sliceString(tokenStart, tokenEnd)

  return tokenValue // 返回该 token 的值
}

// **检查括号内是否有内容**
export function hasContentBetween(
  state: EditorState,
  startPos: { line: number; ch: number },
  matchPos: { line: number; ch: number }
) {
  let startLine = state.doc.line(startPos.line).text
  let matchLine = state.doc.line(matchPos.line).text

  // **单行括号对**
  if (startPos.line === matchPos.line) {
    let content = startLine.slice(startPos.ch + 1, matchPos.ch)
    return content.trim().length > 0 // **非空字符串**
  }

  // **多行括号对**
  let firstLineContent = startLine.slice(startPos.ch + 1).trim()
  let lastLineContent = matchLine.slice(0, matchPos.ch).trim()

  if (firstLineContent.length > 0 || lastLineContent.length > 0) return true

  // **检查中间行**
  for (let line = startPos.line + 1; line < matchPos.line; line++) {
    if (state.doc.line(line).text.trim().length > 0) return true
  }

  return false
}

// 获取字符偏移量
export const getOffsetFromPosition = (
  code: string,
  line: number,
  column: number,
  endLine?: number,
  endColumn?: number
) => {
  const lines = code.split('\n')
  let offset = 0
  // 计算起始行的偏移
  for (let i = 0; i < line - 1; i++) {
    offset += lines[i].length + 1
  }

  const startOffset = offset + column - 1
  if (endLine && endColumn) {
    // 如果有 endLine 和 endColumn，计算结束偏移
    let endOffset = 0
    for (let i = 0; i < endLine - 1; i++) {
      endOffset += lines[i].length + 1
    }
    return [startOffset, endOffset + endColumn - 1]
  }
  return [startOffset, startOffset]
}

// 获取插入位置
export const checkContentAfterCursor = (editorView: EditorView) => {
  const cursorPos = editorView.state.selection.main.head
  const textAfterCursor = editorView.state.doc.sliceString(cursorPos)

  if (textAfterCursor.length === 0) {
    return {
      hasContent: false,
      startsWithSpace: false,
      firstNonSpaceChar: null,
    }
  }

  const trimmedText = textAfterCursor.trimStart()
  const startsWithSpace = textAfterCursor[0] === ' '
  const firstNonSpaceChar = trimmedText[0] || null

  return {
    hasContent: textAfterCursor.length > 0,
    startsWithSpace,
    firstNonSpaceChar,
    meetsCondition: textAfterCursor.length > 0 && !startsWithSpace,
  }
}

export const getExpectedArgs = (funcName: string) => {
  const FUNC_ARG_SPECS = {
    // 数学函数
    SUM: { min: 1, max: 255, desc: '求参数的和' },
    AVERAGE: { min: 1, max: 255, desc: '计算平均值' },
    COUNT: { min: 1, max: 255, desc: '计算包含数字的单元格个数' },
    ABS: { exact: 1, desc: '返回数字的绝对值' },
    CEILING: {
      min: 1,
      max: 2,
      desc: '将数字向上舍入到最接近的指定基数的倍数',
      args: ['number', 'significance'],
    },
    FLOOR: {
      min: 1,
      max: 2,
      desc: '将数字向下舍入到最接近的指定基数的倍数',
      args: ['number', 'significance'],
    },
    MAX: { min: 1, max: 255, desc: '返回参数列表中的最大值' },
    MIN: { min: 1, max: 255, desc: '返回参数列表中的最小值' },
    ROUND: {
      min: 1,
      max: 2,
      desc: '将数字四舍五入到指定的小数位数',
      args: ['number', 'num_digits'],
    },

    // 逻辑函数
    IF: {
      exact: 3,
      desc: '条件判断: IF(条件,真时返回值,假时返回值)',
      args: ['logical_test', 'value_if_true', 'value_if_false'],
    },
    IFS: {
      min: 2,
      max: 127,
      desc: '检查是否满足一个或多个条件并返回与第一个TRUE条件对应的值',
      args: ['condition1', 'value1', 'condition2', 'value2', '...'],
    },
    AND: {
      min: 1,
      max: 255,
      desc: '所有参数为TRUE时返回TRUE，否则返回FALSE',
      args: ['logical1', 'logical2', '...'],
    },
    OR: {
      min: 1,
      max: 255,
      desc: '任一参数为TRUE时返回TRUE，否则返回FALSE',
      args: ['logical1', 'logical2', '...'],
    },

    // 查找函数
    VLOOKUP: {
      exact: 4,
      desc: '垂直查找: VLOOKUP(查找值,表格区域,列序号,是否近似匹配)',
      args: ['lookup_value', 'table_array', 'col_index_num', 'range_lookup'],
    },
    INDEX: {
      min: 2,
      max: 3,
      desc: '返回表格或区域中的值',
    },

    // 文本函数
    CONCATENATE: { min: 1, max: 255, desc: '连接文本' },
    LEFT: { min: 1, max: 2, desc: '从左侧截取文本' },

    // 日期函数
    DATE: { exact: 3, desc: '创建日期: DATE(年,月,日)' },
    DATEDIF: { exact: 3, desc: '计算日期差' },
  }

  return FUNC_ARG_SPECS[funcName.toUpperCase()]
}

export const tokenizeFormula = (functionList: string[], str?: string) => {
  const functionNames = functionList
  const input = str || ''
  let tokens: IToken[] = []

  // tokens = tokenize(input)
  // return tokens

  let pos = 0

  // 自定义操作符类型映射
  const operatorTypeMap: Record<string, string> = {
    '=': 'Equals',
    '!=': 'Punctuator',
    '!': 'Punctuator',
    '>': 'Punctuator',
    '<': 'Punctuator',
    '>=': 'Punctuator',
    '<=': 'Punctuator',
    '<>': 'Punctuator',
    '+': 'Punctuator',
    '-': 'Punctuator',
    '*': 'Punctuator',
    '/': 'Punctuator',
    '^': 'Punctuator',
  }

  // const regex = /\s*(>=|<=|<>|=|>|<|[#]?[A-Za-z\u4e00-\u9fa5_][A-Za-z0-9\u4e00-\u9fa5_]*|\d+(\.\d+)?|[()+\-*/^,，])\s*/gy;
  // const regex = /\s*(>=|<=|<>|=|>|<|true|false|[#]?[A-Za-z\u4e00-\u9fa5_][A-Za-z0-9\u4e00-\u9fa5_]*|\d+(\.\d+)?%?|\(|\)|[+\-*/^,，])\s*/gi;
  // const regex = /\s*(>=|<=|<>|=|>|<|true|false|[#]?[A-Za-z\u4e00-\u9fa5_][A-Za-z0-9\u4e00-\u9fa5_]*|(?:\d+(\.\d+)?|\.\d+)%?|\(|\)|[.+\-*/^,，])\s*/gi;
  // const regex =
  //   /\s*(>=|<=|<>|!=|=|>|<|true|false|[#]?[A-Za-z\u4e00-\u9fa5_][A-Za-z0-9\u4e00-\u9fa5_]*|(?:\d+(\.\d+)?|\.\d+)%?|\(|\)|[.+\-*/^,，])\s*/gi
  // const regex = /\s*(>=|<=|<>|!=|=|>|<|!|true|false|"(?:[^"]*)"|'(?:[^']*)'|[#]?[A-Za-z\u4e00-\u9fa5_][A-Za-z0-9\u4e00-\u9fa5_]*|(?:\d+(\.\d+)?|\.\d+)%?|\(|\)|[.+\-*/^,，])\s*/gi;
  const regex = /\s*(>=|<=|<>|!=|=|>|<|!|true|false|"(?:[^"]*)"|'(?:[^']*)'|#[A-Za-z0-9\u4e00-\u9fa5_]+|[A-Za-z\u4e00-\u9fa5_][A-Za-z0-9\u4e00-\u9fa5_]*|(?:\d+(\.\d+)?|\.\d+)%?|\(|\)|[.+\-*/^,，])\s*/gi;



  let match
  while ((match = regex.exec(input)) !== null) {
    const value = match[1]
    const start = match.index
    const end = regex.lastIndex

    let type

    if (value.startsWith('#')) {
      type = 'Identifier' //'Variable';
    } else if (!isNaN(Number(value))) {
      type = 'Numeric' //'Number';
    } else if (/^(\d+(\.\d+)?|\.\d+)%?$/.test(value)) {
      type = 'Numeric'
    } else if (value === '(' || value === ')') {
      type = 'Punctuator'
    } else if (value === ',' || value === '，' || value === '.') {
      type = 'Punctuator' // 'Comma';
    } else if (/^(true|false)$/i.test(value)) {
      type = 'Boolean'
    } else if (operatorTypeMap[value] !== undefined) {
      type = operatorTypeMap[value] // 'Operator';
    } else if (functionNames.includes(value.toUpperCase())) {
      type = 'Keyword'
    } else if (/^"(.*)"$/.test(value) || /^'(.*)'$/.test(value)) {
      type = 'string';
    } else {
      type = 'string' // fallback，像 A1，或普通变量
    }
    let calculateValue = value.startsWith('#') ? value.slice(1) : value
    if (calculateValue === '=') {
      calculateValue = '=='
    }
    tokens.push({ type, value: calculateValue, tokenValue: value, start, end })
    pos = end
  }
  return tokens
}
