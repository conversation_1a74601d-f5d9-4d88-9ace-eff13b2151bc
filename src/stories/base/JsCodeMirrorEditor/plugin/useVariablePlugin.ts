// useRealVariablePlugin.ts
import {
  Decoration,
  DecorationSet,
  EditorView,
  ViewPlugin,
  ViewUpdate,
  WidgetType,
  keymap
} from "@codemirror/view";
import {
  RangeSetBuilder,
  StateEffect,
  Extension,
  EditorState,
  Text,
  StateField,
  TransactionSpec
} from "@codemirror/state";

const insertVariableEffect = StateEffect.define<{ from: number; to: number; name: string }>();
const deleteVariableEffect = StateEffect.define<{ from: number; to: number }>();
const initializeVariablesEffect = StateEffect.define<{ variables: Array<{ from: number; to: number; name: string }> }>();

const variableRegex = /#[\u4e00-\u9fa5\w]+\u200C?/g;

class VariableWidget extends WidgetType {
  constructor(readonly name: string) {
    super();
  }
  eq(other: VariableWidget) {
    return this.name === other.name;
  }
  toDOM() {
    const span = document.createElement("span");
    span.className = "cm-variable-capsule";
    // 创建单独的 # 号元素
    const hashSpan = document.createElement("span");
    hashSpan.className = "cm-variable-hash";
    hashSpan.textContent = "#";
    
    // 创建变量名元素
    const nameSpan = document.createElement("span");
    nameSpan.className = "cm-variable-name";
    nameSpan.textContent = this.name;

    const spacer = document.createElement("span");
    spacer.textContent = "\u200C";  // 加入零宽空格，实际占位但不可见
    
    // 将两个元素添加到胶囊中
    span.appendChild(hashSpan);
    span.appendChild(nameSpan);
    span.appendChild(spacer);
    
    span.setAttribute("contenteditable", "false");
    return span;
  }
  ignoreEvent() {
    return true;
  }
}

interface VariablePluginState {
  variables: Array<{ from: number; to: number; name: string, hasEmpty?: boolean }>;
}

export const variablePluginState = StateField.define<VariablePluginState>({
  create(state) {
    const initialVariables = findVariablesInDoc(state.doc);
    return { variables: initialVariables };
  },
  update(value, tr) {
    let variables = [...value.variables];
    const insertedVariables: Array<{ from: number; to: number; name: string }> = [];
    const deletedRanges: Array<{ from: number; to: number }> = [];

    for (const effect of tr.effects) {
      if (effect.is(initializeVariablesEffect)) {
        return { variables: effect.value.variables };
      }
      if (effect.is(insertVariableEffect)) {
        insertedVariables.push(effect.value);
      }
        
      if (effect.is(deleteVariableEffect)) {
        deletedRanges.push(effect.value);
      }
    }
    variables.push(...insertedVariables);
    for (const { from, to } of deletedRanges) {
      variables = variables.filter(v => !(v.from === from && v.to === to));
    }

    if (tr.docChanged) {
      if (insertedVariables.length === 0 && deletedRanges.length === 0 && tr.annotations?.some(a => a.value === 'input.paste')) {
        const newVariables = findVariablesInDoc(tr.newDoc);
        return { variables: newVariables };
      } else {
        const insertedSet = new Set(insertedVariables.map(v => `${v.from}-${v.to}`));
        variables = variables
          .map(v => {
            if (insertedSet.has(`${v.from}-${v.to}`)) return v;
            const newFrom = tr.changes.mapPos(v.from, 1);
            const newTo = tr.changes.mapPos(v.to, -1);
            const text = tr.newDoc.sliceString(newFrom, newTo);
            const hasEmpty = text.endsWith('\u200C');
            if (text === `#${v.name}`) {
              return { from: newFrom, to: newTo, name: v.name,  hasEmpty};
            }
            return null;
          })
          .filter((v): v is { from: number; to: number; name: string } => v !== null);
      }
      variables.sort((a, b) => a.from - b.from);
    } else if (tr.reconfigured) {
      const newVariables = findVariablesInDoc(tr.newDoc);
      return { variables: newVariables };
    }
    return { variables };
  }
});

const readOnlyRangesField = StateField.define<readonly { from: number; to: number }[]>({
  create() {
    return [];
  },
  update(_, tr) {
    const pluginState = tr.state.field(variablePluginState);
    return pluginState.variables.map(v => ({ from: v.from, to: v.to }));
  }
});

function findVariablesInDoc(doc: Text): Array<{ from: number; to: number; name: string }> {
  const variables: Array<{ from: number; to: number; name: string, hasEmpty?: boolean }> = [];
  const text = doc.toString();
  let match;
  while ((match = variableRegex.exec(text))) {
    const name = match[0].slice(1).replace("\u200C", "");
    const hasEmpty = match[0].slice(1).includes('\u200C');
    const to = match.index + match[0].length - (hasEmpty ? 1 : 0);
    variables.push({ from: match.index, to, name, hasEmpty });
  }
  return variables;
}

export function getVariableAtPos(pos: number, state: EditorState) {
  const { variables } = state.field(variablePluginState);
  return variables.find(v => pos >= v.from && pos <= v.to);
}

function useRealVariablePlugin({ variableList }: { variableList: string[] }): {
  variableExtension: Extension;
  insertVariable: (view: EditorView, name: string, back: number) => void;
  initializeVariables: (view: EditorView) => void;
} {
  const pluginExtension = [variablePluginState, readOnlyRangesField];

  const plugin = ViewPlugin.fromClass(
    class {
      decorations: DecorationSet;
      constructor(view: EditorView) {
        this.decorations = this.buildDecorations(view);
      }
      update(update: ViewUpdate) {
        if (update.docChanged || update.viewportChanged) {
          this.decorations = this.buildDecorations(update.view);
        }
        if (update.selectionSet) {
          const { from } = update.state.selection.main;
          const variable = getVariableAtPos(from, update.state);
          if (variable && from > variable.from && from < variable.to) {
            update.view.dispatch({ selection: { anchor: variable.to } });
          }
        }
      }
      buildDecorations(view: EditorView) {
        const builder = new RangeSetBuilder<Decoration>();
        const { variables } = view.state.field(variablePluginState);
        variables.forEach(v => {
          if (variableList.includes(v.name)) {
            builder.add(
              v.from,
              v.to,
              Decoration.replace({
                widget: new VariableWidget(v.name),
                inclusive: false,
                atomic: true,
                side: 1
              })
            );
          }
        });
        return builder.finish();
      }
    },
    {
      decorations: v => v.decorations,
      eventHandlers: {
        mousedown(view, event) {
          const pos = view.posAtCoords?.({ x: event.clientX, y: event.clientY });
          if (pos == null) return false;
          const variable = getVariableAtPos(pos, view.state);
          if (variable) {
            view.dispatch({ selection: { anchor: variable.from, head: variable.to } });
            event.preventDefault();
            return true;
          }
          return false;
        }
      }
    }
  );

  const variableKeymap = keymap.of([
    {
      key: "Backspace",
      run: (view: EditorView) => handleVariableDeletion(view, "backward")
    },
    {
      key: "Delete",
      run: (view: EditorView) => handleVariableDeletion(view, "forward")
    }
  ]);

  const handleVariableDeletion = (view: EditorView, direction: "forward" | "backward") => {
    const currentState = view.state;
    let transaction: TransactionSpec | null = null;
    const range = currentState.selection.main;
    if (range.empty) {
      const pos = direction === "backward" ? range.from - 1 : range.from;
      const variable = getVariableAtPos(pos, currentState);
      if (variable && (
        (direction === "backward" && range.from === variable.to + (variable.hasEmpty ? 1 : 0)) ||
        (direction === "forward" && range.from === variable.from)
      )) {
        const from = variable.from;
        const to = variable.hasEmpty ? variable.to + 1 : variable.to;
        transaction = {
          changes: { from, to, insert: "" },
          effects: deleteVariableEffect.of({ from, to }),
          selection: { anchor: from }
        };
      }
    }
    if (transaction) {
      view.dispatch(transaction);
      return true;
    }
    return false;
  };

  const insertVariable = (view: EditorView, name: string, back: number = 0) => {
    if (!variableList.includes(name)) return;
    const { from, to } = view.state.selection.main;
    const variableText = `#${name}\u200C`;
    const variableEnd = from + variableText.length - back;
    view.dispatch({
      changes: { from: from - back, to: to, insert: variableText },
      selection: { anchor: variableEnd },
      effects: insertVariableEffect.of({ from: from - back, to: variableEnd, name })
    });
    view.focus();
  };

  const initializeVariables = (view: EditorView) => {
    const variables = findVariablesInDoc(view.state.doc).filter(v => variableList.includes(v.name));
    view.dispatch({ effects: initializeVariablesEffect.of({ variables }) });
  };

  return {
    variableExtension: [
      ...pluginExtension,
      plugin,
      variableKeymap,
      EditorView.baseTheme({
        ".cm-variable-capsule": {
          display: "inline-block",
          backgroundColor: "rgba(128,166,255,0.2)",
          borderRadius: "4px",
          padding: "0px 6px",
          color: "#002680",
          fontSize: "1em",
          margin: "0 2px",
          verticalAlign: "middle",
          height: '20px',
          lineHeight: '20px',
          transition: 'opacity 0.15s ease-out', // 添加过渡效果
          willChange: 'opacity' // 提示浏览器优化
        },
        ".cm-variable-capsule:hover": {
          backgroundColor: "rgba(128,166,255,0.6)",
          cursor: "pointer"
        },
        // 添加 # 号的特殊样式
        ".cm-variable-hash": {
          marginRight: "4px"
        },
      })
    ],
    insertVariable,
    initializeVariables,
  };
}

export default useRealVariablePlugin;
