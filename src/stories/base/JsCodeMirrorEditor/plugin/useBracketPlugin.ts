import { StateEffect, Extension, StateField } from '@codemirror/state'
import { EditorView, Decoration, ViewUpdate } from '@codemirror/view'
import { findMatchingBrackets } from '../utils'
function useBracketPlugin(): {
  bracketExtension: Extension
} {
  // 定义清除高亮的 Effect
  const clearBracketHighlightEffect = StateEffect.define()
  // 定义高亮括号的 StateField

  const highlightBracketField = StateField.define({
    create() {
      return Decoration.none
    },
    update(oldDecorations, transaction) {
      // 只在光标移动或删除时重新计算高亮
      // if (!transaction.selectionSet && !transaction.docChanged) return oldDecorations;

      // **失去焦点时清除高亮**
      if (transaction.effects.some((e) => e.is(clearBracketHighlightEffect))) {
        return Decoration.none
      }

      let cursor = transaction.state.selection.main.head
      let brackets = findMatchingBrackets(transaction.state)
      let decorations: any[] = []

      let matchedBracket = null
      for (let key in brackets) {
        let matchPos = brackets[key]
        let startPos = JSON.parse(key)
        if (
          cursor >=
            transaction.state.doc.line(startPos.line).from + startPos.ch &&
          cursor <= transaction.state.doc.line(matchPos.line).from + matchPos.ch
        ) {
          // if (hasContentBetween(transaction.state, startPos, matchPos)) {
          //   matchedBracket = { startPos, matchPos };
          // }
          matchedBracket = { startPos, matchPos }
          break
        }
      }

      if (matchedBracket) {
        decorations.push(
          Decoration.mark({ class: 'bracket-highlight' }).range(
            transaction.state.doc.line(matchedBracket.startPos.line).from +
              matchedBracket.startPos.ch,
            transaction.state.doc.line(matchedBracket.startPos.line).from +
              matchedBracket.startPos.ch +
              1
          ),
          Decoration.mark({ class: 'bracket-highlight' }).range(
            transaction.state.doc.line(matchedBracket.matchPos.line).from +
              matchedBracket.matchPos.ch,
            transaction.state.doc.line(matchedBracket.matchPos.line).from +
              matchedBracket.matchPos.ch +
              1
          )
        )
      }

      return Decoration.set(decorations)
    },
    provide: (f) => EditorView.decorations.from(f),
  })
  // **CodeMirror 失去焦点时清除括号高亮**
  const updateListener = EditorView.updateListener.of((update: ViewUpdate) => {
    if (!update.focusChanged && !update.selectionSet) return
    
    if (!update.view.hasFocus) {
      if (update.state.field(highlightBracketField).size > 0) {
        update.view.dispatch({
          effects: clearBracketHighlightEffect.of(null),
        })
      }
    }
  })

  return {
    bracketExtension: [
      highlightBracketField,
      updateListener,
      EditorView.theme({
        '.bracket-highlight': {
          backgroundColor: '#F6BD16',
          borderRadius: '4px',
        },
      }),
    ],
  }
}

export default useBracketPlugin
