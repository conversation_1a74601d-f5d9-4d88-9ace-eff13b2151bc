import { StateEffect, Extension, StateField } from '@codemirror/state'
import {
  EditorView,
  Decoration,
  ViewUpdate,
  ViewPlugin,
  DecorationSet,
  WidgetType,
  keymap,
} from '@codemirror/view'
import { defaultKeymap } from '@codemirror/commands'
import { findMatchingBrackets, getCurrentTokenValue } from '../utils'

const updateHintEffect = StateEffect.define<string>()
function useHintPlugin({ functionList }): {
  hintExtension: Extension,
  updateHint: (view: EditorView, hint: string) => void
} {
  // 内联提示的小部件
  class HintWidget extends WidgetType {
    constructor(readonly hint: string) {
      super()
    }
    toDOM() {
      const span = document.createElement('span')
      span.textContent = this.hint
      // span.style.opacity = '0.5'
      span.style.pointerEvents = 'none'
      span.style.color = '#3f4755'
      span.style.lineHeight = '22px'
      span.style.backgroundColor = 'rgba(40,121,255,0.1)'
      return span
    }
  }

  // 生成装饰器，用于渲染灰色的内联提示
  const hintPlugin = ViewPlugin.fromClass(
    class {
      decorations: DecorationSet
      externalHint: string | null = null // 外部手动设置的 hint，优先级高
      currentHint: string = ''
      constructor(view: EditorView) {
        this.decorations = this.getDecorations(view)
      }
      update(update: any) {
        let hintChanged = false
        for (let tr of update.transactions) {
          for (let e of tr.effects) {
            if (e.is(updateHintEffect)) {
              this.externalHint = e.value
              hintChanged = true
            }
          }
        }
        if (update.docChanged) {
          this.externalHint = null // 输入时清空外部 hint，回到自动推断
          hintChanged = true
        }
        if (hintChanged) {
          this.decorations = this.getDecorations(update.view)
        }
      }
      getDecorations(view: EditorView) {
        const widgets: any[] = []
        const cursor = view.state.selection.main.head
        const line = view.state.doc.lineAt(cursor)
        const textAfterCursor = line.text.slice(cursor - line.from)

        // 修改：仅在光标后面为空格或没有任何内容
        // if (!textAfterCursor.trim() || textAfterCursor.startsWith(' ')) {
        //   // 使用 Token 来替换字符串匹配
        //   const tokens = []
        //   const tokenizer = view.state.doc.lineAt(cursor).text
        //   let currentToken = ''

        //   // 分词逻辑
        //   for (let i = 0; i < tokenizer.length; i++) {
        //     const char = tokenizer[i]
        //     if (char.match(/[a-zA-Z0-9_]/)) {
        //       currentToken += char
        //     } else {
        //       if (currentToken) {
        //         tokens.push({ type: 'identifier', value: currentToken })
        //         currentToken = ''
        //       }
        //       if (!char.match(/\s/)) {
        //         tokens.push({ type: 'other', value: char })
        //       }
        //     }
        //   }
        //   if (currentToken) {
        //     tokens.push({ type: 'identifier', value: currentToken })
        //   }

        //   const currentTokenValue = getCurrentTokenValue(view)

        //   // 查找与函数匹配的 token
        //   const match = typeof currentToken === 'string' ? functionList.find((fn) => {
        //     const fnPrefix = fn.toUpperCase()
        //     // 在 token 中查找匹配函数
        //     return fnPrefix.startsWith(currentTokenValue.toUpperCase())
        //   }) : '';

        //   if (match && currentTokenValue.length > 0) {
        //     const completedFunction = match + '()'
        //     const fullText = line.text

        //     // **光标是否在已完成的 `SUM()` 中**
        //     const matchIndex = fullText.indexOf(completedFunction)
        //     const cursorInsideCompletedWord =
        //       matchIndex !== -1 &&
        //       cursor > matchIndex && // 光标在 `SUM()` 的 `S` 之后
        //       cursor < matchIndex + match.length // 光标在 `)` 之前

        //     // if (cursorInsideCompletedWord) {
        //     //   return Decoration.none; // **如果光标在 `SUM()` 内部，则不提示**
        //     // }

        //     // 仅在光标位于单词末尾时，显示灰色提示
        //     const hintText = match.slice(currentTokenValue.length) + '()'
        //     const deco = Decoration.widget({
        //       widget: new HintWidget(hintText),
        //       side: 1,
        //     })
        //     widgets.push(deco.range(cursor))
        //   }
        // }

        let hint = ''
        if (!(textAfterCursor.trim() || '') || textAfterCursor.startsWith(' ')) {
          const currentTokenValue = getCurrentTokenValue(view)
          if (this.externalHint !== null) {
            hint = this.externalHint
          } else if (currentTokenValue.length > 0) {
            const match = functionList.find((fn) => {
              const fnPrefix = fn.toUpperCase();
              const currentToken = currentTokenValue.replace(/^\s+|\s+$/g,'').replace(/\u200C/g, "")
              return currentToken ? fnPrefix.startsWith(currentToken.toUpperCase()) : false
            })
            if (match) {
              hint = match.slice(currentTokenValue.length) + '()'
            }
          }
        }

        this.currentHint = hint // 记录当前 hint
        if (hint) {
          const deco = Decoration.widget({
            widget: new HintWidget(hint),
            side: 1,
          })
          widgets.push(deco.range(cursor))
        }

        return Decoration.set(widgets)
      }
    },
    {
      decorations: (v) => v.decorations,
    }
  )

  const insertHintCompletion = (editorView: EditorView, key): boolean => {
    const plugin = editorView.plugin(hintPlugin)
    if (!plugin) return false
  
    const hint = plugin.currentHint
    const cursor = editorView.state.selection.main.head
    const line = editorView.state.doc.lineAt(cursor)
    const textBeforeCursor = line.text.slice(0, cursor - line.from)
    const currentTokenValue = getCurrentTokenValue(editorView)
  
    if (hint && currentTokenValue.length > 0) {
      const insertText =
        textBeforeCursor.slice(0, textBeforeCursor.length - currentTokenValue.length) +
        currentTokenValue.toUpperCase() +
        hint
  
      editorView.dispatch({
        changes: {
          from: cursor - textBeforeCursor.length,
          to: cursor,
          insert: insertText,
        },
        selection: {
          anchor:
            cursor -
            currentTokenValue.length +
            (currentTokenValue + hint).indexOf('()') +
            1,
        },
      })
      return true
    }

    if (key === 'Enter'){
      return defaultKeymap?.find((key) => key.key === 'Enter')?.run(editorView)
    }
  
    return false
  }

  const tabCompletion = keymap.of([
    {
      key: 'Tab',
      run: (editorView) => insertHintCompletion(editorView, 'Tab'),
    },
    {
      key: 'Enter',
      run: (editorView) => insertHintCompletion(editorView, 'Enter')
    },
  ])
  function updateHint(view: EditorView, hint: string) {
    view.dispatch({
      effects: updateHintEffect.of(hint ? hint + '()' : hint),
    })
  }
  return {
    hintExtension: [hintPlugin, tabCompletion],
    updateHint
  }
}

export default useHintPlugin
