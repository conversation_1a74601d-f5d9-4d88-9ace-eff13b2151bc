import { parse } from 'excel-formula-parser'
import { syntaxTree } from '@codemirror/language'
import { EditorView } from '@codemirror/view'
import { getExpectedArgs, getOffsetFromPosition, tokenizeFormula } from './utils'
import { translateEslintMessage } from './translateEslintMessage'
import { 
  // lintCode, 
  validateExcelFormula 
} from './linter'
import { ValidationResult } from './interface'

// 运行 ESLint 校验代码
// export const runLintCode = (code: string, variableList: {key: string, value: string}[]) => {
//   // 调用 lintCode 获取 ESLint 校验结果
//   const messages: any = lintCode(code, variableList)

//   const errors = messages.map((msg: any) => {
//     let translatedMessage = msg.message
//     if (msg.fatal) {
//       // 如果是 fatal 错误，直接返回一个默认的错误位置
//       const offset = getOffsetFromPosition(code, msg.line, msg.column)
//       translatedMessage = translateEslintMessage(msg.message, msg.ruleId) // 对 fatal 错误进行翻译
//       return {
//         from: offset[0], // 使用起始位置
//         to: offset[0] + 1, // 假设错误是单字符错误
//         severity: 'error',
//         message: `语法错误: ${translatedMessage}`, // 显示更具体的错误信息
//       }
//     }

//     // 处理非 fatal 错误
//     // const from = getOffsetFromPosition(code, msg.line, msg.column)
//     // const to = getOffsetFromPosition(
//     //   code,
//     //   msg.endLine || msg.line, // 如果有 endLine，使用 endLine
//     //   msg.endColumn || msg.column + 1 // 如果有 endColumn，使用 endColumn，否则假设错误是一个字符
//     // )

//     // return {
//     //   from: from[0], // 使用开始位置
//     //   to: to[1], // 使用结束位置
//     //   severity: 'error', // 错误级别, // 错误级别
//     //   message: msg.message, // 错误信息
//     // }
//   })

//   return errors
// }

export const formulaFormatValidate = (code: string, variableList: {key: string, value: string}[], functionList: string[]) => {
  const tokenize = tokenizeFormula(functionList, code);
  const messages = validateExcelFormula(code, tokenize, variableList);
  return messages?.map(i=>({...i, severity: 'error', error: i.message}));
}

export const validateExcelFormulaWithCM = (
  view: EditorView
): ValidationResult[] => {
  const errors: ValidationResult[] = []
  const docText = view.state.doc.toString()
  const tree = syntaxTree(view.state)

  let ast: any
  try {
    ast = parse(docText?.replace(/#/g, ''))
  } catch (err) {
    if (docText.trim() !== '') {
      errors.push({
        from: 0,
        to: docText.length,
        error: '语法错误'
      })
    }
    return errors
  }

  // 提取语法树中的函数调用（按顺序）
  const functionTokens: { name: string; from: number; to: number }[] = []

  tree.cursor().iterate((node) => {
    if (node.name === 'FunctionCall' || node.name === 'CallExpression') {
      const nameNode =
        node.node.getChild('VariableName') || node.node.getChild('Identifier')
      if (nameNode) {
        const name = docText.slice(nameNode.from, nameNode.to).toUpperCase()
        functionTokens.push({
          name,
          from: node.from,
          to: node.to,
        })
      }
    }
  })

  // 匹配并打标签（按顺序取出）
  let funcIndex = 0

  const validateNode = (astNode: any) => {
    if (astNode?.type === 'function') {
      const expectedNode = getExpectedArgs(astNode.name)
      const expectedArgs = getExpectedArgs(astNode.name)?.exact
      const expectedMinArgs = getExpectedArgs(astNode.name)?.min
      const expectedMaxArgs = getExpectedArgs(astNode.name)?.max
      const expectedArgsCount = astNode.arguments.length
      if (!expectedNode) {
        const token = functionTokens[funcIndex]
        if (token && token.name === astNode.name.toUpperCase()) {
          errors.push({
            from: token.from,
            to: token.to,
            error: `未知函数: ${astNode.name}`,
          })
          funcIndex++
        }
      } else if (
        (expectedArgs && expectedArgsCount < expectedArgs) ||
        (expectedMinArgs && expectedArgsCount < expectedMinArgs) ||
        (expectedMaxArgs && expectedArgsCount > expectedMaxArgs)
      ) {
        // 使用语法树中已提取的函数位置信息
        const token = functionTokens[funcIndex]
        if (token && token.name === astNode.name.toUpperCase()) {
          if (expectedArgs) {
            errors.push({
              from: token.from,
              to: token.to,
              error: `${astNode.name} 需要 ${expectedArgs} 个参数，当前仅 ${expectedArgsCount} 个`,
            })
          } else if (expectedMinArgs && expectedArgsCount < expectedMinArgs) {
            errors.push({
              from: token.from,
              to: token.to,
              error: `${astNode.name} 至少需要 ${expectedMinArgs} 个参数，当前仅 ${expectedArgsCount} 个`,
            })
          } else if (expectedMaxArgs && expectedArgsCount > expectedMaxArgs) {
            errors.push({
              from: token.from,
              to: token.to,
              error: `${astNode.name} 最多接受 ${expectedMaxArgs} 个参数，当前有 ${expectedArgsCount} 个`,
            })
          }
          funcIndex++
        }
      } else {
        funcIndex++ // 正常匹配，也需要推进
      }

      // 继续递归处理嵌套参数
      for (const arg of astNode.arguments) {
        validateNode(arg)
      }
    } else if (astNode?.type === 'binary-expression') {
      validateNode(astNode.left)
      validateNode(astNode.right)
    }
  }

  validateNode(ast)

  return errors
}
