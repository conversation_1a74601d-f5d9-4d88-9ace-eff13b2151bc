type ErrorTranslation = {
  keyword: string | RegExp;
  message: string;
  ruleId?: string; // 新增 ruleId 字段
};
export const errorTranslations: ErrorTranslation[] = [
  { keyword: /Unexpected token\s+\)/i, message: '应为表达式' },
  { keyword: /Unexpected token\s+\(/i, message: '多余的左括号' },
  { keyword: /Missing semicolon/i, message: '缺少分号' },
  { keyword: /Unexpected identifier/i, message: '标识符使用不当' },
  { keyword: /Unexpected string/i, message: '字符串使用不当' },
  { keyword: /'[^']+' is not defined/i, message: '未定义的变量' },
  { keyword: /Assignment to constant variable/i, message: '不允许修改常量' },
  { keyword: /Unexpected reserved word/i, message: '保留字使用不当' },
  { keyword: /Unexpected number/i, message: '数字出现位置不正确' },
  { keyword: /Unexpected end of input/i, message: '代码未结束' },
  { keyword: /Unexpected keyword/i, message: '关键字使用不当' },
  { keyword: /Cannot assign to read only property/i, message: '只读属性不能赋值' },
  { keyword: /Missing space before function parentheses/i, message: '函数括号前缺少空格', ruleId: 'space-before-function-paren' },
  { keyword: /Expected indentation of 2 spaces but found 4/i, message: '缩进应为 2 空格，但找到 4 空格', ruleId: 'indent' },
  { keyword: /Unexpected use of comma operator/i, message: '不应使用逗号运算符', ruleId: 'no-sequences' },
  { keyword: /'console' is not allowed/i, message: '不允许使用 console', ruleId: 'no-console' },
  { keyword: /Missing return type/i, message: '缺少返回类型声明', ruleId: 'consistent-return' },
  { keyword: /'==' should be '==='/i, message: '建议使用严格相等（===）代替宽松相等（==）', ruleId: 'eqeqeq' },
  { keyword: /Expected an assignment or function call/i, message: '应为赋值语句或函数调用', ruleId: 'no-unused-expressions' },
  { keyword: /Unnecessary escape character/i, message: '不必要的转义字符', ruleId: 'no-useless-escape' },
  { keyword: /Unexpected console statement/i, message: '不应包含 console 语句', ruleId: 'no-console' },
  { keyword: /No variable assignments/i, message: '不应进行变量赋值', ruleId: 'no-var' },
  { keyword: /Too many blank lines/i, message: '空白行过多', ruleId: 'no-multiple-empty-lines' },
  { keyword: /Function '.*' has too many parameters/i, message: '函数参数过多', ruleId: 'max-params' },
  { keyword: /Missing JSDoc comment/i, message: '缺少 JSDoc 注释', ruleId: 'require-jsdoc' },
  { keyword: /Unexpected function expression/i, message: '不应使用函数表达式', ruleId: 'prefer-arrow-callback' },
  { keyword: /Expected newline after "if" condition/i, message: 'if 条件后应换行', ruleId: 'newline-after-if' },
  { keyword: /Variable .* is declared but never used/i, message: '变量声明但未使用', ruleId: 'no-unused-vars' },
  { keyword: /Expected method shorthand/i, message: '方法应使用简写语法', ruleId: 'object-shorthand' },
  { keyword: /Unexpected use of 'eval'/i, message: '不应使用 eval 函数', ruleId: 'no-eval' },
  { keyword: /Too many nested callbacks/i, message: '回调函数过多层嵌套', ruleId: 'max-nested-callbacks' },
  { keyword: /Missing default case in switch statement/i, message: 'switch 语句缺少默认情况', ruleId: 'default-case' },
  { keyword: /Unexpected async function/i, message: '不应使用异步函数', ruleId: 'no-async-promise-executor' },
  { keyword: /Unexpected string concatenation/i, message: '不应使用字符串拼接', ruleId: 'prefer-template' },
  { keyword: /Missing 'new' keyword/i, message: '缺少 new 关键字', ruleId: 'new-cap' },
  { keyword: /'this' outside of a method/i, message: 'this 只能在方法内使用', ruleId: 'no-invalid-this' },
  { keyword: /Unexpected alert/i, message: '不应使用 alert', ruleId: 'no-alert' },
  { keyword: /Multiple spaces found before '='/i, message: '等号前有多余空格', ruleId: 'space-infix-ops' },
  { keyword: /Unnecessary parentheses/i, message: '不必要的括号', ruleId: 'no-extra-parens' },
  { keyword: /Expected a default case/ , message: '应包含 default 分支', ruleId: 'default-case-last' },
  { keyword: /Expected property shorthand/i, message: '属性应使用简写语法', ruleId: 'prefer-destructuring' },
  { keyword: /Unexpected continue statement/i, message: '不应使用 continue 语句', ruleId: 'no-continue' },
  { keyword: /Unexpected function invocation/i, message: '不应直接调用函数', ruleId: 'no-new-func' },
  { keyword: /Multiple spaces found after 'if'/i, message: 'if 后有多余空格', ruleId: 'space-after-if' },
  { keyword: /'require' should not be used/i, message: '不应使用 require', ruleId: 'global-require' },
  { keyword: /The value of "this" is not always consistent/i, message: 'this 的值并不总是一致', ruleId: 'consistent-this' },
  { keyword: /"use strict" is unnecessary/i, message: '不需要使用 "use strict"', ruleId: 'strict' },
  { keyword: /Unexpected empty block/i, message: '不应出现空代码块', ruleId: 'no-empty' },
  { keyword: /Expected to return a value at the end of this function/i, message: '函数末尾应返回一个值', ruleId: 'consistent-return' },
  { keyword: /Expected type "string" but received/i, message: '期望类型为 "string"，但收到的是其他类型', ruleId: 'valid-typeof' },
  { keyword: /Extra semicolon/i, message: '多余的分号', ruleId: 'no-extra-semi' },
  { keyword: /Empty array/i, message: '空数组', ruleId: 'no-empty-array' },
  { keyword: /Missing function parentheses/i, message: '缺少函数括号', ruleId: 'func-names' },
  { keyword: /Expected an arrow function/i, message: '应使用箭头函数', ruleId: 'prefer-arrow-callback' },
  { keyword: /Invalid template literal/i, message: '模板字符串无效', ruleId: 'no-template-curly-in-string' },
  { keyword: /Unexpected function declaration/i, message: '不应使用函数声明', ruleId: 'func-style' },
  { keyword: /Unexpected multiline comment/i, message: '不应使用多行注释', ruleId: 'multiline-comment-style' },
  { keyword: /Unexpected empty character class/i, message: '不应出现空字符类', ruleId: 'no-empty-character-class' },
  { keyword: /Avoid using arguments.callee/i, message: '不应使用 arguments.callee', ruleId: 'no-caller' },
  { keyword: /Unexpected chained assignment/i, message: '不应链式赋值', ruleId: 'no-multi-assign' },
  { keyword: /Unexpected duplicate case label/i, message: '不应出现重复的 case 标签', ruleId: 'no-duplicate-case' },
  { keyword: /Unexpected setter/i, message: '不应出现 setter', ruleId: 'no-setter' },
  { keyword: /Expected method "get" to be defined/i, message: '应定义 "get" 方法', ruleId: 'getter-return' },
  { keyword: /Unexpected parameter ".*" in function/i, message: '函数中的参数不应使用 ".*" 名称', ruleId: 'no-param-reassign' },
  { keyword: /Unexpected return/i, message: '不应返回值', ruleId: 'no-return-assign' },
  // 更多规则
];

export function translateEslintMessage(message: string, ruleId?: string): string {
  // 尝试先根据 ruleId 匹配
  if (ruleId) {
    const ruleTranslation = errorTranslations.find((translation) => translation.ruleId === ruleId);
    if (ruleTranslation) {
      return ruleTranslation.message;
    }
  }
  
  // 如果没有根据 ruleId 匹配到，再进行关键词匹配
  for (const { keyword, message: zhMessage } of errorTranslations) {
    const matched = typeof keyword === 'string'
      ? message.includes(keyword)
      : keyword.test(message);
    if (matched) return zhMessage;
  }

  return message; // 如果没有匹配上，返回原始英文信息
}