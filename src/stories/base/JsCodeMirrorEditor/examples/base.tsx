import React, { FC, useRef } from 'react'
import JsCodeMirrorEditor, { JsCodeMirrorEditorRef } from '@/stories/base/JsCodeMirrorEditor'

export interface IBaseProps {}

const functionList = ['SUM', 'SELECT', 'SUBSTRING', 'STRING_AGG'] // 预设的函数列表
const Base: FC<IBaseProps> = ({}) => {
  const editorRef = useRef<JsCodeMirrorEditorRef>(null)
  const handleInsert = () => {
    if (editorRef.current){
      editorRef.current.insert('hello', 'field')
    }
  }
  const handleInsertFunction = (name: string) => {
    if (editorRef.current) {
      editorRef.current.insert(`${name}`, 'formula')
    }
  }
  return (
    <div>
      <JsCodeMirrorEditor ref={editorRef} functionList={functionList} />
      <div>
        <button onClick={handleInsert}>插入变量</button>
        <div>
          {functionList.map((func) => (
            <div key={func} onClick={() => handleInsertFunction(func)}>
              {func}()
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default React.memo(Base)
