import React, {
  FC,
  useRef,
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useMemo,
} from 'react'
import { useSpring, animated } from '@react-spring/web'
import { useSize } from 'ahooks'
import classNames from 'classnames'
import './index.scss'
import InsetShadow from '../InsetShadow'

export interface ICarouselProps {
  /**
   * 单位元素宽度，如果设定了该值，则根据每页显示的个数自动计算下一页的位置
   */
  unitWidth?: number | string
  children: React.ReactNode | React.ReactNode[]
  className?: string
  style?: React.CSSProperties
  disableBtn?: boolean
  disableShadow?: boolean
  isAdaptation?: boolean
  // 隐藏时去除容器高度
  hiddenHeight?: boolean
  leftBtnStyle?: React.CSSProperties
  rightBtnStyle?: React.CSSProperties
}
export interface ICarouselRef {
  next: () => void
  back: () => void
  go: (index: number) => void
}

const preCls = 'tita-ui-carousel'

export const Carousel = React.memo(
  forwardRef<ICarouselRef, ICarouselProps>(
    (
      {
        unitWidth,
        className,
        style,
        children,
        disableBtn,
        disableShadow,
        isAdaptation = false,
        hiddenHeight = false,
        leftBtnStyle,
        rightBtnStyle
      },
      ref
    ) => {
      const _children = (
        Array.isArray(children) ? children : [children]
      ).filter(Boolean)

      const contentRef = useRef() as React.MutableRefObject<HTMLDivElement>
      const contentRefSize = useSize(contentRef)
      const contentWidth = (contentRefSize && contentRefSize.width) || 0
      // @ts-ignore
      const _unitWidth = parseInt(unitWidth || contentWidth)
      // @ts-ignore
      const _buttonWidth = parseInt((_unitWidth / 240) * 36)
      // @ts-ignore
      const _buttonSize = parseInt((_unitWidth / 240) * 16)

      const totalPages = React.Children.count(_children)

      const [startIndex, setStartIndex] = useState(0)
      const [endIndex, setEndIndex] = useState(0)
      const [pageSize, setPageSize] = useState(0)
      const [{ x }, api] = useSpring(() => ({ x: 0 }))

      const { count } = useMemo(() => {
        return {
          count: hiddenHeight ? 0 : 1,
        }
      }, [hiddenHeight])

      useImperativeHandle(ref, () => ({
        next: toNext,
        back: toPre,
        go: (index: number) => {
          setStartIndex(index)
        },
      }))

      useEffect(() => {
        if (contentWidth) api.start({ x: -(_unitWidth * startIndex) })
      }, [startIndex, _unitWidth])

      useEffect(() => {
        const endIndex =
          startIndex + (Math.floor(contentWidth / _unitWidth) - 1)
        setEndIndex(endIndex)
        setPageSize(endIndex - startIndex + 1)
      }, [startIndex, contentWidth, _unitWidth])

      const toNext = () => {
        setStartIndex(startIndex + pageSize)
      }

      const toPre = () => {
        let newStartIndex = startIndex - pageSize
        setStartIndex(newStartIndex < 0 ? 0 : newStartIndex)
      }

      return (
        <div className={`${preCls} ${className}`} style={style}>
          {!disableBtn && (
            <div
              className={classNames(`${preCls}__left-btn`, {
                [`${preCls}__show`]: startIndex > 0,
              })}
              style={
                isAdaptation
                  ? {
                      width: _buttonWidth,
                      height: _buttonWidth,
                      fontSize: _buttonSize > 12 ? _buttonSize : 12,
                      ...leftBtnStyle
                    }
                  : {...leftBtnStyle}
              }
              onClick={toPre}
            >
              <i className='tu-icon-left' />
            </div>
          )}
          {!disableBtn && (
            <div
              className={classNames(`${preCls}__right-btn`, {
                [`${preCls}__show`]: endIndex < totalPages - 1,
              })}
              style={
                isAdaptation
                  ? {
                      width: _buttonWidth,
                      height: _buttonWidth,
                      fontSize: _buttonSize > 12 ? _buttonSize : 12,
                      ...rightBtnStyle
                    }
                  : {...rightBtnStyle}
              }
              onClick={toNext}
            >
              <i className='tu-icon-APP-xi' />
            </div>
          )}

          {!disableShadow && (
            <InsetShadow
              zIndex={2}
              left={startIndex > 0}
              right={endIndex < totalPages - 1}
            />
          )}

          <div ref={contentRef} className={`${preCls}__content`}>
            <animated.div
              style={{ x }}
              className={`${preCls}__children-container`}
            >
              {React.Children.map(_children, (childItem, i) => (
                <div
                  key={i}
                  style={{ width: _unitWidth }}
                  className={classNames(`${preCls}__children-item`, {
                    [`${preCls}__children-item--hidden`]:
                      i < startIndex || i > endIndex + count || !_unitWidth,
                    [`${preCls}__children-item--hidden--height`]:
                      (i < startIndex || i > endIndex + count) && hiddenHeight,
                  })}
                >
                  {childItem}
                </div>
              ))}
            </animated.div>
          </div>
        </div>
      )
    }
  )
)

export default Carousel
