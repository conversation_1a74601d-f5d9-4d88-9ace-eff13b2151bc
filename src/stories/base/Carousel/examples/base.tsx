import React, { FC, useCallback, useRef, } from 'react'
import Carousel, { ICarouselRef } from '@/stories/base/Carousel'

export interface IBaseProps {
  /**
   * 单位宽度
   * @default 270
   */
  unitWidth: number
}

const Base: FC<IBaseProps> = ({ unitWidth }) => {

  const carouselRef = useRef<ICarouselRef>(null)
  const goHandle = useCallback(()=>{
    carouselRef.current?.go(5)
  }, [carouselRef])

  return (
    <Carousel unitWidth={parseInt(unitWidth)} ref={carouselRef} >
      <div style={{ height: 100, backgroundColor: 'brown' }}>1</div>
      <div style={{ height: 100, backgroundColor: 'beige' }} onClick={goHandle}>2</div>
      <div style={{ height: 100, backgroundColor: 'darkgreen' }}>3</div>
      <div style={{ height: 100, backgroundColor: 'deepskyblue' }}>4</div>
      <div style={{ height: 100, backgroundColor: 'lightgoldenrodyellow' }}>5</div>
      <div style={{ height: 100, backgroundColor: 'mediumpurple' }}>6</div>
      <div style={{ height: 100, backgroundColor: 'red' }} >7</div>
    </Carousel>
  )
}

export default React.memo(Base)
