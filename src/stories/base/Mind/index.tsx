// @ts-nocheck
import React, { FC, useEffect, useState } from 'react'
import classNames from 'classnames'
import { But<PERSON> } from 'antd'
import SimpleMind from 'react-simple-mind'
import 'react-simple-mind/dist/style.css';
import './index.scss'

export interface IMindProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-mind'

const testData = {
  title: 'root',
  children: [
    {
      title: 'xxx',
      children: [
        {
          title: <p>xxx <Button>hello</Button></p>
        },
        {
          title: 'xxx1'
        },
        {
          title: 'xxx3'
        },
      ]
    },
    {
      title: 'xxx1',
      children: [
        {
          title: 'xxx'
        },
        {
          title: <p>xxx <Button>hello</Button> <Button>hello</Button><Button>hello</Button><Button>hello</Button></p>
        },
        {
          title: 'xxx3'
        },
      ]
    },
    {
      title: 'xxx3',
      children: [
        {
          title: 'xxx'
        },
        {
          title: 'xxx1'
        },
      ]
    },
  ]
}

const Mind: FC<IMindProps> = ({ className, style }) => {
  const [minder, setMinder] = useState(null)

  return (
    <div className={classNames(preCls, className)} style={style}>
      <SimpleMind
        elementClassName={'element'}
        data={testData}
        gap={[64, 24]}
        lineWidth={4}
        lineColor={'red'}
      />
    </div>
  )
}

export default React.memo(Mind)
