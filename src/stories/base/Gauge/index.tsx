import React, { FC, useRef, useEffect } from 'react'
import classNames from 'classnames'
import merge from 'lodash/merge'

import * as echarts from 'echarts/core'
import {
  TooltipComponent,
  TooltipComponentOption,
  TitleComponent,
  TitleComponentOption,
} from 'echarts/components'
import { GaugeChart, GaugeSeriesOption } from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
import { LabelLayout } from 'echarts/features'
import { useUpdateEffect } from 'ahooks'
import type { EChartsType } from 'echarts/types/dist/shared'

import './index.scss'
import { getLocale } from '@tita/utils'

interface IData {
  name: string
  value: number
}

export interface IGuageProps {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  width?: number
  height?: number
  option: ECOption
}

echarts.use([
  TitleComponent,
  TooltipComponent,
  <PERSON><PERSON>ge<PERSON><PERSON>,
  <PERSON>vas<PERSON><PERSON><PERSON>,
  LabelLayout,
])

const preCls = 'tita-ui--gauge'

type ECOption = echarts.ComposeOption<
  | GaugeSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
>

export const Guage: FC<IGuageProps> = React.memo(
  ({ className, style, width = 200, height = 240, option = {} }) => {
    const ref = useRef<HTMLElement | null>(null)
    const chartRef = useRef<EChartsType | null>(null)

    useEffect(() => {
      chartRef.current = echarts.init(ref.current as HTMLElement)
      let _option: ECOption = {
        tooltip: {
          show: false,
          trigger: 'item',
        },
        series: {
          type: 'gauge',
          center: ['50%', '40%'],
          startAngle: 180,
          endAngle: 0,
          min: 0,
          max: 100,
          radius: 60,
          itemStyle: {
            color: '#5979FF'
          },
          progress: {
            show: true,
            // 柱的宽度
            width: 12,
            roundCap: true
          },
          axisLine: {
            lineStyle: {
              // 柱的宽度
              width: 12
            },
            roundCap: true
          },
          // 隐藏指针
          pointer: {
            show: false
          },
          // 隐藏分隔线
          splitLine: {
            show: false
          },
          // 隐藏刻度
          axisTick: {
            show: false
          },
          // 隐藏刻度label
          axisLabel: {
            show: false
          },
          // 仪表盘中心内容区
          detail: {
            valueAnimation: true,
            width: '60%',
            lineHeight: 20,
            borderRadius: 8,
            offsetCenter: [0, '-15%'],
            fontSize: '20px',
            fontWeight: 'bolder',
            formatter: '{value} %',
            color: '#2879ff'
          },
          data: [
            {
              value: 0.7,
              name: getLocale('OKR_MyO_Ew_Completionrate')
            }
          ],
        },
      }
      let targetOption = merge(_option, option)
      chartRef.current.setOption(targetOption)
    }, [])

    useEffect(() => {
      const chartSize = () => chartRef.current?.resize()
      window.addEventListener('resize', chartSize)
      return () => {
        window.removeEventListener('resize', chartSize)
      }
    }, [])

    useUpdateEffect(() => {
      chartRef.current?.setOption(option)
    }, [option])

    return (
      <div
        // @ts-ignore
        ref={ref}
        style={{ ...style, width, height }}
        className={classNames(preCls, className)}
      ></div>
    )
  }
)

export default Guage
